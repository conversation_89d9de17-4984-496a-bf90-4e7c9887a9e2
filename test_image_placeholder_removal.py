#!/usr/bin/env python3
"""
Test script to verify image placeholder removal fix
"""

import sys
import os
from pathlib import Path
import tempfile

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_placeholder_removal_from_requests():
    """Test removing placeholders from content requests"""
    
    print("Testing placeholder removal from requests...")
    print("=" * 50)
    
    # Create a mock sync tool
    class MockSync:
        def remove_placeholders_from_requests(self, content_requests, placeholder_ranges):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.remove_placeholders_from_requests(content_requests, placeholder_ranges)
    
    sync_tool = MockSync()
    
    # Test case with image placeholders
    original_requests = [
        {
            'insertText': {
                'location': {'index': 1},
                'text': 'This is the first paragraph.\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 30},
                'text': '\n[IMAGE_PLACEHOLDER_0]\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 55},
                'text': 'This is text after the image.\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 85},
                'text': 'Another paragraph with [IMAGE_PLACEHOLDER_1] in the middle.\n'
            }
        },
        {
            'updateParagraphStyle': {
                'range': {'startIndex': 1, 'endIndex': 29},
                'paragraphStyle': {'namedStyleType': 'NORMAL_TEXT'},
                'fields': 'namedStyleType'
            }
        }
    ]
    
    # Define placeholder ranges to remove
    placeholder_ranges = [
        (32, 54, '[IMAGE_PLACEHOLDER_0]'),  # First placeholder
        (105, 127, '[IMAGE_PLACEHOLDER_1]')  # Second placeholder
    ]
    
    print("Original requests:")
    for i, req in enumerate(original_requests):
        if 'insertText' in req:
            text = req['insertText']['text']
            index = req['insertText']['location']['index']
            print(f"  {i+1}: Index {index} -> {repr(text)}")
        else:
            print(f"  {i+1}: {list(req.keys())[0]} request")
    
    print(f"\nPlaceholder ranges to remove:")
    for start, end, text in placeholder_ranges:
        print(f"  {start}-{end}: {repr(text)}")
    
    # Remove placeholders
    updated_requests = sync_tool.remove_placeholders_from_requests(original_requests, placeholder_ranges)
    
    print(f"\nUpdated requests:")
    for i, req in enumerate(updated_requests):
        if 'insertText' in req:
            text = req['insertText']['text']
            index = req['insertText']['location']['index']
            print(f"  {i+1}: Index {index} -> {repr(text)}")
        else:
            print(f"  {i+1}: {list(req.keys())[0]} request")
    
    # Verify placeholders are removed
    all_text = ""
    for req in updated_requests:
        if 'insertText' in req:
            all_text += req['insertText']['text']
    
    has_placeholders = 'IMAGE_PLACEHOLDER_' in all_text
    print(f"\nVerification:")
    print(f"  Contains placeholders: {'❌ YES' if has_placeholders else '✅ NO'}")
    print(f"  Total requests: {len(original_requests)} -> {len(updated_requests)}")
    
    return not has_placeholders

def test_image_position_calculation():
    """Test image position calculation with placeholder removal"""
    
    print(f"\n" + "=" * 50)
    print("Testing image position calculation...")
    print("=" * 50)
    
    class MockSync:
        def calculate_image_positions_in_doc(self, content_requests, image_positions):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.calculate_image_positions_in_doc(content_requests, image_positions)
        
        def remove_placeholders_from_requests(self, content_requests, placeholder_ranges):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.remove_placeholders_from_requests(content_requests, placeholder_ranges)
    
    sync_tool = MockSync()
    
    # Simulate content requests with placeholders
    content_requests = [
        {
            'insertText': {
                'location': {'index': 1},
                'text': 'Document title\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 16},
                'text': 'First paragraph before image.\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 46},
                'text': '\n[IMAGE_PLACEHOLDER_0]\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 70},
                'text': 'Text after first image.\n'
            }
        },
        {
            'insertText': {
                'location': {'index': 95},
                'text': 'Another paragraph with [IMAGE_PLACEHOLDER_1] embedded.\n'
            }
        }
    ]
    
    # Simulate image positions
    image_positions = [
        ('test_image1.png', Path('test_image1.png'), 'IMAGE_PLACEHOLDER_0'),
        ('test_image2.jpg', Path('test_image2.jpg'), 'IMAGE_PLACEHOLDER_1')
    ]
    
    print("Content requests:")
    for i, req in enumerate(content_requests):
        if 'insertText' in req:
            text = req['insertText']['text']
            index = req['insertText']['location']['index']
            print(f"  {i+1}: Index {index} -> {repr(text[:50])}...")
    
    print(f"\nImage positions:")
    for name, path, placeholder_id in image_positions:
        print(f"  {name} -> {placeholder_id}")
    
    try:
        # Calculate positions and get updated requests
        image_positions_with_indices, updated_requests = sync_tool.calculate_image_positions_in_doc(
            content_requests, image_positions
        )
        
        print(f"\nCalculated image positions:")
        for name, path, index in image_positions_with_indices:
            print(f"  {name} will be inserted at index {index}")
        
        print(f"\nUpdated requests (placeholders removed):")
        for i, req in enumerate(updated_requests):
            if 'insertText' in req:
                text = req['insertText']['text']
                index = req['insertText']['location']['index']
                print(f"  {i+1}: Index {index} -> {repr(text[:50])}...")
        
        # Verify no placeholders remain
        all_text = ""
        for req in updated_requests:
            if 'insertText' in req:
                all_text += req['insertText']['text']
        
        has_placeholders = 'IMAGE_PLACEHOLDER_' in all_text
        print(f"\nVerification:")
        print(f"  Placeholders removed: {'✅ YES' if not has_placeholders else '❌ NO'}")
        print(f"  Images positioned: {len(image_positions_with_indices)}/{len(image_positions)}")
        
        return not has_placeholders and len(image_positions_with_indices) == len(image_positions)
        
    except Exception as e:
        print(f"Error during calculation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_workflow():
    """Test the complete workflow with a realistic example"""
    
    print(f"\n" + "=" * 50)
    print("Testing complete workflow...")
    print("=" * 50)
    
    # Create test content with images
    test_content = """# Test Document

This is a paragraph before the first image.

![[test_image1.png]]

This is text between images.

![[test_image2.jpg]]

This is the final paragraph."""
    
    print("Test content:")
    for i, line in enumerate(test_content.split('\n'), 1):
        print(f"  {i:2d}: {line}")
    
    # Create temporary test files
    with tempfile.TemporaryDirectory() as temp_dir:
        vault_path = Path(temp_dir) / "test_vault"
        vault_path.mkdir()
        
        # Create test images
        (vault_path / "test_image1.png").write_bytes(b"fake_png_data")
        (vault_path / "test_image2.jpg").write_bytes(b"fake_jpg_data")
        
        # Create test note
        note_path = vault_path / "test_note.md"
        note_path.write_text(test_content, encoding='utf-8')
        
        # Test the workflow
        try:
            class MockSync:
                def process_images_in_markdown(self, content, note_path):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    real_sync.obsidian_path = vault_path
                    return real_sync.process_images_in_markdown(content, note_path)
                
                def find_media_file(self, base_path, image_name):
                    return base_path / image_name
                
                def calculate_image_positions_in_doc(self, content_requests, image_positions):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.calculate_image_positions_in_doc(content_requests, image_positions)
                
                def remove_placeholders_from_requests(self, content_requests, placeholder_ranges):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.remove_placeholders_from_requests(content_requests, placeholder_ranges)
            
            sync_tool = MockSync()
            
            # Process images
            processed_content, image_positions = sync_tool.process_images_in_markdown(test_content, note_path)
            
            print(f"\nProcessed content:")
            for i, line in enumerate(processed_content.split('\n'), 1):
                if 'IMAGE_PLACEHOLDER_' in line:
                    print(f"  {i:2d}: >>> {line.strip()} <<<")
                else:
                    print(f"  {i:2d}: {line}")
            
            print(f"\nFound {len(image_positions)} images:")
            for name, path, placeholder_id in image_positions:
                print(f"  {name} -> {placeholder_id}")
            
            # Simulate content requests (simplified)
            content_requests = [
                {'insertText': {'location': {'index': 1}, 'text': processed_content}}
            ]
            
            # Calculate positions and remove placeholders
            image_positions_with_indices, updated_requests = sync_tool.calculate_image_positions_in_doc(
                content_requests, image_positions
            )
            
            print(f"\nFinal results:")
            print(f"  Images positioned: {len(image_positions_with_indices)}")
            for name, path, index in image_positions_with_indices:
                print(f"    {name} at index {index}")
            
            # Check if placeholders are removed
            final_text = ""
            for req in updated_requests:
                if 'insertText' in req:
                    final_text += req['insertText']['text']
            
            has_placeholders = 'IMAGE_PLACEHOLDER_' in final_text
            print(f"  Placeholders removed: {'✅ YES' if not has_placeholders else '❌ NO'}")
            
            return not has_placeholders
            
        except Exception as e:
            print(f"Error in workflow test: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("Testing Image Placeholder Removal Fix")
    print("=" * 60)
    
    success1 = test_placeholder_removal_from_requests()
    success2 = test_image_position_calculation()
    success3 = test_complete_workflow()
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed!")
        print("Image placeholders should now be properly removed after image insertion.")
    else:
        print("\n💥 Some tests failed!")
        print("There may still be issues with placeholder removal.")
