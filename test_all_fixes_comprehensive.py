#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes implemented in the sync tool
"""

import sys
import os
from pathlib import Path
import tempfile

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len, clean_whitespace_lines
    import markdown
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def create_comprehensive_test_content():
    """Create comprehensive test content with all features"""
    return """# Comprehensive Test Document

This document tests all the fixes implemented in the sync tool.

## 1. Grapheme Cluster Fix (Vietnamese Text)

Nhà tuyển dụng thường yêu cầu ứng viên ở vị trí Senior Software Engineer c<PERSON><PERSON> c<PERSON> khả năng thiết kế hệ thống.

<PERSON><PERSON><PERSON> k<PERSON> tự đặc biệt: <PERSON>, <PERSON>, ở, <PERSON>ng, được

## 2. Whitespace Cleanup Fix

This paragraph has normal spacing.
   
		
    	  
Another paragraph after whitespace-only lines.

## 3. Nested Lists Fix

- Level 1 item 1
  - Level 2 item 1.1
    - Level 3 item 1.1.1
    - Level 3 item 1.1.2
  - Level 2 item 1.2
- Level 1 item 2
  - Level 2 item 2.1

Mixed lists:
1. Ordered item 1
   - Unordered sub item A
   - Unordered sub item B
2. Ordered item 2
   1. Ordered sub item 1
   2. Ordered sub item 2

## 4. Table Processing Fix

| Feature | Status | Priority | Assignee |
|---------|--------|----------|----------|
| Login System | Done | High | John |
| User Dashboard | In Progress | Medium | Jane |
| API Documentation | Not Started | Low | |

Vietnamese table:

| Tên sản phẩm | Giá | Trạng thái |
|--------------|-----|------------|
| Laptop Dell | 15,000,000 VND | Còn hàng |
| iPhone 15 | 25,000,000 VND | Hết hàng |

## 5. Image Placeholder Removal Fix

This paragraph comes before the first image.

![[test_image1.png]]

This text appears between images.

![[test_image2.jpg]]

This is the final paragraph after all images.

## 6. Code Blocks

```python
def hello_world():
    print("Hello, World!")
    return "success"
```

## 7. Mixed Content

Final paragraph with **bold text** and *italic text* to test formatting.
"""

def test_grapheme_cluster_fix():
    """Test grapheme cluster handling for Vietnamese text"""
    print("1. Testing Grapheme Cluster Fix...")
    print("-" * 40)
    
    vietnamese_text = "Nhà tuyển dụng thường yêu cầu ứng viên ở vị trí"
    
    code_point_len = len(vietnamese_text)
    grapheme_cluster_len = grapheme_len(vietnamese_text)
    
    print(f"   Vietnamese text: '{vietnamese_text}'")
    print(f"   Code points (len()): {code_point_len}")
    print(f"   Grapheme clusters: {grapheme_cluster_len}")
    print(f"   Difference: {code_point_len - grapheme_cluster_len}")
    
    # Test specific Vietnamese characters
    test_chars = ['ể', 'ụ', 'ở', 'ứng', 'được']
    print(f"   Special characters:")
    for char in test_chars:
        char_len = len(char)
        char_grapheme = grapheme_len(char)
        print(f"     '{char}': len={char_len}, grapheme={char_grapheme}")
    
    success = grapheme_cluster_len <= code_point_len
    print(f"   ✅ Grapheme cluster fix: {'PASS' if success else 'FAIL'}")
    return success

def test_whitespace_cleanup_fix():
    """Test whitespace cleanup functionality"""
    print("\n2. Testing Whitespace Cleanup Fix...")
    print("-" * 40)
    
    test_content = """Line 1
   
		
    	  
Line 2"""
    
    print(f"   Original content:")
    lines = test_content.split('\n')
    for i, line in enumerate(lines, 1):
        print(f"     {i}: {repr(line)}")
    
    cleaned_content = clean_whitespace_lines(test_content)
    cleaned_lines = cleaned_content.split('\n')
    
    print(f"   Cleaned content:")
    for i, line in enumerate(cleaned_lines, 1):
        print(f"     {i}: {repr(line)}")
    
    # Count whitespace-only lines
    orig_whitespace = sum(1 for line in lines if line.strip() == '' and line != '')
    clean_whitespace = sum(1 for line in cleaned_lines if line.strip() == '' and line != '')
    
    print(f"   Whitespace-only lines: {orig_whitespace} -> {clean_whitespace}")
    
    success = clean_whitespace == 0
    print(f"   ✅ Whitespace cleanup fix: {'PASS' if success else 'FAIL'}")
    return success

def test_nested_lists_fix():
    """Test nested list processing"""
    print("\n3. Testing Nested Lists Fix...")
    print("-" * 40)
    
    class MockSync:
        def parse_list_structure(self, list_lines):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.parse_list_structure(list_lines)
        
        def is_list_item(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.is_list_item(line)
        
        def get_list_indent_level(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_list_indent_level(line)
        
        def get_bullet_preset_for_level(self, level):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_bullet_preset_for_level(level)
    
    sync_tool = MockSync()
    
    test_lines = [
        '- Level 1 item 1',
        '  - Level 2 item 1.1',
        '    - Level 3 item 1.1.1',
        '  - Level 2 item 1.2',
        '- Level 1 item 2'
    ]
    
    print(f"   Test list:")
    for i, line in enumerate(test_lines, 1):
        is_list = sync_tool.is_list_item(line)
        level = sync_tool.get_list_indent_level(line) // 2 if is_list else -1
        print(f"     {i}: {line} (level {level})")
    
    items = sync_tool.parse_list_structure(test_lines)
    
    print(f"   Parsed structure:")
    for i, item in enumerate(items, 1):
        preset = sync_tool.get_bullet_preset_for_level(item['level'])
        print(f"     {i}: '{item['text']}' (level {item['level']}, preset: {preset})")
    
    # Verify different levels have different presets
    presets = [sync_tool.get_bullet_preset_for_level(item['level']) for item in items]
    unique_presets = len(set(presets))
    
    success = len(items) == 5 and unique_presets >= 2
    print(f"   Items parsed: {len(items)}, Unique presets: {unique_presets}")
    print(f"   ✅ Nested lists fix: {'PASS' if success else 'FAIL'}")
    return success

def test_table_processing_fix():
    """Test table processing"""
    print("\n4. Testing Table Processing Fix...")
    print("-" * 40)
    
    class MockSync:
        def extract_table_data(self, table_element):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.extract_table_data(table_element)
        
        def create_simple_table_fallback(self, table_data, start_index):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.create_simple_table_fallback(table_data, start_index)
    
    sync_tool = MockSync()
    
    # Test with Vietnamese table
    table_markdown = """| Tên | Tuổi | Thành phố |
|-----|------|-----------|
| Nguyễn Văn A | 25 | Hà Nội |
| Trần Thị B | 30 | TP.HCM |"""
    
    html = markdown.markdown(table_markdown, extensions=['tables'])
    soup = BeautifulSoup(html, 'html.parser')
    table_element = soup.find('table')
    
    if table_element:
        table_data = sync_tool.extract_table_data(table_element)
        print(f"   Extracted table data ({len(table_data)} rows):")
        for i, row in enumerate(table_data):
            print(f"     Row {i}: {row}")
        
        requests, length = sync_tool.create_simple_table_fallback(table_data, 1)
        
        if requests and 'insertText' in requests[0]:
            table_text = requests[0]['insertText']['text']
            lines = table_text.split('\n')
            print(f"   Formatted table preview:")
            for i, line in enumerate(lines[:5], 1):
                if line.strip():
                    print(f"     {i}: {line}")
        
        success = len(table_data) == 3 and len(requests) == 1
        print(f"   Rows extracted: {len(table_data)}, Requests: {len(requests)}")
        print(f"   ✅ Table processing fix: {'PASS' if success else 'FAIL'}")
        return success
    else:
        print(f"   ❌ No table found in HTML")
        return False

def test_image_placeholder_removal_fix():
    """Test image placeholder removal"""
    print("\n5. Testing Image Placeholder Removal Fix...")
    print("-" * 40)
    
    class MockSync:
        def remove_placeholders_from_requests(self, content_requests, placeholder_ranges):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.remove_placeholders_from_requests(content_requests, placeholder_ranges)
    
    sync_tool = MockSync()
    
    # Test requests with placeholders
    original_requests = [
        {
            'insertText': {
                'location': {'index': 1},
                'text': 'Text before image.\n[IMAGE_PLACEHOLDER_0]\nText after image.\n'
            }
        }
    ]
    
    placeholder_ranges = [
        (20, 42, '[IMAGE_PLACEHOLDER_0]')
    ]
    
    print(f"   Original text: {repr(original_requests[0]['insertText']['text'])}")
    
    updated_requests = sync_tool.remove_placeholders_from_requests(original_requests, placeholder_ranges)
    
    if updated_requests:
        updated_text = updated_requests[0]['insertText']['text']
        print(f"   Updated text: {repr(updated_text)}")
        
        has_placeholders = 'IMAGE_PLACEHOLDER_' in updated_text
        success = not has_placeholders
        print(f"   Contains placeholders: {'NO' if success else 'YES'}")
        print(f"   ✅ Image placeholder removal fix: {'PASS' if success else 'FAIL'}")
        return success
    else:
        print(f"   ❌ No updated requests generated")
        return False

def test_complete_integration():
    """Test complete integration with all fixes"""
    print("\n6. Testing Complete Integration...")
    print("-" * 40)
    
    # Create comprehensive test content
    test_content = create_comprehensive_test_content()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        vault_path = Path(temp_dir) / "test_vault"
        vault_path.mkdir()
        
        # Create test images
        (vault_path / "test_image1.png").write_bytes(b"fake_png_data")
        (vault_path / "test_image2.jpg").write_bytes(b"fake_jpg_data")
        
        # Create test note
        note_path = vault_path / "comprehensive_test.md"
        note_path.write_text(test_content, encoding='utf-8')
        
        try:
            class MockSync:
                def __init__(self):
                    self.obsidian_path = vault_path
                
                def process_images_in_markdown(self, content, note_path):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    real_sync.obsidian_path = vault_path
                    return real_sync.process_images_in_markdown(content, note_path)
                
                def find_media_file(self, base_path, image_name):
                    return base_path / image_name
                
                def convert_markdown_to_docs_requests(self, content, note_path):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.convert_markdown_to_docs_requests(content, note_path)

                def calculate_image_positions_in_doc(self, content_requests, image_positions):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.calculate_image_positions_in_doc(content_requests, image_positions)
            
            sync_tool = MockSync()
            
            print(f"   Processing comprehensive test content...")
            print(f"   Content length: {len(test_content)} characters")

            content_lines = test_content.split('\n')
            print(f"   Content lines: {len(content_lines)}")
            
            # Process images
            processed_content, image_positions = sync_tool.process_images_in_markdown(test_content, note_path)
            print(f"   Images found: {len(image_positions)}")
            
            # Convert to requests
            requests = sync_tool.convert_markdown_to_docs_requests(processed_content, note_path)
            print(f"   Requests generated: {len(requests)}")

            # Process image placeholders (this is the key step that was missing!)
            if image_positions:
                image_positions_with_indices, updated_requests = sync_tool.calculate_image_positions_in_doc(
                    requests, image_positions
                )
                print(f"   Image positions calculated: {len(image_positions_with_indices)}")
                # Use updated requests for analysis
                final_requests = updated_requests
            else:
                final_requests = requests

            # Analyze final requests
            insert_requests = [r for r in final_requests if 'insertText' in r]
            style_requests = [r for r in final_requests if 'updateParagraphStyle' in r or 'updateTextStyle' in r]
            bullet_requests = [r for r in final_requests if 'createParagraphBullets' in r]
            table_requests = [r for r in final_requests if 'insertTable' in r]

            print(f"   Insert text requests: {len(insert_requests)}")
            print(f"   Style requests: {len(style_requests)}")
            print(f"   Bullet requests: {len(bullet_requests)}")
            print(f"   Table requests: {len(table_requests)}")

            # Check for remaining placeholders in final requests
            all_text = ""
            for req in insert_requests:
                all_text += req['insertText']['text']

            has_placeholders = 'IMAGE_PLACEHOLDER_' in all_text

            all_text_lines = all_text.split('\n')
            has_whitespace_lines = any(line.strip() == '' and line != '' for line in all_text_lines)
            
            print(f"   Contains image placeholders: {'YES' if has_placeholders else 'NO'}")
            print(f"   Contains whitespace-only lines: {'YES' if has_whitespace_lines else 'NO'}")
            
            success = (
                len(image_positions) >= 2 and  # Found images
                len(requests) > 10 and  # Generated sufficient requests
                len(bullet_requests) > 0 and  # Processed lists
                not has_placeholders and  # No placeholders
                not has_whitespace_lines  # No whitespace-only lines
            )
            
            print(f"   ✅ Complete integration: {'PASS' if success else 'FAIL'}")
            return success
            
        except Exception as e:
            print(f"   ❌ Integration test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("COMPREHENSIVE TEST SUITE FOR ALL FIXES")
    print("=" * 60)
    
    tests = [
        ("Grapheme Cluster Fix", test_grapheme_cluster_fix),
        ("Whitespace Cleanup Fix", test_whitespace_cleanup_fix),
        ("Nested Lists Fix", test_nested_lists_fix),
        ("Table Processing Fix", test_table_processing_fix),
        ("Image Placeholder Removal Fix", test_image_placeholder_removal_fix),
        ("Complete Integration", test_complete_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("All fixes are working correctly and ready for production use.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("Please review the failed tests and fix any issues.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
