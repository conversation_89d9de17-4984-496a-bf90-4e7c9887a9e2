#!/usr/bin/env python3
"""
Test script to verify the sync fix works with the problematic Vietnamese file
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_conversion_only():
    """Test just the markdown conversion without Google API calls"""
    
    # Initialize the sync tool (without credentials for this test)
    try:
        sync_tool = ObsidianToGoogleSync("obsidian", "dummy_credentials.json")
    except:
        # Create a minimal version for testing
        class MockSync:
            def convert_markdown_to_docs_requests(self, content, path):
                # Import the actual method logic here
                import markdown
                from bs4 import BeautifulSoup
                
                try:
                    html = markdown.markdown(content, extensions=['tables', 'fenced_code'])
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    requests = []
                    index = 1
                    
                    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre']):
                        if element.name.startswith('h'):
                            text = element.get_text()
                            text_length = grapheme_len(text)
                            
                            requests.append({
                                'insertText': {
                                    'location': {'index': index},
                                    'text': text + '\n'
                                }
                            })
                            
                            requests.append({
                                'updateParagraphStyle': {
                                    'range': {
                                        'startIndex': index,
                                        'endIndex': index + text_length
                                    },
                                    'paragraphStyle': {
                                        'namedStyleType': f'HEADING_{element.name[1]}'
                                    },
                                    'fields': 'namedStyleType'
                                }
                            })
                            
                            index += text_length + 1
                            
                        elif element.name == 'p':
                            text = element.get_text()
                            if text.strip():
                                requests.append({
                                    'insertText': {
                                        'location': {'index': index},
                                        'text': text + '\n'
                                    }
                                })
                                index += grapheme_len(text) + 1
                    
                    return requests
                    
                except Exception as e:
                    print(f"Error in conversion: {e}")
                    return []
        
        sync_tool = MockSync()
    
    # Read the problematic file
    markdown_file = Path("obsidian/Top 10 câu hỏi phỏng vấn System Design và Microservices.md")
    
    if not markdown_file.exists():
        print(f"File not found: {markdown_file}")
        return False
    
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Testing markdown conversion with grapheme-aware indexing...")
    print("=" * 60)
    
    # Convert to requests
    requests = sync_tool.convert_markdown_to_docs_requests(content, markdown_file)
    
    print(f"Generated {len(requests)} requests")
    print()
    
    # Analyze the requests for potential issues
    insert_requests = [r for r in requests if 'insertText' in r]
    print(f"Insert text requests: {len(insert_requests)}")
    
    # Check the first few requests
    print("\nFirst 5 insert requests:")
    for i, req in enumerate(insert_requests[:5]):
        location = req['insertText']['location']
        text = req['insertText']['text']
        index = location['index']
        text_preview = text.replace('\n', '\\n')[:50]
        
        print(f"  {i+1}. Index {index}: '{text_preview}...'")
        print(f"      Text length (len): {len(text)}")
        print(f"      Text length (grapheme): {grapheme_len(text)}")
    
    # Check for any problematic indices
    problematic = []
    for i, req in enumerate(insert_requests):
        index = req['insertText']['location']['index']
        if index < 1:
            problematic.append((i, index, "Index less than 1"))
    
    if problematic:
        print(f"\nFound {len(problematic)} potentially problematic requests:")
        for i, index, issue in problematic:
            print(f"  Request {i}: Index {index} - {issue}")
    else:
        print("\nNo obviously problematic requests found")
    
    print(f"\nConversion test completed successfully!")
    return True

if __name__ == "__main__":
    print("Testing sync fix for Vietnamese text grapheme cluster issue")
    print("=" * 70)
    print()
    
    success = test_conversion_only()
    
    if success:
        print("\n✅ Test passed! The conversion logic appears to be working correctly.")
        print("The grapheme-aware indexing should resolve the Google Docs API error.")
    else:
        print("\n❌ Test failed! There may still be issues with the conversion logic.")
