#!/usr/bin/env python3
"""
Test script to verify complete nested list fix
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_list_extraction():
    """Test extracting list blocks from markdown"""
    
    print("Testing list block extraction...")
    print("=" * 50)
    
    # Create a mock sync tool
    class MockSync:
        def extract_list_blocks_from_markdown(self, content):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.extract_list_blocks_from_markdown(content)
        
        def is_list_item(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.is_list_item(line)
        
        def get_list_indent_level(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_list_indent_level(line)
    
    sync_tool = MockSync()
    
    test_content = """# Header

Some paragraph text.

- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2
  - Nested item 2.1

Another paragraph.

1. Ordered item 1
   - Mixed nested A
   - Mixed nested B
2. Ordered item 2

Final paragraph."""
    
    print("Original content:")
    for i, line in enumerate(test_content.split('\n'), 1):
        print(f"  {i:2d}: {line}")
    
    list_blocks, non_list_content = sync_tool.extract_list_blocks_from_markdown(test_content)
    
    print(f"\nExtracted {len(list_blocks)} list blocks:")
    for i, block in enumerate(list_blocks):
        print(f"  Block {i}:")
        for j, line in enumerate(block):
            print(f"    {j+1:2d}: {repr(line)}")
    
    print(f"\nNon-list content:")
    for i, line in enumerate(non_list_content.split('\n'), 1):
        print(f"  {i:2d}: {line}")

def test_list_structure_parsing():
    """Test parsing list structure"""
    
    print(f"\n" + "=" * 50)
    print("Testing list structure parsing...")
    print("=" * 50)
    
    class MockSync:
        def parse_list_structure(self, list_lines):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.parse_list_structure(list_lines)
        
        def is_list_item(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.is_list_item(line)
        
        def get_list_indent_level(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_list_indent_level(line)
    
    sync_tool = MockSync()
    
    test_cases = [
        {
            'name': 'Simple nested list',
            'lines': [
                '- Item 1',
                '  - Nested item 1.1',
                '  - Nested item 1.2',
                '- Item 2'
            ]
        },
        {
            'name': 'Deep nesting',
            'lines': [
                '- Level 1',
                '  - Level 2',
                '    - Level 3',
                '      - Level 4',
                '    - Level 3 again',
                '  - Level 2 again'
            ]
        },
        {
            'name': 'Mixed ordered/unordered',
            'lines': [
                '1. First item',
                '   - Sub item A',
                '   - Sub item B',
                '2. Second item',
                '   1. Sub item 1',
                '   2. Sub item 2'
            ]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{test_case['name']}:")
        print("-" * 30)
        
        items = sync_tool.parse_list_structure(test_case['lines'])
        
        print("Parsed structure:")
        for i, item in enumerate(items):
            indent = "  " * item['level']
            print(f"  {i+1}: {indent}'{item['text']}' (level {item['level']}, indent {item['indent']})")

def test_complete_list_processing():
    """Test complete list processing workflow"""
    
    print(f"\n" + "=" * 50)
    print("Testing complete list processing...")
    print("=" * 50)
    
    class MockSync:
        def extract_list_blocks_from_markdown(self, content):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.extract_list_blocks_from_markdown(content)
        
        def process_markdown_list_block(self, list_lines, start_index):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.process_markdown_list_block(list_lines, start_index)
        
        def parse_list_structure(self, list_lines):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.parse_list_structure(list_lines)
        
        def get_bullet_preset_for_level(self, level):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_bullet_preset_for_level(level)
        
        def is_list_item(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.is_list_item(line)
        
        def get_list_indent_level(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_list_indent_level(line)
    
    sync_tool = MockSync()
    
    test_content = """# Test Document

- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2
  - Nested item 2.1
    - Deep nested 2.1.1

Final paragraph."""
    
    print("Test content:")
    for i, line in enumerate(test_content.split('\n'), 1):
        print(f"  {i:2d}: {line}")
    
    # Extract list blocks
    list_blocks, non_list_content = sync_tool.extract_list_blocks_from_markdown(test_content)
    
    print(f"\nFound {len(list_blocks)} list blocks")
    
    # Process each list block
    for i, list_block in enumerate(list_blocks):
        print(f"\nProcessing list block {i}:")
        for j, line in enumerate(list_block):
            if line.strip():
                print(f"  {j+1}: {line}")
        
        try:
            requests, total_length = sync_tool.process_markdown_list_block(list_block, 1)
            
            print(f"Generated {len(requests)} requests, total length: {total_length}")
            
            insert_requests = [r for r in requests if 'insertText' in r]
            bullet_requests = [r for r in requests if 'createParagraphBullets' in r]
            
            print("Insert requests:")
            for j, req in enumerate(insert_requests):
                text = req['insertText']['text'].strip()
                index = req['insertText']['location']['index']
                print(f"  {j+1}: Index {index} -> '{text}'")
            
            print("Bullet requests:")
            for j, req in enumerate(bullet_requests):
                preset = req['createParagraphBullets']['bulletPreset']
                range_info = req['createParagraphBullets']['range']
                print(f"  {j+1}: {preset} for range {range_info['startIndex']}-{range_info['endIndex']}")
            
        except Exception as e:
            print(f"Error processing list block: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

if __name__ == "__main__":
    print("Testing Complete Nested List Fix")
    print("=" * 60)
    
    test_list_extraction()
    test_list_structure_parsing()
    success = test_complete_list_processing()
    
    if success:
        print("\n🎉 Complete nested list fix is working!")
        print("Lists should now be processed with proper nesting levels and bullet styles.")
    else:
        print("\n💥 There are still issues with the nested list fix.")
        print("Check the error messages above for details.")
