#!/usr/bin/env python3
"""
Test script to verify the grapheme cluster fix for Google Docs API
"""

import grapheme

def test_grapheme_length():
    """Test grapheme length calculation with Vietnamese text"""
    
    # Test cases with Vietnamese text that caused the original error
    test_cases = [
        "Nhà tuyển dụng thường yêu cầu ứng viên ở vị trí",
        "đượ<PERSON>", 
        "ở",
        "<PERSON>ng", 
        "định lý CAP",
        "Bạn có thể tham khảo video này",
        "👇",  # Emoji that might be multi-byte
        "🔼",  # Another emoji
        "🙏",  # Another emoji
    ]
    
    print("Testing grapheme length calculation:")
    print("=" * 50)
    
    for text in test_cases:
        code_point_len = len(text)
        grapheme_len = grapheme.length(text)
        
        print(f"Text: '{text}'")
        print(f"  Code points (len()): {code_point_len}")
        print(f"  Grapheme clusters:   {grapheme_len}")
        print(f"  Difference:          {code_point_len - grapheme_len}")
        print()
    
    # Test the specific text from the error
    problematic_text = "<PERSON>hà tuyển dụng thường yêu cầu <PERSON>ng viên ở vị trí Senior Software Engineer (Backend) cần giải quyết được một số vấn đề của hệ thống Microservices và khả năng thiết kế hệ thống. Dưới đây là một số câu hỏi và gợi ý cách trả lời để bạn tham khảo về 2 chủ đề trên."
    
    print("PROBLEMATIC TEXT FROM ERROR:")
    print("=" * 50)
    print(f"Text length (len()): {len(problematic_text)}")
    print(f"Grapheme length:     {grapheme.length(problematic_text)}")
    print(f"Difference:          {len(problematic_text) - grapheme.length(problematic_text)}")

if __name__ == "__main__":
    test_grapheme_length()
