#!/usr/bin/env python3
"""
Test script to debug Google Docs API indexing issues with Vietnamese text
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
    import markdown
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_markdown_conversion():
    """Test the markdown conversion process with the problematic file"""
    
    # Read the problematic markdown file
    markdown_file = Path("obsidian/Top 10 câu hỏi phỏng vấn System Design và Microservices.md")
    
    if not markdown_file.exists():
        print(f"File not found: {markdown_file}")
        return
    
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("ORIGINAL MARKDOWN CONTENT:")
    print("=" * 50)
    print(f"File: {markdown_file}")
    print(f"Content length (len()): {len(content)}")
    print(f"Content length (grapheme): {grapheme_len(content)}")
    print(f"First 200 chars: {repr(content[:200])}")
    print()
    
    # Convert to HTML like the sync tool does
    html = markdown.markdown(content, extensions=['tables', 'fenced_code'])
    soup = BeautifulSoup(html, 'html.parser')
    
    print("CONVERTED HTML:")
    print("=" * 50)
    print(f"HTML length: {len(html)}")
    print(f"First 300 chars: {repr(html[:300])}")
    print()
    
    # Simulate the request generation process
    print("SIMULATED REQUEST GENERATION:")
    print("=" * 50)
    
    index = 1  # Start after title
    request_count = 0
    
    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre']):
        if element.name.startswith('h'):
            text = element.get_text()
            text_len = len(text)
            text_grapheme_len = grapheme_len(text)
            
            print(f"Request {request_count}: Header '{text[:50]}...'")
            print(f"  Insert at index: {index}")
            print(f"  Text length (len): {text_len}")
            print(f"  Text length (grapheme): {text_grapheme_len}")
            print(f"  Next index: {index + text_grapheme_len + 1}")
            print()
            
            index += text_grapheme_len + 1
            request_count += 1
            
            if request_count >= 5:  # Limit output
                break
        
        elif element.name == 'p':
            text = element.get_text()
            if text.strip():
                text_len = len(text)
                text_grapheme_len = grapheme_len(text)
                
                print(f"Request {request_count}: Paragraph '{text[:50]}...'")
                print(f"  Insert at index: {index}")
                print(f"  Text length (len): {text_len}")
                print(f"  Text length (grapheme): {text_grapheme_len}")
                print(f"  Next index: {index + text_grapheme_len + 1}")
                print()
                
                index += text_grapheme_len + 1
                request_count += 1
                
                if request_count >= 5:  # Limit output
                    break

def test_specific_vietnamese_text():
    """Test the specific text that caused the error"""
    
    # This is the exact text from the error log
    problematic_text = "Nhà tuyển dụng thường yêu cầu ứng viên ở vị trí Senior Software Engineer (Backend) cần giải quyết được một số vấn đề của hệ thống Microservices và khả năng thiết kế hệ thống. Dưới đây là một số câu hỏi và gợi ý cách trả lời để bạn tham khảo về 2 chủ đề trên."
    
    print("PROBLEMATIC TEXT ANALYSIS:")
    print("=" * 50)
    print(f"Text: {problematic_text}")
    print(f"Length (len()): {len(problematic_text)}")
    print(f"Length (grapheme): {grapheme_len(problematic_text)}")
    print()
    
    # Check each character for potential issues
    print("CHARACTER ANALYSIS:")
    print("-" * 30)
    for i, char in enumerate(problematic_text[:20]):  # First 20 chars
        char_len = len(char.encode('utf-8'))
        char_grapheme = grapheme_len(char)
        print(f"Index {i:2d}: '{char}' (UTF-8: {char_len} bytes, grapheme: {char_grapheme})")

if __name__ == "__main__":
    print("Testing Google Docs API indexing with Vietnamese text")
    print("=" * 60)
    print()
    
    test_specific_vietnamese_text()
    print()
    test_markdown_conversion()
