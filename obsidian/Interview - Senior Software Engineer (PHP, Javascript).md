---
relates:
  - "[[<PERSON><PERSON>]]"
  - "[[Interview - Phỏng vấn]]"
---
# 1. <PERSON><PERSON><PERSON><PERSON>uyễn - <PERSON><PERSON> Việt Nam

Chia sẻ cho mọi người một số câu hỏi mình được hỏi cho vị trí **Senior Software Engineer (PHP, Javascript)** gần đây. Vị trí này được tuyển vào để làm việc với <PERSON> 8+.

Sườn bài:

1. Chào hỏi chém gió. Hỏi m biết cty bọn tao chưa, m thấy tương lai ổn k blah blah
2. Time management, tickets management
3. Refactoring skills. Cái này nó cho đọc 1 đoạn code bùi nhùi. Nhiệm vụ là hiểu code và refactor sao cho dễ maintain. Cũng k khó lắm. Có hỏi thêm design pattern.
4. Coding Proficiency ==> failed cái này. Phần này focus on algorithms and data structures
5. System Design and Optimization: thiết kế hệ thống stream multimedia, cách implement caching thông qua một số system như redis/elastic search
6. Version Control Discipline: cách mình sử dụng git. Cái gì cũng biết,trừ rebase k quen xài =))
7. Adaptability: Ability to work effectively with pre-existing codebases and adhere to set constraints. Cái này nó live stream codebase, hỏi mình khi start thì mình làm gì trước.
8. Chào, bye. 1h30p total.

Technical:

- Kinh nghiệm làm việc với các framework bằng Javascript của bạn như thế nào?
- Khi nào nên sử dụng MySQL hoặc MongoDB? Bạn thấy thích làm việc với hệ nào hơn?
- Trong những framework bạn đã làm việc như Yii2, Symfony, Laravel, bạn thấy cái nào tương tác với database tốt và vì sao như vậy? ActiveRecord, DoctrineORM, Eloquent... bạn có suy nghĩ gì về chúng?
- Có bao nhiêu kiểu test? Testing Pyramid là như thế nào? Theo bạn thì developer nên viết test hay là để cho QA Team viết test?
- Kinh nghiệm sử dụng RESTful API ra sao? Đặc điểm nào làm cho RESTful API trở nên nổi bật? Bạn đã ứng dụng những đặc điểm nào của RESTful API vào app đang làm hiện tại? Có phải RESTful API là kiểu API duy nhất và có phải RESTful API chỉ có thể hoạt động duy nhất qua giao thức HTTP không?
- Sự khác nhau chủ yếu của jQuery và các FE framework hiện đại như React, Vue, Angular là gì? (Họ kỳ vọng câu trả lời sẽ là: jQuery tương tác DOM trực tiếp, các món còn lại thì tương tác với virtual DOM)
- Bạn có biết design pattern không? Kể tên một vài cái xem nào.
- Vì bạn có liệt kê singleton như là một design pattern, vậy theo bạn thì tại sao người ta gọi singleton lại là anti-pattern? Điểm mạnh và điểm yếu của singleton là gì?
- Dev thường gặp những lỗi nào liên quan tới bảo mật khi code? Kể tên một vài điểm xem. (SQL Injection là một, nhưng họ kỳ vọng người trả lời sẽ nói về cách Laravel hỗ trợ phòng chống CSRF như thế nào)
- Bạn có kinh nghiệm làm việc với CSS hiện đại như flexbox, grid... này kia hay không?
- OpenShift với AWS ECS có khác gì nhau không? (Câu này do mình kể có một chút kinh nghiệm DevOps nên họ hỏi thêm)
- Khi nào thì nên dùng cấu trúc monolith, khi nào thì microservices? Phân tích điểm tốt và điểm xấu của từng mô hình.

Vì vị trí tuyển vào để làm Laravel (yêu cầu kinh nghiệm Laravel) nên nhấn nhá khen Laravel giúp triển khai app rất nhanh, để làm sản phẩm proof-of-concept v.v... nếu chúng ta tuân theo các convention của framework như đặt tên Model, table trong database đúng chuẩn v.v...
