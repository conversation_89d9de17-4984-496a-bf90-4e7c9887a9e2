- **Apache Iceberg** là một định dạng bảng (table format) mã nguồn mở, hiệ<PERSON> suất cao, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt để quản lý các bảng phân tích dữ liệu lớn (big data analytics tables) trong các hồ dữ liệu (data lakes). <PERSON><PERSON> được phát triển bởi Netflix để xử lý dữ liệu quy mô petabyte và hiện là một dự án của Apache. Mục tiêu chính của Iceberg là mang lại độ tin cậy và sự đơn giản của các bảng SQL truyền thống vào môi trường dữ liệu lớn, cho phép các công cụ xử lý dữ liệu như Apache Spark, Trino, Flink, Presto, Hive và Impala cùng làm việc an toàn trên cùng một bảng dữ liệu.

- Locker Studio - công cụ phân tích và trực quan hóa dữ liệu miễn phí dựa trên nền tảng đám mây của Google: https://lookerstudio.google.com