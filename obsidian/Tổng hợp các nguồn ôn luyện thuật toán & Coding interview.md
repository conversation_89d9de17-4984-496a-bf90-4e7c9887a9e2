**Tổ<PERSON> hợp các nguồn ôn luyện thuật toán & Coding interview đầy đủ nhất - Tổ<PERSON> hợp từ nhiều nguồn (Cập nhật 23/5/2022)**

**1. THUẬT TOÁN & CẤU TRÚC DỮ LIỆU (DSA)**

**SÁCH**

Introduction to Al<PERSON><PERSON><PERSON> (<PERSON>, <PERSON>, <PERSON> and <PERSON>): <PERSON><PERSON><PERSON><PERSON> sách nhập môn dành cho những người muốn học thuật toán. <PERSON>ù hợp với người mới.

Grokking Algorithm (Aditya Bhargava): Học DSA bằng Python. Phù hợp với người mới.

Algorithms in a Nutshell (<PERSON>, <PERSON> and <PERSON>): Học DSA bằng C++, Java, Python. Phù hợp với cả người mới và trình độ khá.

Introduction to Algorithms: A Creative Approach (Udi Manber): <PERSON><PERSON><PERSON> nâng cao dành cho những người đã biết về <PERSON>, muốn tìm hiểu sâu hơn hoặc tìm các ý tưởng <PERSON>i<PERSON>p cận mới.

Cấu trúc dữ liệu và giải thuật (Đh Bách Khoa Hn - Pgs.Ts Nguyễn Đức Nghĩa): Phù hợp với người mới.

**KHÓA HỌC ONLINE**

Algorithm Specialization - by Standford University (Coursera): Bao gồm 4 Khóa học được feedback khá tốt (4.8/5), có cấp chứng chỉ.

https://www.coursera.org/specializations/algorithms

Micro Master Program, specialized in Data Structure & Algorithm - UC San Diego: Chương trình Micro Master dành cho những bạn muốn thu thập credit để học lấy bằng.

https://www.edx.org/.../ucsandiegox-algorithms-and-data...

FSE k03 - Khóa học DSA và Coding interview của Future Software Engineer: Khóa học ngắn hạn ôn luyện thuật toán và Coding interview để phỏng vấn tại Big Tech.

**WEBSITE ÔN TẬP**

Leetcode.com: - https://l.facebook.com/l.php?u=https%3A%2F%2FLeetcode.com%2F%3Ffbclid%3DIwAR1BGBPqLuYn1Oah36jkUTsVAh_X32AKbe-MZFyYFqYrbekATa7WikptCjg&h=AT3z7Q1gjvW33rr6lbXXlohWCS6SJGJHPsYUEZG5mefJvtE4H6977-b0dWnJK4bn6y5l1lbXwQZSOnQhI-7Mf_bq5lgY5DoPz50tsRC0Qo4lpqnDO8g1ZFV7PKne6Tdwggj2cZZc9I0AV4MdZ4CC2lMoyYWt-k8vIPa8aVcT7D2KF16zYKElN40ruO3uiKPXywjcEFPZreyhwU5ZenbqKlJptHdXwg8zg5ju2-W5wwuUE_INmlH2YujWZhR9MQ3K4u9RoAaGr5hP730&__tn__=-UK-R&c%5B0%5D=AT2KF-BSYCO03tRCltDndo3VJnlgTQwJabU6gP6goC9ltmb_V75xEQBb2yC5jkkeVeZVOsJlTEdcDf2BXAF-TvJpKHoGCA95xf-2aAR7BaVbRUk7QKg5OLuORlIDg1xS3VKoqbx_kPh6ct2-p0nRrHFi6t7C6u7G5AM

Một trong những trang web hàng đầu để ôn thuật toán, giải đề nếu bạn có mong muốn apply vào các công ty công nghệ lớn. Một trong những lợi thế của leetcode là bạn có thể tham khảo được những cách giải tối ưu cho một đề bài. Leetcode có hệ thống xếp hạng lời giải hấp dẫn hơn. Bạn cũng có thể tham khảo các dạng bài tập thường xuất hiện trong buổi phỏng vấn Coding của các công ty với Leetcode Premium.

Hackerrank.com: - https://l.facebook.com/l.php?u=http%3A%2F%2FHackerrank.com%2F%3Ffbclid%3DIwAR3j4rIoGjPYEo8FItv6Ivb3owicUkEBT3_ryMF0bHcdThl3eguwsg_bqPM&h=AT0CND21ftlvba3q-B9rtZEcTnbyxLzhh8gMAAFN_SmSlBSbTb7fgpm588duPAhNcFclPcqXzRNtUR-wFU4nFCGo4xEDRS-QqQ6HqoHaPGll8vwXDGBpnjPwNhoxNkDypbgEyztsYxFWa462zCyQ_8zA8G2v2FMofkMUBacBsx_TJwrzG2jz2rCQ68KwyJjJMV19r3raXfhcmpXeh0CNX1cGr1bkf-1YooA-MGbZPC5sA1fUaegBp_mo6ZX4Tj--pOhqqZscCoX7tjI&__tn__=-UK-R&c%5B0%5D=AT2KF-BSYCO03tRCltDndo3VJnlgTQwJabU6gP6goC9ltmb_V75xEQBb2yC5jkkeVeZVOsJlTEdcDf2BXAF-TvJpKHoGCA95xf-2aAR7BaVbRUk7QKg5OLuORlIDg1xS3VKoqbx_kPh6ct2-p0nRrHFi6t7C6u7G5AM

Cũng là một website để luyện DSA tương tự leetcode. Hackerrank gần như miễn phí mọi tính năng. Một số công ty như Expedia, GE, Goldman Sachs gửi câu hỏi coding của họ qua Hackerrank.

**2. PHỎNG VẤN VIẾT CODE (CODING INTERVIEW)**

**SÁCH**

Cracking the Coding Interview - 189 Programming Questions and Solutions (Gayle Laakmann McDowell): Cuốn sách cơ bản để ôn các câu hỏi trong Coding Interview ai cũng nên đọc.

Programming Interviews Exposed (John Mongan, Noah Suojanen Kindler, Eric Giguère): Cuốn sách giới thiệu về programming interviews, có bao gồm các câu hỏi từ cơ bản tới nâng cao.

**WEBSITE MOCK INTERVIEW**

Interview.io - http://interview.io/ - Website giúp bạn luyện interview, bao gồm cả coding interview. Rate cho Coding Interview khoảng $200/h.

Hackpack.xyz - Website giúp bạn ghép gặp và luyện mock interview với các bạn khác.

Server Discord Mock Interview của FSE: Các bạn học viên FSE và các bạn khác có nhu cầu có thể join Server Discord này để luyện tập Mock interview cùng nhau.

https://discord.gg/7SbGJnZn

**KHÓA HỌC ONLINE**

Grokking the Coding Interview: Khóa học 182 bài giảng, 125 challenges dành cho những bạn muốn thử thách bản thân. Giá subscription 10$/tháng.

FSE k03 - Khóa học DSA & Coding interview thực chiến tổ chức bởi Future Software Engineers. Khóa ngắn hạn FSE có lợi thế cả 4 giảng viên đang làm việc tại big tech như Google, Amazon, TikTok, Booking sẽ trực tiếp giảng dạy, review code cho học viên, và trực tiếp tham gia Mock Interview giúp các bạn học viên vững kiến thức, tự tin phỏng vấn chinh phục Big Tech.

Ngoài ra, để tìm hiểu thêm về kinh nghiệm phỏng vấn Coding interview tại FAANG và Big Tech nói chung, các bạn có thể tham khảo FB group Viet Tech. Trong đó có rất nhiều anh chị đang làm tại các công ty lớn và có rất nhiều bài review về interview các vị trí technical.

**Tổng hợp các bài đã chia sẻ trên fanpage FSE**

[Cơ hội việc làm] Job FE, BE, iOS, Android, DevOps, Security tại TikTok Singapore:

**https://www.facebook.com/FSEcourse/posts/143535571584132**

[Bài viết] Giới thiệu về Coding Interview:

**https://www.facebook.com/FSEcourse/posts/132762685994754**

[Bài viết] Mock interview là gì, tại sao lại quan trọng. Cách ôn luyện Mock interview?

**https://www.facebook.com/FSEcourse/posts/137146282223061**

[Bài viết] Tự học thuật toán so với học thuật toán tại FSE có gì khác?

**https://www.facebook.com/FSEcourse/posts/136645505606472**

[Bài viết] Tại sao các công ty công nghệ lớn như Google, Facebook, Amazon ngày càng chú trọng kiến thức thuật toán khi tuyển chọn ứng viên?

**https://www.facebook.com/FSEcourse/posts/128605529743803**

[Video] Bài giảng chủ đề Two-Pointers:

**https://www.facebook.com/watch/?v=669763524315586**

[Tips & Tricks] 7 mẹo tạo ấn tượng tốt khi phỏng vấn Coding Interview:

**https://www.facebook.com/FSEcourse/posts/135304129073943**

[Tips & Tricks] 4 lỗi cần tránh khi tham gia Coding Interview tại các công ty công nghệ:

**https://www.facebook.com/FSEcourse/posts/131218466149176**

Nguồn: Future Software Engineers - https://www.facebook.com/FSEcourse/?__cft__%5B0%5D=AZWm-0gpaZnzTjhoVvtM5fJ9Kzlg7KosxSteGWsoQ7xJIVqy6HdzQtcNYXNA73k7uHWLXV9-0bD0GHyBnZ9qug8DxX8mvqAllQAds7fOxHtVqESsCTKstpmNvobz9I8WutCNm6WjmvEkeD66EbE2dFqb&__tn__=kK-R
