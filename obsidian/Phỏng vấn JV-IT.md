# Giới thiệu bản thân

- Họ và tên
- <PERSON><PERSON> năm kinh nghiệm
- Sở thích công nghệ
- M<PERSON><PERSON> tiêu (digital nomad)

---

# Câu hỏi nên hỏi công ty

## Hỏi trước HR

- Phỏng vấn với ai?
- C<PERSON> mấy bước phỏng vấn?
- Người phỏng vấn gồm những ai?

## Hỏi HR

- Có phải vốn 100% của Nhật không?
- Công ty có tester không?
- Công ty đóng bảo hiểm trên full lương hay là lương cơ bản vùng?
- Hợp đồng có thời hạn hay vô thời hạn?
- Review lương bao lâu một lần?
- Giám đốc MITANI MASASHI có hay ở văn phòng không? Các lãnh đạo có hay lên văn phòng không?
- Teambuilding mấy lần / năm? Có nhất thiết phải đi không?
- C<PERSON><PERSON> ty có tủ lạnh, đồ ăn không?
- <PERSON><PERSON> quy định gì ở văn phòng không?
- Wifi công ty có chặn Twitter, Youtube không?
- Có phải chào cờ, đọc khẩu hiệu gì không?
- Có cam kết ràng buộc gì khi làm hợp đồng không?

## Hỏi leader

- Những dự án mà công ty đã làm có microservice, cache, tối ưu query, design system không?
- Tech stack chính của công ty là gì? Tech stack có dựa vào khách hàng không?
- Scope của em trong dự án là làm gì? Có bao gồm các công việc của devOps hay tester không?
- Leader có cùng code không hay chỉ quản lý dự án thôi?
- Ở vị trí của em thì khi làm việc có làm việc trực tiếp với sếp người Nhật hay khách hàng Nhật không? Tương lai có thể không?
- Trong một khoảng thời gian thì nhân viên có làm song song nhiều dự án không?
- Thời gian họp có thường xuyên không? Công ty có follow theo Scrum Agile hay Waterfall không?
- Có thể đánh giá một chút về CV của em không?
- Quá trình thăng tiến như thế nào?
- Hỏi xem công việc có phải đang cần Laravel, PHP và VueJS không?
- Các buổi workshop thường nói về cái gì?

## Ghi chú

- Người phỏng vấn cười nhiều ⇒ xịt.
- Nhật coi trọng ng gắn bó vs cty lắm á.
- Người Nhật họ thích những người tuân thủ quy định và quy trình.
- Các công ty Nhật có xu hướng tìm người có tố chất (thường là ít kinh nghiệm hoặc sinh viên mới ra trường nhưng phải có đủ tố chất để đào tạo chứ kp tuyển bừa bãi) đào tạo làm nhân viên. Cây mềm thì dễ uốn, nhân viên được đào tạo sẽ thấm nhuần định hướng phát triển và văn hoá làm việc của công ty, tạo một khối đoàn kết cùng phát triển thay vì mạnh thằng nào thằng ấy làm.
- kinh nghiệm PV cty nhật là thằng nào có nguyện vọng học hỏi thêm chuyên ngành khi vào cty là thằng đó tạch.

[[Note câu hỏi phỏng vấn Laravel]]
