---
relates:
  - "[[Machine learning - Deep Learning - AI - ML - DL]]"
  - "[[LLM promt engineering]]"
---
# 1. T<PERSON><PERSON> nguyên học tập

- [[LLM promt engineering]]
- Cách feed knowledge / memory cho LLM: https://viblo.asia/p/memory-in-llm-agent-n1j4lkwMVwl
* Series 18 bài học LLM cho beginner của Microsoft: [https://github.com/microsoft/generative-ai-for-beginners](https://github.com/microsoft/generative-ai-for-beginners) #course #LLM
* Langchain #1 - Điểm qua các chức năng sừng sỏ nhất của Langchain - một framework cực bá đạo khi làm việc với LLM: [https://viblo.asia/p/langchain-1-diem-qua-cac-chuc-nang-sung-so-nhat-cua-langchain-mot-framework-cuc-ba-dao-khi-lam-viec-voi-llm-BQyJKmrqVMe](https://viblo.asia/p/langchain-1-diem-qua-cac-chuc-nang-sung-so-nhat-cua-langchain-mot-framework-cuc-ba-dao-khi-lam-viec-voi-llm-BQyJKmrqVMe) #Langchain
* <a href="obsidian://open?file=C%C3%A1ch%20%C4%91%E1%BA%B7t%20c%C3%A2u%20h%E1%BB%8Fi%20cho%20ChatGPT.md">Cách đặt câu hỏi cho ChatGPT</a>
* Retrieval-Augmented Generation: Phương pháp không thể thiếu khi triển khai các dự án LLM trong thực tế!: [https://viblo.asia/p/retrieval-augmented-generation-phuong-phap-khong-the-thieu-khi-trien-khai-cac-du-an-llm-trong-thuc-te-phan-1-Ny0VG7yzVPA](https://viblo.asia/p/retrieval-augmented-generation-phuong-phap-khong-the-thieu-khi-trien-khai-cac-du-an-llm-trong-thuc-te-phan-1-Ny0VG7yzVPA) #RAG
* Paper Explain - Mixtral of Experts: Lắm thầy thì model khỏe: [https://viblo.asia/p/paper-explain-mixtral-of-experts-lam-thay-thi-model-khoe-EvbLbQ8oJnk](https://viblo.asia/p/paper-explain-mixtral-of-experts-lam-thay-thi-model-khoe-EvbLbQ8oJnk) #Mixtral
* Retrieval-Augmented Generation: Phương pháp không thể thiếu khi triển khai các dự án LLM trong thực tế! (Phần 1): [https://viblo.asia/p/retrieval-augmented-generation-phuong-phap-khong-the-thieu-khi-trien-khai-cac-du-an-llm-trong-thuc-te-phan-1-Ny0VG7yzVPA](https://viblo.asia/p/retrieval-augmented-generation-phuong-phap-khong-the-thieu-khi-trien-khai-cac-du-an-llm-trong-thuc-te-phan-1-Ny0VG7yzVPA) #RAG
* Quy trình xây dựng hệ thống RAG tích hợp Function Calling: [https://viblo.asia/p/quy-trinh-xay-dung-he-thong-rag-tich-hop-function-calling-with-source-code-vlZL98GZJQK](https://viblo.asia/p/quy-trinh-xay-dung-he-thong-rag-tich-hop-function-calling-with-source-code-vlZL98GZJQK) #RAG #functionCalling
* Scrapegraph-ai #1: Sử dụng sức mạnh của LLMs để giải quyết bài toán thu thập và xử lý dữ liệu cho các hệ thống AI: [https://viblo.asia/p/scrapegraph-ai-1-su-dung-suc-manh-cua-llms-de-giai-quyet-bai-toan-thu-thap-va-xu-ly-du-lieu-cho-cac-he-thong-ai-EvbLbavPJnk](https://viblo.asia/p/scrapegraph-ai-1-su-dung-suc-manh-cua-llms-de-giai-quyet-bai-toan-thu-thap-va-xu-ly-du-lieu-cho-cac-he-thong-ai-EvbLbavPJnk) #scrapegraph
* Các kỹ thuật tạo và sử dụng các prompt (lời nhắc) trong LLM models: [https://trigaten.github.io/Prompt_Survey_Site](https://trigaten.github.io/Prompt_Survey_Site) #promptEngineering
* Bạn đã biết gì về prompt engineering?: [https://viblo.asia/s/ban-da-biet-gi-ve-prompt-engineering-EbNVQNO24vR](https://viblo.asia/s/ban-da-biet-gi-ve-prompt-engineering-EbNVQNO24vR) #promptEngineering
* Bài 2 - Prompt Engineering, RAG và Finetuning
* Dùng operator với Deepseek API + WebUI: [https://www.facebook.com/Passer.maker/videos/601578342493962/?idorvanity=3262256010653927](https://www.facebook.com/Passer.maker/videos/601578342493962/?idorvanity=3262256010653927) #Deepseek #API
* [Hướng dẫn Finetune Mô hình LLM với Unsloth](https://viblo.asia/p/huong-dan-finetune-mo-hinh-llm-don-gian-va-mien-phi-voi-unsloth-0gdJzRvAJz5)
    * Hướng dẫn chi tiết cách tinh chỉnh mô hình LLM để tạo chatbot sinh workflow n8n.
    * Sử dụng thư viện Unsloth và Google Colab để finetune miễn phí.
    * Dữ liệu được lưu trữ trên Hugging Face; sử dụng định dạng prompt kiểu Alpaca. #finetune #LLM #Unsloth
* [6 Chiến Lược Prompt Hiệu Quả của OpenAI – Phần 1](https://viblo.asia/p/6-chien-luoc-de-prompt-hieu-qua-ma-openai-dua-ra-co-gi-hot-phan-1-E1XVOvkZLMz)
    * Viết hướng dẫn rõ ràng và chi tiết.
    * Yêu cầu mô hình nhận vai trò cụ thể (ví dụ: giáo viên, chuyên gia).
    * Sử dụng dấu phân cách để phân biệt các phần trong prompt.
    * Xác định các bước cụ thể để thực hiện yêu cầu.
    * Cung cấp ví dụ minh họa.
    * Chỉ định định dạng hoặc đặc điểm mong muốn của output. #promptEngineering #OpenAI
* [6 Chiến Lược Prompt Hiệu Quả của OpenAI – Phần 2](https://viblo.asia/p/6-chien-luoc-de-prompt-hieu-qua-ma-openai-dua-ra-co-gi-hot-phan-2-obA46OjBJKv)
    * Hướng dẫn mô hình suy luận từng bước trước khi đưa ra kết luận.
    * Sử dụng "Độc thoại Nội tâm" để tách biệt quá trình suy luận khỏi kết quả hiển thị.
    * Áp dụng "Chuỗi Truy vấn" để chia nhỏ nhiệm vụ thành các bước riêng biệt. #promptEngineering #OpenAI

# 2. Libraries / Frameworks

* OpenCopilot: [https://github.com/openchatai/OpenCopilot](https://github.com/openchatai/OpenCopilot) #OpenCopilot
* [https://aistudio.google.com](https://aistudio.google.com) #GoogleAI
* CoreNet: A library for training deep neural networks: [https://github.com/apple/corenet](https://github.com/apple/corenet) #CoreNet
* Triton language - a language and compiler for writing highly efficient custom Deep-Learning primitives: [https://github.com/triton-lang/triton](https://github.com/triton-lang/triton) #Triton
* [MLX Server – API tương thích OpenAI](https://github.com/cubist38/mlx-server-OAI-compat)
    * Máy chủ API hiệu suất cao cho mô hình MLX, tương thích với OpenAI.
    * Phát triển bằng Python và FastAPI, hỗ trợ chạy mô hình ngôn ngữ và thị giác trên MacOS M-series. #MLX #API #OpenAI
* [LangSmith – Giám sát và Đánh giá Ứng dụng AI](https://www.langchain.com/langsmith)
    * Nền tảng giúp debug, test và giám sát hiệu suất ứng dụng AI.
    * Hỗ trợ tracing, đánh giá với LLM-as-Judge, thu thập phản hồi từ người dùng.
    * Không yêu cầu sử dụng LangChain; hỗ trợ Python, TypeScript và OpenTelemetry. #LangSmith #debug #test
* [LiteLLM – Giao diện Thống nhất cho 100+ Mô hình LLM](https://docs.litellm.ai/docs/)
    * Cho phép gọi hơn 100 mô hình LLM với định dạng OpenAI.
    * Hỗ trợ retry, fallback, theo dõi chi phí và thiết lập ngân sách theo dự án.
    * Cung cấp Proxy Server và Python SDK để tích hợp linh hoạt. #LiteLLM #API #LLM
- [OpenUI – Thiết kế Giao diện Người Dùng bằng Trí Tưởng Tượng](https://github.com/wandb/openui?tab=readme-ov-file)
	* Công cụ cho phép mô tả UI bằng ngôn ngữ tự nhiên và xem kết quả trực tiếp.
	* Hỗ trợ chuyển đổi HTML sang React, Svelte, Web Components, v.v.
	* Phù hợp cho việc thử nghiệm và tạo nguyên mẫu ứng dụng sử dụng LLM. #OpenUI #UI #design
- LangGraph – Xây dựng Ứng dụng LLM Stateful với Đồ thị:
	- Một thư viện được xây dựng dựa trên LangChain, chuyên dùng để tạo các ứng dụng LLM có trạng thái (stateful) và nhiều tác nhân (multi-actor).
	- Cho phép định nghĩa luồng xử lý (workflow) dưới dạng đồ thị (graph), nơi các "node" có thể là LLM call, tool use, hoặc bất kỳ logic tùy chỉnh nào.
	- Hỗ trợ quản lý trạng thái giữa các bước trong luồng, giúp xây dựng các tác nhân (agents) phức tạp có khả năng ra quyết định và lặp lại.
	- Lý tưởng cho các ứng dụng cần chuỗi hành động phức tạp, ra quyết định dựa trên kết quả trước đó, hoặc phối hợp nhiều mô hình/công cụ.
	- Khái niệm cốt lõi là định nghĩa tính toán dưới dạng đồ thị:
		- Các node: Đại diện cho các bước xử lý (gọi LLM, sử dụng công cụ, logic tùy chỉnh).
		- Các cạnh (edges): Định nghĩa chuyển đổi giữa các node. Có thể là chuyển đổi cố định hoặc có điều kiện.
	- Trạng thái (state) được truyền qua lại giữa các node và có thể thay đổi (mutable).
	- Hỗ trợ các chu trình (cycles) trong đồ thị, rất quan trọng cho hành vi của tác nhân (lập kế hoạch, hành động, quan sát, lặp lại).
	- Các thành phần chính:
		- `StateGraph`: Định nghĩa cấu trúc trạng thái, các node và cạnh.
		- Nodes: Các hàm hoặc runnables thực hiện thao tác trên trạng thái.
		- Edges: Định nghĩa cách chuyển từ node này sang node khác.
		- State: Mô hình dữ liệu (thường là Pydantic) lưu trữ thông tin.
	- Trường hợp sử dụng phổ biến:
		- Xây dựng các tác nhân (agents) phức tạp.
		- Hệ thống nhiều tác nhân phối hợp.
		- Các luồng xử lý phức tạp cần quản lý trạng thái.
		- Chatbot có bộ nhớ và khả năng ra quyết định.
	- Ưu điểm:
		- Quản lý trạng thái rõ ràng, tường minh.
		- Trực quan hóa luồng xử lý dễ dàng.
		- Xử lý được luồng điều khiển phức tạp (vòng lặp, phân nhánh).
		- Tận dụng hệ sinh thái LangChain.
	- Tài liệu:
		- https://viblo.asia/s/hanh-trinh-kham-pha-langgraph-muon-hero-ban-phai-bat-dau-tu-zero-vlZL9lMdJQK
	- #LangGraph #LLM #framework

## 2.1. Make It Heavy

[https://github.com/Doriandarko/make-it-heavy](https://github.com/Doriandarko/make-it-heavy)

### 2.1.1. Chức năng nổi bật

- **Mô phỏng Grok Heavy**: Hệ thống multi-agent tái hiện chế độ phân tích sâu, đa chiều như Grok heavy.
- **Song song nhiều agent**: Triển khai đồng thời 4 (hoặc hơn) agent chuyên biệt để đảm bảo độ bao phủ thông tin và quan điểm.
- **Tạo câu hỏi động**: AI tự động tạo 4 câu hỏi nghiên cứu chuyên sâu từ truy vấn người dùng nhằm đảm bảo từng khía cạnh được khai triển độc lập.
- **Hiển thị trạng thái thực thi**: Giao diện console hiển thị tiến độ xử lý theo từng agent theo thời gian thực.
- **Tích hợp công cụ linh hoạt**: Hệ thống tool tự động phát hiện các thư viện, hàm công cụ được thêm vào thư mục `tools/`, cho phép "cắm nóng".
- **Tổng hợp thông minh**: Tích hợp kết quả từ nhiều góc nhìn của các agent thành một câu trả lời thống nhất, sâu sắc.
- **Chế độ đơn agent**: Có thể chạy ở chế độ một agent duy nhất (cho bài toán đơn giản hơn).

### 2.1.2. Thành phần chính

|Thành phần|Chức năng|
|---|---|
|agent.py|Triển khai agent độc lập, tích hợp tool, vòng lặp tác vụ tự hoàn chỉnh|
|orchestrator.py|Sinh câu hỏi chuyên biệt, điều phối agent song song, tổng hợp & recovery|
|tools/|Hệ thống phát hiện, gắn, sử dụng tool động, interface thống nhất|
|config.yaml|Tùy chỉnh API, model, max agent, timeout, prompt sinh câu hỏi/tổng hợp|

### 2.1.3. Tool tích hợp sẵn

|Tool|Nhiệm vụ|Tham số chính|
|---|---|---|
|search_web|Tìm kiếm web DuckDuckGo|query, max_results|
|calculate|Tính toán an toàn|expression|
|read_file|Đọc file|path, head, tail|
|write_file|Ghi/ghi đè file|path, content|
|mark_task_complete|Đánh dấu hoàn thành tác vụ|task_summary, completion_message|

### 2.1.4. Tích hợp AI Model

- Hỗ trợ chọn model OpenRouter (Claude, GPT-4.1, Gemini, Llama v.v.)
- Tùy chỉnh số lượng agent song song (cấu hình orchestrator)
- Có thể thêm tool nhanh chỉ qua thêm file Python kế thừa `BaseTool`

### 2.1.5. Cài đặt & Sử dụng cơ bản

- Yêu cầu Python 3.8+, package manager `uv`, API key OpenRouter
- Khởi chạy chế độ 1 agent: `uv run main.py`
- Khởi chạy Grok heavy (multi-agent): `uv run make_it_heavy.py`
- Tùy chỉnh bot, tool, config trong `config.yaml`

### 2.1.6. Ưu và nhược điểm

#### 2.1.6.1. Ưu điểm

- **Phân tích đa chiều, chuyên sâu**: Mỗi agent tiếp cận một góc độ → tổng hợp sâu, giảm thiếu sót thông tin.
- **Tự động hóa workflow**: Không tốn công sinh câu hỏi, điều phối, chỉ việc nhập truy vấn.
- **Mở rộng công cụ dễ dàng**: Chỉ cần thêm file tool mới vào đúng thư mục.
- **Dễ cấu hình**: Tùy chỉnh thông qua file YAML, hỗ trợ nhiều model khác nhau linh hoạt về hiệu năng, giá thành.
- **Có chế độ đơn giản và nâng cao**: Phù hợp nhiều bài toán, usecase khác nhau.

#### 2.1.6.2. Nhược điểm

- **Phụ thuộc OpenRouter API và quota**: Mỗi agent ngốn request riêng, giới hạn bởi gói dịch vụ.
- **Overhead cho task đơn giản**: Quá trình orchestrator/agent có thể dư thừa nếu bài toán ngắn/gọn.
- **Yêu cầu cấu hình ban đầu (API key, Python env)**, newbie sẽ phải setup nhiều bước.
- **Các tool mặc định chỉ ở mức cơ bản**, nếu muốn cao cấp cần tự phát triển thêm.

#### 2.1.6.3. Các ứng dụng tiềm năng (Usecases)

- **Nghiên cứu tổng hợp**: Yêu cầu phân tích chuyên sâu từ nhiều khía cạnh (ex: ảnh hưởng AI đến lập trình, xu hướng công nghệ...).
- **Tư vấn/chẩn đoán kỹ thuật**: Nhận diện, so sánh, xác thực nhiều phương án/phản biện (tư vấn code, so sánh framework, xác thực dữ liệu...).
- **Kịch bản sáng tạo/phân tích rủi ro**: Lập kế hoạch startup, đánh giá thị trường, phân tích tài chính, risk assessment.
- **Tổng hợp tài liệu lớn**: Chia nhỏ nhiệm vụ theo hướng chuyên môn hóa (giống teamwork), thay thế brainstorming nhóm nhỏ.

## 2.2. CrewAI

https://github.com/crewAIInc/crewAI

**CrewAI** là một framework Python nhẹ, tốc độ cao, dùng để điều phối hệ thống đa agent dựa trên mô hình ngôn ngữ lớn (LLM Multi-Agent Orchestration Framework)[2](https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent).

### 2.2.1. Chức năng chính của CrewAI

- CrewAI tổ chức các **agent** (đại diện cho các vai trò như Researcher, Writer, Critic...) hoạt động phối hợp như một “đội nhóm” (crew) để xử lý các tác vụ (tasks) cụ thể.
- Mỗi agent có vai trò rõ ràng, bối cảnh, mục tiêu, và có thể sử dụng các tool mặc định hoặc riêng phục vụ cho từng task.
- Hỗ trợ tạo các luồng công việc (process) linh hoạt: thực thi nhiệm vụ tuần tự hoặc song song, tương tác trong nhóm agent có hoặc không.
- Điều phối toàn bộ bằng **CrewManager**, quản lý khởi tạo, kích hoạt và xử lý logic phân chia nhiệm vụ và trả về kết quả cuối cùng.
- Cho phép gán tool riêng cho từng agent, hỗ trợ các mô hình LLM như OpenAI GPT, Claude, Mistral, Cohere, và dễ dàng cấu hình qua langchain.llms.
- Cung cấp API đơn giản, dễ hiểu, hỗ trợ tốt cho các workflow AI thực tế dạng Nghiên cứu → Viết → Phản biện → Tổng hợp → Xuất bản[2](https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent).

### 2.2.2. Ưu điểm

- **Phong cách tư duy giống teamwork thực tế**: dễ hình dung, dễ mở rộng các vai trò mới trong hệ thống.
- **Phù hợp workflow AI thực tế đa tác vụ** với các giai đoạn rõ ràng.
- **Hỗ trợ nhiều loại mô hình LLM** và khả năng cấu hình đa dạng công cụ.
- **Giao diện lập trình rõ ràng và đơn giản**, dễ tiếp cận với người mới.
- Cho phép gán công cụ riêng cho từng agent, tăng tính linh hoạt khi làm việc chuyên sâu[2](https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent).

### 2.2.3. Nhược điểm

- **Không hỗ trợ hội thoại trực tiếp giữa các agent** (agents không giao tiếp tương tác song phương như trong một số framework khác như AutoGen).
- **Tasks tuyến tính, không có vòng lặp phản hồi tự động hay multi-round interaction**, tức là các tác vụ chỉ chạy theo một chiều không lặp lại hoặc tự điều chỉnh trong quá trình chạy.
- **Chưa tích hợp bộ nhớ dài hạn hoặc đa vòng trò chuyện mặc định** (long-term memory)[2](https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent).

### 2.2.4. Usecases phù hợp

- Các workflow AI đa tác vụ, cần phân chia rõ ràng vai trò như nghiên cứu, viết, phản biện, tổng hợp và xuất bản nội dung.
- Hệ thống tự động hóa phức hợp, có nhiều bước xử lý liên tiếp hoặc song song, ví dụ:
    - Tạo nội dung tự động với nhiều bước kết hợp xử lý logic và đánh giá.
    - Quản lý dự án AI mà có nhiều chuyên gia ảo đóng vai trò khác nhau.
    - Các hệ thống cần tương tác định hướng nhiệm vụ rõ ràng cho từng agent, ví dụ chatbot đa agent, hệ thống hỗ trợ ra quyết định phức tạp.

CrewAI có thể dùng làm framework độc lập, không phụ thuộc vào LangChain hoặc các framework agent khác, đem lại sự linh hoạt và nhanh chóng trong việc xây dựng multi-agent orchestration dựa trên LLM.
Tóm lại, CrewAI là framework điều phối đa agent tập trung vào workflow teamwork thực tế, dễ mở rộng, hỗ trợ đa mô hình LLM nhưng hạn chế ở tính tương tác đa chiều giữa agent và chưa có vòng lặp nhiệm vụ tự động[2](https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent).

1. [https://github.com/crewAIInc/crewAI](https://github.com/crewAIInc/crewAI)
2. [https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent](https://www.scribd.com/document/868728195/Tim-hi%E1%BB%83u-Multi-Agent-LLM-Multi-Agent)

# 3. LLM Products & Models

* [https://llama.meta.com](https://llama.meta.com) #LLaMA
* [https://huggingface.co/vilm](https://huggingface.co/vilm) #VILM
* [https://github.com/mistralai/mistral-src](https://github.com/mistralai/mistral-src) #Mistral
* [https://huggingface.co/bkai-foundation-models/vietnamese-llama2-7b-120GB](https://huggingface.co/bkai-foundation-models/vietnamese-llama2-7b-120GB) #Vietnamese #LLaMA2
* PrivateGPT - Đọc và trả lời dữ liệu từ document với sức mạnh của GPT: [https://github.com/imartinez/privateGPT](https://github.com/imartinez/privateGPT) #PrivateGPT
* [https://github.com/nomic-ai/gpt4all](https://github.com/nomic-ai/gpt4all) #GPT4All
* [https://github.com/langgenius/dify](https://github.com/langgenius/dify) #Dify
* [https://ollama.com/seallms/seallm-7b-v2](https://ollama.com/seallms/seallm-7b-v2) #SeaLLM
* [https://huggingface.co/Viet-Mistral](https://huggingface.co/Viet-Mistral) #VietMistral
* FinGPT - LLM models cho domain tài chính: [https://github.com/AI4Finance-Foundation/FinGPT](https://github.com/AI4Finance-Foundation/FinGPT) #FinGPT #finance
* [https://github.com/duydvu/gpt-j-6B-vietnamese-news-api](https://github.com/duydvu/gpt-j-6B-vietnamese-news-api) #GPT-J #Vietnamese
* SEO/Content:
    * WordAI - Công cụ viết nội dung tự động, WordAI sử dụng công nghệ Spinning AI để tạo ra nội dung có độ chính xác cao từ các nguồn dữ liệu có sẵn: [https://wordai.com](https://wordai.com) #SEO #content
    * BuzzSumo - Công cụ nghiên cứu nội dung, BuzzSumo sử dụng AI để tìm kiếm và phân tích các nội dung phổ biến trên mạng xã hội và Internet: [https://buzzsumo.com](https://buzzsumo.com) #SEO #content
    * ContentBot: Công cụ tạo nội dung tự động, ContentBot sử dụng AI để tạo ra nội dung theo yêu cầu từ nguồn dữ liệu có sẵn. #SEO #content
    * Quill: Công cụ viết nội dung tự động, Quill sử dụng AI để tạo ra nội dung tự động dựa trên dữ liệu có sẵn. #SEO #content
    * Acrolinx: Công cụ kiểm tra và tối ưu nội dung, Acrolinx sử dụng AI để đảm bảo sự nhất quán và chất lượng của nội dung trên nhiều nền tảng và kênh truyền thông. #SEO #content

## 3.1. API

* [https://together.ai](https://together.ai) #API
* Ollama - Hỗ trợ cài đặt các LLM model và cung cấp API tương tác #API

## 3.2. GUI

* NextChat: [https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web) #GUI
* [https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web) #GUI
* [https://jan.ai](https://jan.ai) #GUI
* GUI cho việc download và chạy các model LLM: [https://lmstudio.ai](https://lmstudio.ai) #GUI
* Tạo giao diện chat cho các AI models: [https://github.com/huggingface/chat-ui](https://github.com/huggingface/chat-ui) #GUI

## 3.3. Kỹ thuật fine-tune LLM models

Trong thế giới Large Language Models (LLMs), việc tinh chỉnh mô hình (fine-tuning) là một bước cực kỳ quan trọng để giúp mô hình hiểu và giải quyết các tác vụ cụ thể của chúng ta. Nhưng… liệu Fine-tuning truyền thống có phải là cách tối ưu nhất? Hôm nay, mình sẽ so sánh ba kỹ thuật tinh chỉnh phổ biến: Fine-tuning, LoRA và QLoRA – và giúp bạn hiểu khi nào nên dùng cách nào! 🚀
### 3.3.1. Fine-tuning – Tinh chỉnh mô hình toàn bộ

Fine-tuning là cách truyền thống nhất, nơi bạn tinh chỉnh toàn bộ trọng số của mô hình đã được huấn luyện trước (pre-trained model) để tối ưu cho tác vụ của mình.
Ưu điểm:
- Tạo ra mô hình tùy chỉnh cực kỳ mạnh mẽ cho bài toán cụ thể.
- Đảm bảo chất lượng output cao khi mô hình đã được tối ưu hóa cho một nhiệm vụ cụ thể.
Nhược điểm:
- Tốn tài nguyên: Việc tinh chỉnh toàn bộ mô hình đòi hỏi nhiều GPU và thời gian.
- Không tiết kiệm bộ nhớ: Mô hình fine-tuned có thể rất nặng.

### 3.3.2. LoRA (Low-Rank Adaptation) – Tinh chỉnh nhanh mà vẫn hiệu quả

LoRA là một kỹ thuật mới, cho phép bạn chỉ tinh chỉnh một phần nhỏ của mô hình, cụ thể là các trọng số của lớp Attention. Mô hình này tăng tốc quá trình huấn luyện mà không làm thay đổi nhiều cấu trúc ban đầu của mô hình.
Ưu điểm:
- Nhẹ nhàng, tiết kiệm: Chỉ cần thay đổi một số trọng số nhất định mà không phải tinh chỉnh toàn bộ mô hình.
- Tiết kiệm tài nguyên, thời gian huấn luyện nhanh hơn rất nhiều.
Nhược điểm: Đôi khi không đạt được hiệu suất tốt như Fine-tuning khi yêu cầu độ chính xác rất cao.
Khi nào dùng LoRA?
- Khi bạn muốn tiết kiệm tài nguyên và thời gian nhưng vẫn cần tinh chỉnh mô hình cho các tác vụ khá phức tạp.
- Khi làm việc với các mô hình cực kỳ lớn mà không đủ sức mạnh tính toán để fine-tune toàn bộ.

### 3.3.3. QLoRA (Quantized LoRA) – Đỉnh cao của tiết kiệm tài nguyên

QLoRA là sự kết hợp giữa LoRA và quantization – giúp bạn giảm kích thước mô hình mà không làm mất đi quá nhiều chất lượng. Kỹ thuật này giúp mô hình quá trình tinh chỉnh hiệu quả hơn và tiết kiệm bộ nhớ.
Ưu điểm:
- Tiết kiệm tài nguyên gấp đôi, có thể fine-tune trên các mô hình cực kỳ lớn mà không cần nhiều GPU.
- Dễ dàng triển khai trên môi trường có tài nguyên hạn chế, ví dụ như môi trường edge hoặc cloud nhỏ gọn.
Nhược điểm: Đôi khi có thể mất một chút chất lượng nếu không áp dụng cẩn thận.
Khi nào dùng QLoRA?
- Khi bạn cần tinh chỉnh mô hình cực kỳ lớn nhưng tài nguyên tính toán bị hạn chế hoặc khi bạn cần triển khai mô hình nhanh chóng trên môi trường có bộ nhớ nhỏ nhưng vẫn muốn giữ được độ chính xác cao.

### 3.3.4. Kết luận: Dùng cái nào khi nào?

- Fine-tuning là lựa chọn tốt nhất khi bạn cần tối ưu mô hình cho một tác vụ rất cụ thể và không có giới hạn tài nguyên.
- LoRA là lựa chọn lý tưởng khi bạn cần tinh chỉnh nhanh, tiết kiệm tài nguyên, nhưng không muốn làm giảm chất lượng mô hình.
- QLoRA là sự lựa chọn đỉnh cao cho các mô hình rất lớn nhưng bạn muốn giảm thiểu tài nguyên và bộ nhớ mà vẫn duy trì hiệu suất.

# 4. Indexing

## 4.1. Tools

- [HelixDB](https://github.com/HelixDB/helix-db) is an open-source, high-performance graph-vector database designed for Retrieval-Augmented Generation (RAG) and AI applications. Built in Rust and powered by LMDB, it offers a unified platform for managing both graph relationships and vector embeddings, streamlining the development process for AI-driven systems.

## 4.2. Context store

- Context7 - https://context7.com: Context7 là một máy chủ MCP (Model Context Protocol) được phát triển bởi Upstash, nhằm cung cấp tài liệu chính thức và ví dụ mã cập nhật theo thời gian thực cho các mô hình ngôn ngữ lớn (LLMs) và trợ lý lập trình AI như Cursor, Claude Desktop, VS Code, Windsurf, v.v.
- Airweave AI: Airweave là một công cụ cho phép các agent tìm kiếm ngữ nghĩa trên bất kỳ ứng dụng nào. Nó tương thích với MCP và kết nối liền mạch bất kỳ ứng dụng, cơ sở dữ liệu hoặc API nào để biến nội dung của chúng thành kiến thức sẵn sàng cho agent.

## 4.3. RAG (Retrieval-Augmented Generation)

- Weaviate: CSDL vector mã nguồn mở, cloud-native, tìm kiếm ngữ nghĩa, tích hợp LLM.
- Milvus: CSDL vector mã nguồn mở, chuyên quản lý/tìm kiếm vector quy mô lớn, phân tán, mở rộng cao.
- Faiss: Thư viện tìm kiếm tương đồng vector hiệu quả của Facebook AI, tối ưu tốc độ/bộ nhớ, không phải DB hoàn chỉnh.
- Cognita: Framework mã nguồn mở xây dựng ứng dụng RAG, đơn giản hóa phát triển, kiến trúc module hóa, hỗ trợ đa dạng thành phần và tính năng nâng cao.
- LLMWare: Framework mã nguồn mở xây dựng ứng dụng LLM doanh nghiệp (RAG, Agent), cung cấp công cụ pipeline RAG, hỗ trợ nhiều loại dữ liệu, chạy được trên CPU.
- GraphRAG: Kỹ thuật kết hợp RAG với đồ thị tri thức để cải thiện ngữ cảnh LLM, giúp hiểu sâu sắc, truy vấn phức tạp, tăng giải thích được, giảm nhiễu.
