---
relates:
  - "[[Java]]"
  - "[[Fresher Java Interview]]"
  - "[[Backend - Back-end]]"
---
# 1. Resources

- https://viblo.asia/s/nhat-ky-spring-boot-Je5EjWx0KnL
- Dockerize: https://viblo.asia/p/dockerize-project-java-spring-boot-mysql-redis-MkNLrbRaLgA

# 2. Auth

## 2.1. Authorize

- Spring Authorization Server

# 3. Libraries

- Maptruct - DTO mapper: https://github.com/mapstruct/mapstruct
- https://www.baeldung.com/mapstruct
