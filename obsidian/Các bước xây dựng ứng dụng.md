---
aliases: 
relates:
  - "[[Solutions & System Designs & Design Patterns]]"
---
# 1. Resources

## 1.1. Non-Functional Requirements (NFR)

**Non-Functional Requirements (NFR)** là các yêu cầu không liên quan trực tiếp đến các chức năng cụ thể mà hệ thống phải thực hiện, mà thay vào đó, chúng xác định các tiêu chuẩn mà hệ thống cần đáp ứng. Các NFR thường bao gồm:

1. **<PERSON><PERSON><PERSON> suất (Performance):** <PERSON><PERSON><PERSON> đ<PERSON> phản hồi, thời gian xử lý, và khả năng chịu tải.
2. **<PERSON><PERSON><PERSON> năng mở rộng (Scalability):** <PERSON><PERSON><PERSON> năng hệ thống mở rộng khi khối lượng công việc tăng.
3. **<PERSON><PERSON><PERSON> năng bảo mậ<PERSON> (Security):** <PERSON><PERSON><PERSON> vệ dữ liệu và ngăn chặn truy cập tr<PERSON>i phép.
4. **<PERSON><PERSON><PERSON> khả dụng (Availability):** Thời gian hệ thống sẵn sàng hoạt động.
5. **Khả năng bảo trì (Maintainability):** Dễ dàng bảo trì và cập nhật hệ thống.
6. **Khả năng sử dụng (Usability):** Mức độ dễ sử dụng của hệ thống đối với người dùng cuối.
7. **Độ tin cậy (Reliability):** Khả năng hệ thống hoạt động ổn định trong thời gian dài.
8. **Khả năng tương thích (Compatibility):** Khả năng tương thích với các hệ thống khác hoặc môi trường khác.

NFR là yếu tố quan trọng để đảm bảo rằng hệ thống không chỉ hoạt động đúng chức năng mà còn hoạt động tốt trong môi trường thực tế và đáp ứng các yêu cầu về hiệu suất và chất lượng.

## 1.2. Capability

**Capability** trong bối cảnh phát triển phần mềm và hệ thống có thể được hiểu theo hai cách chính:

1. **Khả năng của hệ thống:** Các chức năng hoặc dịch vụ cụ thể mà hệ thống cung cấp. Đây là những gì hệ thống có thể làm, và thường được mô tả thông qua các yêu cầu chức năng (Functional Requirements). Ví dụ, một hệ thống quản lý khách hàng có khả năng lưu trữ thông tin khách hàng, theo dõi giao dịch, và gửi email marketing.
2. **Khả năng của tổ chức:** Trong bối cảnh chiến lược và quản lý, capability còn có thể ám chỉ các năng lực hoặc khả năng mà tổ chức cần có để đạt được mục tiêu kinh doanh. Điều này bao gồm các kỹ năng, quy trình, công nghệ và các yếu tố khác giúp tổ chức thực hiện công việc một cách hiệu quả. Ví dụ, một tổ chức cần có khả năng phát triển phần mềm, quản lý dự án, và bảo mật thông tin.

# 2. Nghiệp vụ

- Lên danh sách các functional/non-functional requirements, các capabilities cho hệ thống.

# 3. Các yêu cầu cần thiết

- Logging
- Benmark
