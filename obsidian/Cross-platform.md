---
relates:
  - "[[Frontend - Front-end]]"
  - "[[Backend - Back-end]]"
  - "[[Microservices]]"
  - "[[SaaS]]"
  - "[[Rust]]"
---
# 1. Frameworks

- Expo - An open-source framework for making universal native apps with React. Expo runs on Android, iOS, and the web: https://github.com/expo/expo

## 1.1. Lynx

Lynx is an open-source *family* of technologies empowering developers to create truly native user interfaces for mobile and web from a single codebase. It focuses on performance, scalability, and developer velocity, powering large-scale apps like TikTok.

### 1.1.1. Core Philosophy and Features

- Cross-platform native UI support (mobile, web, desktop, TV, IoT)
- Framework and platform agnostic core engine
- Performance-driven dual-threaded UI programming
- Modern Rust-based tooling for robustness
- Supports custom renderers for pixel-perfect UI consistency

### 1.1.2. Key Repositories

| Repository        | Description                                                                             |
| ----------------- | --------------------------------------------------------------------------------------- |
| **lynx**          | Main framework and core engine. Active development includes new features and bug fixes. |
| **lynx-stack**    | Core JavaScript stack including ReactLynx, Rspeedy, Lynx for Web.                       |
| **lynx-devtool**  | Debugging tools for Lynx applications.                                                  |
| **primjs**        | High-performance JS engine optimized for Lynx, fully ES2019 compliant.                  |
| **lynx-examples** | Examples, tutorials, UI components, and platform demos.                                 |
| **habitat**       | Lightweight dependency management tool for large codebases.                             |
| **lynx-website**  | Official website and documentation.                                                     |

### 1.1.3. Development and Community

- Verified organization controlling [lynxjs.org](https://lynxjs.org)
- Production-ready open source starting from version 3.x
- Open development on GitHub with active issue tracking
- Lynx Authors community supports contributors
- Ongoing feature development and bug fixes
