---
relates:
  - "[[Work]]"
  - "[[<PERSON><PERSON> chung IT]]"
---

Source: https://dev.to/_ndeyefatoudiop/37-tips-from-a-senior-frontend-developer-251b

---

I really liked [@abbeyperini](https://dev.to/abbeyperini) [post](https://dev.to/abbeyperini/12-tips-from-a-mid-level-developer-29bk) and decided to share my tips after **5+ years** as a software dev.

Ready? Let's dive in 💪.

# 1. Master the fundamentals

A house 🏠 built on shaky grounds will fall apart at the smallest issue.

Similarly, if you don't have strong basics:

- You will struggle with JavaScript frameworks
- You will get stuck at the first unfamiliar problem
- You won't grasp the common themes between some problems

So, if you want to improve as a frontend developer, master **HTML**, **CSS**, and **JavaScript** first.

---

# 2. Understand how the web works

Frontend development has become more complex in the past few years.

Numerous tools are used (_bundlers_, _transpilers_, etc.)

If you don't understand how the web works (i.e., the "only" languages supported are **HTML** (for structure), **CSS** (for styling), and **JavaScript** (for interactivity)), you'll struggle to understand why there's a need for so many tools to run your code online.

---

# 3. Get familiar with Data Structures & Algorithms

Data Structures & Algorithms often get a bad rap due to coding interviews 🤦‍♀️.

**Yet**, it's crucial to understand the key ones and their complexities. Without this knowledge, you won't be able to code complex programs or evaluate your code efficiency.

Below is a non-exhaustive list of Data Structures/Algorithms to know :

**Data Structures:** _Stack_, _Queue_, _Hashmap_, _Set_, _Graph_, etc.

**Algorithms:** _Dynamic programming_, _Greedy algorithm_, _Recursion_, etc.

---

# 4. Learn by doing rather than reading/watching

Tutorials trick you into feeling like you're making progress.

They make you believe you're learning and improving, but you're not moving forward. Real learning happens through practice or teaching.

So, practice a lot and avoid getting stuck in tutorial hell 🔥.

---

# 5. Ask for help when stuck

You're not alone.

Chances are, you have senior developers nearby whom you can ask for help.

So, don't waste time going down rabbit holes. Your goal is to provide value, and you're not achieving that if you're wasting time tackling issues others have already addressed.

---

# 6. Ask for help the proper way

Before asking for help, make sure you have done the bare minimum.

You should first:

- Search on Google/Stack Overflow/ChatGPT for solutions
- Understand your goal and what's not working
- Keep a record of all your failed attempts

Also, don't repeatedly ask for help for the same issue. When you get help, note it somewhere so you don't forget it 😉.

---

# 7. Don't copy/paste code you don't understand

This is seriously bad for various reasons:

- You might end up with insecure code that exposes sensitive information like tokens.
- You won't be able to debug the code easily.
- You won't be able to explain it to your colleagues.

This is particularly concerning with ChatGPT, as it sometimes provides only 80% accurate answers.

---

# 8. Don't blindly apply every piece of advice found online

I made this mistake when I just started.

I came across advice saying memoization in React was evil, so I stopped using it.

To my embarrassment, my code crashed in pre-production 😅.

So, don't be "old" me.

When you see advice online, ask yourself the following questions first:

- Does this advice apply to me?
- Is the person advising in a similar context to mine?

**TLDR**: Exercise common sense 🫠.

---

# 9. Assume good intent: people want you to succeed ❤️

When you're new, it's common to think people are waiting to catch your mistakes and fire you.

At least, that was what my mind was whispering to me.

But it's the opposite.

When a company hires you, it wants you to succeed badly. Otherwise, it's a waste of the time and resources they invested in training and onboarding you.

So, trust that your company wants you to do well, and don't hesitate to lean on your colleagues for support.

---

# 10. Done is better than perfect

The pursuit of perfection often results in the following:

- procrastination
- waste of time
- overcomplexity
- etc.

So, aim to ship/validate a V0 before pushing for more.

---

# 11. Always break tasks into manageable ones

Easiest way to feel overwhelmed?

👉 Trying to bite more than you can chew.

Always break projects into smaller tasks.

This will:

- Keep you from feeling overwhelmed
- Make your PRs (pull requests) easier to review
- Provide a sense of progress

---

# 12. Be trusted to reach out when you need help

When starting out, your top priority is earning your manager's trust.

They should have peace of mind when thinking about you.

They should believe that:

- You're reliable with simple tasks
- You'll seek help when necessary
- You'll communicate any problems

You don't want to add to your manager's workload by becoming a problem they must constantly monitor 🔎.

---

# 13. Show enthusiasm for the work

You can make up for a lot of shortcomings with enthusiasm 🤪.

When you're new, be eager and excited.

Only experienced devs can afford to lack enthusiasm.

No one wants to constantly push someone to work. Mentoring is tough enough already; having to motivate someone makes it more challenging.

---

# 14. Stay open to learning new things/tools/methods

Frontend development is constantly evolving.

So, you need to be open to jumping to new technologies.

Don't cling too tightly to your current tools. Instead, show an appetite for learning 😋.

---

# 15. Master your dev tools 🛠️

Want to speed up your development time?

Master your dev tools:

- Your IDE (e.g., VSCode)
- Your source control system (e.g., Github)
- Your browser and the inspector (e.g., Chrome inspector)
- Etc.

---

# 16. Focus on delivering value

Don't write code in a vacuum.

Every code you write should provide value to:

- Your customers
- Your company
- Your stakeholders
- Etc.

Your compensation is tied to the value you provide, not the code you write.

So, prioritize writing effective code that serves a purpose 🥅.

---

# 17. Advocate for your work: it won't speak for itself

Probably one of the most common mistakes for new devs (especially if you come from a culture valuing modesty).

1. You've done something remarkable.
    
2. No one knows about it.
    

👉 That work won't matter.

So, share your work through writing, demos, etc.

---

# 18. Prefer writing dumb code over clever code

Code is read much more often than it is written 📝.

So, refrain from writing clever code that only you can understand.

**Readability > Performance > Cleverness.**

You want your colleagues to work with your code efficiently, assist you if necessary, etc.

---

# 19. Your manager is your best ally

Unless you're exceptionally unlucky, your manager is there to support your growth 📈.

They typically want you to thrive, contribute to the team, and remain with the company instead of seeking opportunities elsewhere.

So, make sure to enlist them to reach your goals.

Share your wins, setbacks (in a positive light), and frustrations instead of struggling alone.

---

# 20. Make your manager's life easier

This one's easy but often overlooked.

Your manager likely has issues you can assist with:

- They might need to add documentation but lack time
- Their workload could be overwhelming, and they need support
- And more

This is probably the simplest way to earn your manager's support (for promotions, raises, etc.). They're already aware of the tasks and can directly see their impact (at least for them).

---

# 21. Understand the big picture behind your tasks

Don't be a code monkey 🐒.

It might serve you well when starting.

But to reach the next level, you need to understand the context behind your tasks:

- Why they are valuable
- Why you were assigned to them
- How they fit in the company's overall strategy

This understanding is essential for advancing to the next level of your career.

---

# 22. Contribute to the team (documentation, tech talk, demos, etc.)

This benefits not just the company but also you.

By conducting demos, sharing documentation, etc., you showcase your skills and enhance the team's productivity.

Always aim to uplift your team's performance as much as possible: it's enjoyable and rewarding 😊.

---

# 23. Become the "go-to-person" in a specific area

If I had to offer just one tip, it would be this.

At the beginning of your career, exploring various areas is okay.

However, to advance to mid/senior levels, focus on building expertise in one area. This is more compelling for promotions than being average in multiple areas.

So aim for [T-shaped](https://www.forbes.com/sites/lisabodell/2020/08/28/futurethink-forecasts-t-shaped-teams-are-the-future-of-work/) skills: broad knowledge with deep expertise in one area.

---

# 24. Develop your communication skills

Unfortunately, this is a must 😀.

Communication is vital for developers. We often have to do the following:

- RFCs (Request for Comments)
- Demos
- Presentations
- Etc.

So, make sure you have a basic level of proficiency in communication.

---

# 25. Take breaks when you're stuck on a problem

It's tough to pause when you're deep into a problem.

Even after 5+ years, I struggle with it 🥹.

Yet, I consistently generate fresher ideas after taking a break.

So, step away if you've been stuck for too long.

---

# 26. Work from your strengths, not your weaknesses

Stop wasting time trying to fix apparent weaknesses.

If it takes you consistently >1 hour to do a task that others at your level accomplish in <5 minutes, steer clear of that task.

Likely, investing more energy won't make you exceptional at it.

Instead, do the essentials and concentrate on maximizing your strengths. If something comes naturally to you and is valuable, do it more 🚀.

---

# 27. Take ownership of your career path

No one will plan your career for you.

And without a plan, you'll be working for someone else's plans.

So make sure you create a plan for what you want to achieve in 1/2/5 years 💪.

---

# 28. Hang with other devs

Are you currently experiencing impostor syndrome?

If so, spend time with other developers.

You'll quickly realize you're not alone.

Connecting with other devs has additional benefits:

- You can pick up new tricks/tips
- You can discuss shared experiences
- You can complement each other's work
- Etc.

---

# 29. Mentor younger devs

This is one of the top cures for impostor syndrome.

Once you begin mentoring younger devs:

- You'll realize you know things
- You'll establish yourself as a mid/senior dev
- Etc.

---

# 30. Diversify the problems you solve

If you're constantly tackling the same issues, your progress will plateau.

Ensure you're solving diverse problems so you can:

- Compare various approaches
- Develop a toolkit for problem-solving
- Etc.

---

# 31. Find mentors

Having great mentors has been the highlight of my career 🥰.

- Mentors keep you grounded as they understand your journey.
- They guide you to avoid the mistakes they've made.
- Etc.

**How to find a mentor?**

Connect with a more experienced developer you interact with, ask questions, discuss their experiences, etc.

If you don't have access to a senior dev, engage with individuals on platforms like X, build a relationship with them, and then reach out 😉.

---

# 32. Commit to a JavaScript framework & master it

The best framework is the one that helps you achieve your goals the fastest.

So, ignore pointless debates online.

Choose the framework you're most comfortable with or need to learn.

And master it. That's sufficient.

With solid JavaScript fundamentals, transitioning to another framework will be quick.

---

# 33. Constantly think of the user experience

As a frontend developer, you should think about the user.

Even if you have PMs or designers, ensure the user experience is nice 😌.

- Use loading states when needed
- Communicate progress in the UI
- Give feedback to the user
- Etc.

---

# 34. Be comfortable saying no

This is a tough one for me.

I get excited about every project and struggle to decline.

But as a developer, you'll often have more requests than you can manage.

So, prioritize the ones that align best with your goals 🥅.

# 35. Continuously invest in your skills

By choosing to become a frontend developer, you committed to a career in which you must continuously learn.

Therefore, keep investing in your skills by acquiring new languages, mastering new techniques, etc.

---

# 36. When faced with too much work, reduce the features vs. the quality of the code.

The more features your app has, the better, right? Right?

Initially, this might seem true, but additional features lead to more code. And more code means more issues (maintenance, bugs, etc.).

So, when pressed with time, trim features over sacrificing code quality.

---

# 37. Strive to understand your collaborators (designers, Backend developers, etc.)

Always show respect to your collaborators (backend devs, designers, PMs, etc.).

App development is a team effort.

The more synergy among team members, the happier and more effective the environment will be 🥰.