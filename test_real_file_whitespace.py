#!/usr/bin/env python3
"""
Test whitespace cleanup with real Vietnamese file
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import clean_whitespace_lines
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_real_file_whitespace():
    """Test whitespace cleanup with the actual Vietnamese file"""
    
    print("Testing whitespace cleanup with real Vietnamese file...")
    print("=" * 60)
    
    # Use the actual problematic file
    note_path = Path("obsidian/Top 10 câu hỏi phỏng vấn System Design và Microservices.md")
    
    if not note_path.exists():
        print(f"File not found: {note_path}")
        return False
    
    # Read the file content
    with open(note_path, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    print(f"📄 File: {note_path.name}")
    print(f"📏 Original content length: {len(original_content)} characters")
    
    # Analyze original content
    original_lines = original_content.split('\n')
    print(f"📊 Original statistics:")
    print(f"   Total lines: {len(original_lines)}")
    
    empty_lines = sum(1 for line in original_lines if line == '')
    whitespace_lines = sum(1 for line in original_lines if line.strip() == '' and line != '')
    content_lines = sum(1 for line in original_lines if line.strip() != '')
    
    print(f"   Empty lines: {empty_lines}")
    print(f"   Whitespace-only lines: {whitespace_lines}")
    print(f"   Content lines: {content_lines}")
    
    # Show some whitespace-only lines if they exist
    if whitespace_lines > 0:
        print(f"\n🔍 Found {whitespace_lines} whitespace-only lines:")
        for i, line in enumerate(original_lines):
            if line.strip() == '' and line != '':
                print(f"   Line {i+1:2d}: {repr(line)}")
                if i > 0:
                    print(f"            Before: {repr(original_lines[i-1][:50])}")
                if i < len(original_lines) - 1:
                    print(f"            After:  {repr(original_lines[i+1][:50])}")
                print()
    
    # Clean the content
    print(f"\n🧹 Cleaning whitespace-only lines...")
    cleaned_content = clean_whitespace_lines(original_content)
    
    # Analyze cleaned content
    cleaned_lines = cleaned_content.split('\n')
    print(f"📊 Cleaned statistics:")
    print(f"   Total lines: {len(cleaned_lines)}")
    
    cleaned_empty_lines = sum(1 for line in cleaned_lines if line == '')
    cleaned_whitespace_lines = sum(1 for line in cleaned_lines if line.strip() == '' and line != '')
    cleaned_content_lines = sum(1 for line in cleaned_lines if line.strip() != '')
    
    print(f"   Empty lines: {cleaned_empty_lines}")
    print(f"   Whitespace-only lines: {cleaned_whitespace_lines}")
    print(f"   Content lines: {cleaned_content_lines}")
    
    # Verify the cleanup
    print(f"\n✅ Verification:")
    print(f"   Content lines preserved: {content_lines == cleaned_content_lines}")
    print(f"   Whitespace lines cleaned: {cleaned_whitespace_lines == 0}")
    print(f"   Total empty lines: {empty_lines + whitespace_lines == cleaned_empty_lines}")
    
    # Show the difference in content length
    print(f"   Content length change: {len(original_content)} -> {len(cleaned_content)} ({len(cleaned_content) - len(original_content):+d})")
    
    # Test that content is preserved (ignoring whitespace changes)
    original_content_only = '\n'.join(line for line in original_lines if line.strip() != '')
    cleaned_content_only = '\n'.join(line for line in cleaned_lines if line.strip() != '')
    
    content_preserved = original_content_only == cleaned_content_only
    print(f"   Actual content preserved: {'✅' if content_preserved else '❌'}")
    
    if not content_preserved:
        print(f"   WARNING: Content may have been modified!")
        return False
    
    return True

def test_integration_with_sync():
    """Test that the cleanup integrates properly with the sync process"""
    
    print(f"\n" + "=" * 60)
    print("Testing integration with sync process...")
    print("=" * 60)
    
    # Create a test file with whitespace issues
    test_content = """# Test Document

This is the first paragraph.
   
\t\t
## Section 2 với tiếng Việt

Đây là đoạn văn tiếng Việt.
    \t  
Đoạn văn khác ở đây.

\t   \t

### Kết luận

Nội dung cuối cùng.
   """
    
    test_file = Path("test_integration.md")
    
    try:
        # Write test file
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📄 Created test file: {test_file}")
        
        # Test the integration by simulating the sync process
        from obsidian_to_google_sync import ObsidianToGoogleSync
        
        # Create a mock sync tool for testing
        class MockSync:
            def process_images_in_markdown(self, content, note_path):
                # Use the actual method that includes whitespace cleanup
                from obsidian_to_google_sync import clean_whitespace_lines
                
                # This simulates what happens in the real process_images_in_markdown
                cleaned_content = clean_whitespace_lines(content)
                return cleaned_content, []  # No images for this test
            
            def convert_markdown_to_docs_requests(self, content, note_path):
                # This simulates what happens in the real convert_markdown_to_docs_requests
                from obsidian_to_google_sync import clean_whitespace_lines
                
                cleaned_content = clean_whitespace_lines(content)
                
                # Simple conversion for testing
                lines = cleaned_content.split('\n')
                requests = []
                index = 1
                
                for line in lines:
                    if line.strip():  # Only process non-empty lines
                        requests.append({
                            'insertText': {
                                'location': {'index': index},
                                'text': line + '\n'
                            }
                        })
                        index += len(line) + 1
                
                return requests
        
        sync_tool = MockSync()
        
        # Read test content
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test image processing (which includes whitespace cleanup)
        processed_content, _ = sync_tool.process_images_in_markdown(content, test_file)
        
        # Test conversion (which also includes whitespace cleanup)
        requests = sync_tool.convert_markdown_to_docs_requests(content, test_file)
        
        print(f"📊 Integration test results:")

        content_lines = content.split('\n')
        processed_lines = processed_content.split('\n')

        print(f"   Original lines: {len(content_lines)}")
        print(f"   Processed lines: {len(processed_lines)}")
        print(f"   Generated requests: {len(requests)}")

        # Count whitespace-only lines in original vs processed
        orig_whitespace = sum(1 for line in content_lines if line.strip() == '' and line != '')
        proc_whitespace = sum(1 for line in processed_lines if line.strip() == '' and line != '')
        
        print(f"   Whitespace-only lines: {orig_whitespace} -> {proc_whitespace}")
        print(f"   ✅ Integration successful: {proc_whitespace == 0}")
        
        return proc_whitespace == 0
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test file
        if test_file.exists():
            test_file.unlink()

if __name__ == "__main__":
    print("Testing Whitespace Cleanup with Real Files")
    print("=" * 70)
    
    success1 = test_real_file_whitespace()
    success2 = test_integration_with_sync()
    
    if success1 and success2:
        print("\n🎉 All tests passed!")
        print("Whitespace-only lines will be properly cleaned in the sync process.")
    else:
        print("\n💥 Some tests failed!")
        print("There may be issues with the whitespace cleanup integration.")
