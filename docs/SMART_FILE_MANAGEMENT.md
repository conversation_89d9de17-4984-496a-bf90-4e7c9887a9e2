# Smart File Management - C<PERSON>i tiến quản lý file thông minh

## Vấn đề trước đây

Tr<PERSON>ớ<PERSON> khi cải tiến, module có những hạn chế:
- **Duplicate files**: <PERSON>ôn tạo file mới, không kiểm tra file đã tồn tại
- **No error tracking**: Không lưu danh sách file upload fail
- **Manual retry**: <PERSON><PERSON>i chạy lại toàn bộ sync khi có lỗi
- **Waste storage**: Tạo nhiều bản copy không cần thiết

## Giải pháp mới

### 1. 🔍 Kiểm tra file đã tồn tại

**Method `find_existing_file()`:**
```python
def find_existing_file(self, filename: str, folder_id: str) -> Optional[str]:
    """Find existing file in Google Drive folder"""
    query = f"name='{filename}' and '{folder_id}' in parents and trashed=false"
    
    results = self.drive_service.files().list(
        q=query,
        fields="files(id, name, modifiedTime)"
    ).execute()
    
    # Return file_id if found, None if not
```

**Method `find_existing_doc()`:**
```python
def find_existing_doc(self, title: str, folder_id: str) -> Optional[str]:
    """Find existing Google Doc in folder"""
    query = f"name='{title}' and '{folder_id}' in parents and mimeType='application/vnd.google-apps.document' and trashed=false"
    
    # Similar logic for documents
```

### 2. 🔄 Replace vs Upload Logic

**Enhanced `upload_media_file()`:**
```python
def upload_media_file(self, file_path: Path, folder_id: str):
    filename = file_path.name
    
    # Check if file already exists
    existing_file_id = self.find_existing_file(filename, folder_id)
    
    if existing_file_id:
        # Update existing file
        result = self.update_existing_file(existing_file_id, file_path)
        if result:
            return result
        else:
            # Add to failed list and try upload new
            self.failed_uploads.append({...})
    
    # Upload new file if not exists or update failed
    # ... upload logic
```

**Enhanced `create_google_doc()`:**
```python
def create_google_doc(self, title: str, content_requests: List[Dict], folder_id: str):
    # Check if document already exists
    existing_doc_id = self.find_existing_doc(title, folder_id)
    
    if existing_doc_id:
        # Clear existing content and reuse document
        if self.clear_document_content(existing_doc_id):
            doc_id = existing_doc_id
        else:
            # Add to failed list and create new
            self.failed_uploads.append({...})
    
    # Create new document if needed
    # ... creation logic
```

### 3. 📊 Failed Uploads Tracking

**Tracking structure:**
```python
self.failed_uploads = [
    {
        'file_path': 'path/to/file.png',
        'action': 'upload',  # upload, update, create_doc, format_doc
        'error': 'Detailed error message'
    },
    # ... more failures
]
```

**Actions tracked:**
- `upload`: Failed to upload new media file
- `update`: Failed to update existing media file  
- `create_doc`: Failed to create new Google Doc
- `update_doc`: Failed to update existing Google Doc
- `format_doc`: Failed to apply formatting to document

### 4. 📋 Sync Results Report

**Method `report_sync_results()`:**
```python
def report_sync_results(self):
    """Report sync results and failed uploads"""
    if not self.failed_uploads:
        logger.info("✅ All files processed successfully!")
    else:
        logger.warning(f"⚠️  {len(self.failed_uploads)} files failed to process:")
        
        # Group failures by action type
        failures_by_action = {}
        for failure in self.failed_uploads:
            action = failure['action']
            if action not in failures_by_action:
                failures_by_action[action] = []
            failures_by_action[action].append(failure)
        
        # Report each type of failure
        for action, failures in failures_by_action.items():
            logger.warning(f"\n{action.upper()} FAILURES ({len(failures)}):")
            for i, failure in enumerate(failures, 1):
                logger.warning(f"  {i}. {failure['file_path']}")
                logger.warning(f"     Error: {failure['error']}")
        
        # Save failed uploads to file
        with open("failed_uploads.json", 'w') as f:
            json.dump(self.failed_uploads, f, indent=2)
```

### 5. 🔄 Retry Functionality

**Command line option:**
```bash
python obsidian_to_google_sync.py --retry-failed
```

**Method `retry_failed_uploads()`:**
```python
def retry_failed_uploads(self, failed_file: str = "failed_uploads.json"):
    """Retry previously failed uploads"""
    if not os.path.exists(failed_file):
        logger.info(f"No failed uploads file found: {failed_file}")
        return True
    
    with open(failed_file, 'r') as f:
        failed_uploads = json.load(f)
    
    # Clear current failed list
    self.failed_uploads = []
    
    # Retry each failed upload
    for failure in failed_uploads:
        # Implement specific retry logic based on action type
        # ...
```

## Workflow mới

### Sync bình thường:
1. **Scan Obsidian vault** → tìm tất cả markdown files và media
2. **Check existing files** → query Google Drive cho từng file
3. **Smart upload/update**:
   - File đã có → update content
   - File chưa có → upload mới
   - Lỗi → add vào failed_uploads list
4. **Report results** → hiển thị summary và save failed_uploads.json

### Retry failed uploads:
1. **Load failed_uploads.json** → đọc danh sách file fail trước đó
2. **Retry each failure** → thử lại từng file với logic phù hợp
3. **Update failed list** → remove successful retries
4. **Save updated list** → chỉ giữ lại file vẫn fail

## Lợi ích

### 1. **Tiết kiệm storage**
- Không tạo duplicate files
- Update content thay vì tạo mới
- Giữ nguyên sharing permissions

### 2. **Reliability**
- Track tất cả failures
- Có thể retry sau
- Không mất data khi có lỗi

### 3. **User experience**
- Clear reporting về kết quả sync
- Biết chính xác file nào fail và tại sao
- Có thể fix issues và retry

### 4. **Performance**
- Chỉ update file thay đổi
- Không waste bandwidth upload duplicate
- Faster sync cho lần chạy tiếp theo

## Testing

### Test cases mới:
```python
def test_failed_uploads_tracking():
    """Test failed uploads tracking functionality"""
    sync_tool = ObsidianToGoogleSync("", "")
    
    # Test adding failed uploads
    sync_tool.failed_uploads.append({
        'file_path': 'test_image.png',
        'action': 'upload',
        'error': 'Test error'
    })
    
    # Verify tracking works
    assert len(sync_tool.failed_uploads) == 1
```

## Usage Examples

### Sync với error handling:
```bash
# Run sync
python obsidian_to_google_sync.py

# Output:
# ✅ Sync completed successfully!
# ⚠️  3 files failed to process.
# Check failed_uploads.json for details.
# Run with --retry-failed to retry failed uploads.

# Check failed uploads
cat failed_uploads.json

# Retry failed uploads
python obsidian_to_google_sync.py --retry-failed
```

### Makefile integration:
```bash
# Normal sync
make -f sync.mk sync-google

# Retry failures
python obsidian_to_google_sync.py --retry-failed
```

## Files đã thay đổi

- **`obsidian_to_google_sync.py`**:
  - Added `find_existing_file()` và `find_existing_doc()`
  - Added `update_existing_file()` và `clear_document_content()`
  - Enhanced `upload_media_file()` và `create_google_doc()`
  - Added `report_sync_results()` và `retry_failed_uploads()`
  - Added `--retry-failed` CLI option

- **`test_google_sync.py`**:
  - Added `test_failed_uploads_tracking()`

- **`GOOGLE_SYNC_README.md`**:
  - Added Smart File Management section
  - Updated command line options

- **`README.md`**:
  - Updated feature list

- **`SMART_FILE_MANAGEMENT.md`**:
  - This documentation file

## Future Enhancements

1. **Incremental sync**: Chỉ sync file đã thay đổi dựa trên timestamp
2. **Conflict resolution**: Handle conflicts khi file được edit ở cả 2 nơi
3. **Batch operations**: Group multiple operations để optimize API calls
4. **Progress tracking**: Real-time progress bar cho large syncs
5. **Selective retry**: Retry chỉ specific types of failures

Tính năng Smart File Management làm cho module robust và user-friendly hơn đáng kể! 🚀
