# Fix Vị Trí <PERSON> Ảnh trong Google Docs

## Vấn đề

Trước đâ<PERSON>, tất cả hình ảnh được chèn vào **vị trí cố định** (index 1) ở đầu Google Doc thay vì vị trí chính xác trong nội dung. Điều này khiến:

- Hình ảnh xuất hiện sai vị trí
- Trải nghiệm đọc không tốt
- Nội dung không giữ được cấu trúc gốc từ Obsidian

## Nguyên nhân

### Vấn đề 1: T<PERSON>h toán vị trí không chính xác
```python
# Code cũ - vị trí tính trong markdown gốc
position = len(modified_content[:match.start() - offset]) + 1
```

### Vấn đề 2: Chèn ở vị trí cố định
```python
# Code cũ - luôn chèn ở index 1
success = self.insert_image_into_doc(doc_id, public_url, image_name, 1)
```

### Vấn đề 3: Không xử lý placeholder đúng cách
- Placeholder không được tạo với ID duy nhất
- Không tìm được vị trí chính xác trong Google Doc

## Giải pháp

### 1. Tạo Placeholder với ID Duy Nhất

**Trước:**
```python
placeholder = f"\n[Image: {image_name}]\n"
```

**Sau:**
```python
placeholder_id = f"IMAGE_PLACEHOLDER_{len(image_positions)}"
placeholder_text = f"\n[{placeholder_id}]\n"
```

### 2. Tính Toán Vị Trí Chính Xác trong Google Doc

```python
def calculate_image_positions_in_doc(self, content_requests, image_positions):
    # Xây dựng nội dung text đầy đủ từ tất cả requests
    text_content = ""
    for request in content_requests:
        if 'insertText' in request:
            text_content += request['insertText']['text']
    
    # Tìm vị trí placeholder trong text content
    for image_name, image_path, placeholder_id in image_positions:
        placeholder_text = f"[{placeholder_id}]"
        placeholder_pos = text_content.find(placeholder_text)
        
        if placeholder_pos != -1:
            # Tính index chính xác với grapheme clusters
            text_before_placeholder = text_content[:placeholder_pos]
            doc_index = 1 + grapheme_len(text_before_placeholder)
            
            image_positions_with_indices.append((image_name, image_path, doc_index))
```

### 3. Chèn Hình Ảnh Theo Thứ Tự Ngược

```python
# Xử lý hình ảnh theo thứ tự ngược để tránh index shifting
sorted_positions = sorted(image_positions, key=lambda x: x[2], reverse=True)

for image_name, image_path, position in sorted_positions:
    success = self.insert_image_into_doc(doc_id, public_url, image_name, position)
```

### 4. Xóa Placeholder Sau Khi Chèn

```python
def remove_image_placeholder(self, doc_id: str, image_name: str, image_position: int):
    # Tìm và xóa placeholder text sau khi chèn hình ảnh thành công
    # Tránh để lại text placeholder trong document
```

## Workflow Mới

### Bước 1: Xử lý Markdown
```
Original: ![[image.png]]
↓
Processed: [IMAGE_PLACEHOLDER_0]
```

### Bước 2: Convert sang Google Docs Requests
```
Markdown → HTML → Google Docs API requests
Text content được xây dựng với placeholders
```

### Bước 3: Tính Toán Vị Trí
```
Placeholder position trong text: 1178
↓
Google Doc index: 1179 (1 + grapheme_len(text_before))
```

### Bước 4: Chèn Hình Ảnh
```
Upload image → Get public URL → Insert at calculated index
```

### Bước 5: Dọn Dẹp
```
Remove placeholder text → Clean document
```

## Kết Quả Test

### Test với File Tiếng Việt
```
📄 File: Top 10 câu hỏi phỏng vấn System Design và Microservices.md
📏 Content length: 4817 characters

✅ Found 1 images:
   - a03077b24546810e9aabd32f2afe2608_MD5.webp → IMAGE_PLACEHOLDER_0

✅ Placeholder position: Line 35
✅ Calculated position: Index 1179
✅ Context verification: Correct position in text

✅ Test completed successfully!
```

## So Sánh Trước/Sau

### Trước Fix:
```
Google Doc:
[IMAGE] ← Tất cả hình ảnh ở đây
Đây là đoạn văn đầu tiên...
Đây là đoạn văn có hình ảnh...
Đây là đoạn văn cuối...
```

### Sau Fix:
```
Google Doc:
Đây là đoạn văn đầu tiên...
[IMAGE] ← Hình ảnh ở đúng vị trí
Đây là đoạn văn cuối...
```

## Lợi Ích

1. **Vị trí chính xác**: Hình ảnh xuất hiện đúng vị trí trong nội dung
2. **Trải nghiệm tốt**: Giữ được cấu trúc gốc từ Obsidian
3. **Hỗ trợ Unicode**: Tính toán chính xác với tiếng Việt và các ngôn ngữ phức tạp
4. **Robust**: Xử lý nhiều hình ảnh trong cùng document
5. **Clean**: Tự động xóa placeholder text

## Files Đã Sửa

- `obsidian_to_google_sync.py`:
  - `process_images_in_markdown()` - Tạo placeholder với ID duy nhất
  - `calculate_image_positions_in_doc()` - Method mới tính vị trí chính xác
  - `insert_embedded_images()` - Chèn theo thứ tự ngược
  - `remove_image_placeholder()` - Method mới xóa placeholder

## Testing

- `test_image_positioning.py` - Test logic cơ bản
- `test_real_file_image_positioning.py` - Test với file tiếng Việt thực tế

## Sử Dụng

Fix này được áp dụng tự động khi chạy sync:

```bash
python3 obsidian_to_google_sync.py
```

Hình ảnh sẽ được chèn vào đúng vị trí trong Google Doc theo cấu trúc gốc từ Obsidian.
