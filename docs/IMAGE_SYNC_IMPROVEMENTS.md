# Cải tiến xử lý hình ảnh trong Obsidian to Google Docs Sync

## Vấn đề trước đây

Trước khi cải tiến, module chỉ:
- Upload hình ảnh lên Google Drive
- Không chèn hình ảnh vào Google Docs
- Hình ảnh chỉ tồn tại như files riêng biệt trong Drive

## Giải pháp mới

### 1. Xử lý hình ảnh thông minh

**Tìm kiếm hình ảnh tự động:**
- Tìm trong thư mục hiện tại của note
- Tìm trong các thư mục phổ biến: `attachments/`, `assets/`, `media/`
- Hỗ trợ nhiều format: PNG, JPG, JPEG, GIF, BMP, SVG, PDF, MP4, MP3, WAV

**Xử lý markdown:**
```python
def process_images_in_markdown(self, markdown_content: str, note_path: Path):
    # Tìm tất cả ![[image.png]] syntax
    # Thay thế bằng placeholder text
    # Tr<PERSON> về vị trí để chèn hình ảnh sau
```

### 2. Upload và chia sẻ

**Upload lên Google Drive:**
```python
def upload_media_file(self, file_path: Path, folder_id: str):
    # Upload file
    # Tạo public permission
    # Trả về public URL cho embedding
```

**Tạo public URL:**
- Format: `https://drive.google.com/uc?id={file_id}`
- Cho phép embedding trực tiếp vào Google Docs

### 3. Chèn vào Google Docs

**API request để chèn hình ảnh:**
```python
def insert_image_into_doc(self, doc_id: str, image_url: str, image_name: str):
    requests = [{
        'insertInlineImage': {
            'location': {'index': insert_index},
            'uri': image_url,
            'objectSize': {
                'height': {'magnitude': 300, 'unit': 'PT'},
                'width': {'magnitude': 400, 'unit': 'PT'}
            }
        }
    }]
```

### 4. Workflow hoàn chỉnh

1. **Đọc markdown** và tìm `![[image.png]]` syntax
2. **Xử lý nội dung** - thay thế image syntax bằng placeholder
3. **Convert markdown** sang Google Docs requests
4. **Tạo Google Doc** với nội dung text
5. **Upload hình ảnh** lên Drive với public permission
6. **Chèn hình ảnh** vào đúng vị trí trong document

## Kết quả

### Trước khi cải tiến:
```
Google Drive/
└── Obsidian Sync/
    ├── note.docx (không có hình ảnh)
    └── Media/
        └── image.png (file riêng biệt)
```

### Sau khi cải tiến:
```
Google Drive/
└── Obsidian Sync/
    ├── note.docx (có hình ảnh được chèn trực tiếp)
    └── Media/
        └── image.png (file gốc)
```

## Testing

### Test cases đã thêm:
```python
def test_image_processing():
    # Test tìm kiếm hình ảnh
    # Test xử lý markdown với images
    # Test tạo image positions
```

### Demo script:
```bash
python demo_image_sync.py
# Tạo vault demo với 3 hình ảnh
# Hướng dẫn test sync
```

## Sử dụng

### Cách 1: Sync bình thường
```bash
python obsidian_to_google_sync.py
```

### Cách 2: Test với demo vault
```bash
make -f sync.mk demo-images
# Tạo demo vault với hình ảnh
# Cập nhật config
# Chạy sync
```

### Cách 3: Sử dụng script runner
```bash
./run_google_sync.sh
```

## Lợi ích

1. **Trải nghiệm người dùng tốt hơn:**
   - Hình ảnh xuất hiện trực tiếp trong Google Docs
   - Không cần mở files riêng biệt

2. **Tự động hoàn toàn:**
   - Tìm hình ảnh tự động
   - Upload và chèn tự động
   - Không cần can thiệp thủ công

3. **Hỗ trợ đa dạng:**
   - Nhiều format hình ảnh
   - Nhiều vị trí thư mục
   - Cấu trúc folder phức tạp

4. **Logging chi tiết:**
   - Track quá trình upload
   - Track quá trình chèn
   - Debug dễ dàng

## Giới hạn hiện tại

1. **Vị trí chèn:**
   - Hiện tại chèn ở đầu document
   - Google Docs API phức tạp cho việc chèn tại vị trí chính xác

2. **Kích thước hình ảnh:**
   - Fixed size: 400x300 pt
   - Chưa có auto-resize dựa trên original size

3. **Format hỗ trợ:**
   - Chỉ inline images
   - Chưa hỗ trợ image captions

## Cải tiến tương lai

1. **Vị trí chèn chính xác:**
   - Tính toán index chính xác trong document
   - Chèn hình ảnh đúng vị trí trong text

2. **Smart resizing:**
   - Detect original image size
   - Auto-resize phù hợp với document

3. **Advanced features:**
   - Image captions
   - Image alignment
   - Image borders và effects

## Files đã thay đổi

- `obsidian_to_google_sync.py` - Core improvements
- `test_google_sync.py` - Added image processing test
- `demo_image_sync.py` - New demo script
- `GOOGLE_SYNC_README.md` - Updated documentation
- `README.md` - Updated features
- `sync.mk` - Added demo command
- `IMAGE_SYNC_IMPROVEMENTS.md` - This file
