#!/usr/bin/env python3
"""
Obsidian to Google Docs/Drive Sync Script
Synchronizes notes and media from Obsidian vault to Google Docs and Google Drive
"""

import os
import re
import json
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime
import argparse

# Google API imports
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload

import markdown
from bs4 import BeautifulSoup
import grapheme

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('google_sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def grapheme_len(text: str) -> int:
    """
    Calculate the length of text in grapheme clusters (user-perceived characters)
    instead of Unicode code points. This is essential for Google Docs API
    which expects indices based on grapheme clusters.

    Args:
        text: The text to measure

    Returns:
        Number of grapheme clusters in the text
    """
    return grapheme.length(text)

def clean_whitespace_lines(content: str) -> str:
    """
    Clean up lines that contain only whitespace (spaces, tabs) and convert them to empty lines.
    This ensures consistent formatting in the final Google Doc.

    Args:
        content: The markdown content to clean

    Returns:
        Cleaned content with whitespace-only lines converted to empty lines
    """
    lines = content.split('\n')
    cleaned_lines = []

    for line in lines:
        # If line contains only whitespace (spaces, tabs, etc.), make it empty
        if line.strip() == '':
            cleaned_lines.append('')
        else:
            cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)

class ObsidianToGoogleSync:
    def __init__(self, obsidian_path: str, credentials_path: str):
        """
        Initialize the Google sync tool
        
        Args:
            obsidian_path: Path to Obsidian vault
            credentials_path: Path to Google OAuth2 credentials JSON file
        """
        self.obsidian_path = Path(obsidian_path)
        self.credentials_path = credentials_path
        self.creds = None
        self.drive_service = None
        self.docs_service = None
        
        # Google API scopes
        self.scopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/documents'
        ]
        
        # Supported media types
        self.supported_media = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.pdf', '.mp4', '.mp3', '.wav'}
        
        # Folder mapping: obsidian_path -> google_drive_folder_id
        self.folder_mapping = {}

        # Track upload failures and existing files
        self.failed_uploads = []
        self.existing_files = {}  # filename -> file_id mapping
        
    def authenticate(self) -> bool:
        """Authenticate with Google APIs using OAuth2"""
        try:
            token_path = 'google_token.json'
            
            # Load existing token if available
            if os.path.exists(token_path):
                self.creds = Credentials.from_authorized_user_file(token_path, self.scopes)
            
            # If there are no (valid) credentials available, let the user log in
            if not self.creds or not self.creds.valid:
                if self.creds and self.creds.expired and self.creds.refresh_token:
                    self.creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_path):
                        logger.error(f"Credentials file not found: {self.credentials_path}")
                        logger.error("Please download credentials.json from Google Cloud Console")
                        return False
                        
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_path, self.scopes)
                    self.creds = flow.run_local_server(port=0)
                
                # Save the credentials for the next run
                with open(token_path, 'w') as token:
                    token.write(self.creds.to_json())
            
            # Build service objects
            self.drive_service = build('drive', 'v3', credentials=self.creds)
            self.docs_service = build('docs', 'v1', credentials=self.creds)
            
            logger.info("Google authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Create a folder in Google Drive"""
        try:
            folder_metadata = {
                'name': name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if parent_id:
                folder_metadata['parents'] = [parent_id]
            
            folder = self.drive_service.files().create(
                body=folder_metadata,
                fields='id'
            ).execute()
            
            folder_id = folder.get('id')
            logger.info(f"Created folder: {name} (ID: {folder_id})")
            return folder_id
            
        except HttpError as e:
            logger.error(f"Error creating folder {name}: {e}")
            return None
    
    def find_or_create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Find existing folder or create new one"""
        try:
            # Search for existing folder
            query = f"name='{name}' and mimeType='application/vnd.google-apps.folder'"
            if parent_id:
                query += f" and '{parent_id}' in parents"
            
            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name)"
            ).execute()
            
            items = results.get('files', [])
            
            if items:
                folder_id = items[0]['id']
                logger.info(f"Found existing folder: {name} (ID: {folder_id})")
                return folder_id
            else:
                return self.create_folder(name, parent_id)
                
        except HttpError as e:
            logger.error(f"Error finding/creating folder {name}: {e}")
            return None

    def find_existing_file(self, filename: str, folder_id: str) -> Optional[str]:
        """Find existing file in Google Drive folder"""
        try:
            query = f"name='{filename}' and '{folder_id}' in parents and trashed=false"

            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name, modifiedTime)"
            ).execute()

            items = results.get('files', [])

            if items:
                file_info = items[0]  # Get first match
                file_id = file_info['id']
                logger.info(f"Found existing file: {filename} (ID: {file_id})")
                return file_id

            return None

        except HttpError as e:
            logger.error(f"Error searching for file {filename}: {e}")
            return None

    def update_existing_file(self, file_id: str, file_path: Path) -> Optional[Tuple[str, str]]:
        """Update existing file content"""
        try:
            mime_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'
            media = MediaFileUpload(str(file_path), mimetype=mime_type)

            # Update file content
            file = self.drive_service.files().update(
                fileId=file_id,
                media_body=media,
                fields='id,webViewLink'
            ).execute()

            # Ensure file is publicly viewable
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            self.drive_service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()

            # Get public URL for embedding
            public_url = f"https://drive.google.com/uc?id={file_id}"

            logger.info(f"Updated existing file: {file_path.name} (ID: {file_id})")
            return file_id, public_url

        except HttpError as e:
            logger.error(f"Error updating file {file_path.name}: {e}")
            return None

    def upload_media_file(self, file_path: Path, folder_id: str) -> Optional[Tuple[str, str]]:
        """Upload or update a media file to Google Drive and return file_id and public URL"""
        try:
            filename = file_path.name

            # Check if file already exists
            existing_file_id = self.find_existing_file(filename, folder_id)

            if existing_file_id:
                # Update existing file
                logger.info(f"File {filename} already exists, updating...")
                result = self.update_existing_file(existing_file_id, file_path)
                if result:
                    return result
                else:
                    # If update fails, add to failed list and try upload new
                    self.failed_uploads.append({
                        'file_path': str(file_path),
                        'action': 'update',
                        'error': 'Failed to update existing file'
                    })
                    logger.warning(f"Failed to update {filename}, attempting new upload...")

            # Upload new file
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }

            mime_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'
            media = MediaFileUpload(str(file_path), mimetype=mime_type)

            file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,webViewLink'
            ).execute()

            file_id = file.get('id')

            # Make file publicly viewable
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            self.drive_service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()

            # Get public URL for embedding
            public_url = f"https://drive.google.com/uc?id={file_id}"

            # Cache the file info
            self.existing_files[filename] = file_id

            logger.info(f"Uploaded new media file: {filename} (ID: {file_id})")
            return file_id, public_url

        except HttpError as e:
            logger.error(f"Error uploading media file {file_path}: {e}")
            # Add to failed uploads list
            self.failed_uploads.append({
                'file_path': str(file_path),
                'action': 'upload',
                'error': str(e)
            })
            return None
    
    def get_obsidian_notes(self) -> List[Path]:
        """Get all markdown files from Obsidian vault"""
        notes = []
        
        if not self.obsidian_path.exists():
            logger.error(f"Obsidian path does not exist: {self.obsidian_path}")
            return notes
        
        # Find all .md files
        for md_file in self.obsidian_path.rglob("*.md"):
            # Skip hidden folders and files
            if any(part.startswith('.') for part in md_file.parts):
                continue
            notes.append(md_file)
        
        logger.info(f"Found {len(notes)} markdown files in Obsidian vault")
        return notes
    
    def organize_notes_by_folder(self, notes: List[Path]) -> Dict[str, List[Path]]:
        """Organize notes by their folder structure"""
        organized = {}
        
        for note in notes:
            # Get relative path from obsidian root
            rel_path = note.relative_to(self.obsidian_path)
            
            if len(rel_path.parts) == 1:
                # Root level file
                folder_name = "Root"
            else:
                # Use parent folder name
                folder_name = str(rel_path.parent)
            
            if folder_name not in organized:
                organized[folder_name] = []
            organized[folder_name].append(note)
        
        return organized
    
    def find_media_file(self, base_path: Path, media_name: str) -> Optional[Path]:
        """Find media file in common attachment folders"""
        search_folders = [
            base_path,
            base_path / "attachments",
            base_path / "assets", 
            base_path / "media",
            self.obsidian_path / "attachments",
            self.obsidian_path / "assets",
            self.obsidian_path / "media"
        ]
        
        for folder in search_folders:
            if folder.exists():
                media_path = folder / media_name
                if media_path.exists():
                    return media_path
        
        return None

    def convert_markdown_to_docs_requests(self, markdown_content: str, note_path: Path) -> List[Dict]:
        """Convert Obsidian markdown to Google Docs API requests"""
        try:
            # Clean up whitespace-only lines before processing
            cleaned_content = clean_whitespace_lines(markdown_content)

            # Pre-process nested lists to work around markdown library limitations
            processed_content = self.preprocess_nested_lists(cleaned_content)

            # Convert basic markdown to HTML first
            html = markdown.markdown(processed_content, extensions=['tables', 'fenced_code'])
            soup = BeautifulSoup(html, 'html.parser')

            requests = []
            # Start at index 1 - Google Docs documents start with a newline at index 0
            # and content should be inserted starting at index 1
            index = 1

            # First, extract and process lists directly from markdown before HTML conversion
            list_blocks, non_list_content = self.extract_list_blocks_from_markdown(processed_content)

            # Convert non-list content to HTML
            html = markdown.markdown(non_list_content, extensions=['tables', 'fenced_code'])
            soup = BeautifulSoup(html, 'html.parser')

            # Process each element in the HTML (excluding lists which we handle separately)
            for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'table', 'pre']):
                if element.name.startswith('h'):
                    # Handle headers
                    level = int(element.name[1])
                    text = element.get_text()

                    requests.append({
                        'insertText': {
                            'location': {'index': index},
                            'text': text + '\n'
                        }
                    })

                    # Apply heading style
                    text_length = grapheme_len(text)
                    requests.append({
                        'updateParagraphStyle': {
                            'range': {
                                'startIndex': index,
                                'endIndex': index + text_length
                            },
                            'paragraphStyle': {
                                'namedStyleType': f'HEADING_{level}'
                            },
                            'fields': 'namedStyleType'
                        }
                    })

                    index += text_length + 1

                elif element.name == 'p':
                    # Handle paragraphs
                    text = self.process_paragraph_formatting(element)
                    if text.strip():
                        requests.append({
                            'insertText': {
                                'location': {'index': index},
                                'text': text + '\n'
                            }
                        })
                        index += grapheme_len(text) + 1

                elif element.name == 'table':
                    # Handle tables
                    table_requests, table_length = self.process_table(element, index)
                    requests.extend(table_requests)
                    index += table_length

                elif element.name == 'pre':
                    # Handle code blocks
                    code_text = element.get_text()
                    requests.append({
                        'insertText': {
                            'location': {'index': index},
                            'text': code_text + '\n'
                        }
                    })

                    # Apply monospace formatting
                    code_text_length = grapheme_len(code_text)
                    requests.append({
                        'updateTextStyle': {
                            'range': {
                                'startIndex': index,
                                'endIndex': index + code_text_length
                            },
                            'textStyle': {
                                'weightedFontFamily': {
                                    'fontFamily': 'Courier New'
                                },
                                'backgroundColor': {
                                    'color': {
                                        'rgbColor': {
                                            'red': 0.95,
                                            'green': 0.95,
                                            'blue': 0.95
                                        }
                                    }
                                }
                            },
                            'fields': 'weightedFontFamily,backgroundColor'
                        }
                    })

                    index += code_text_length + 1

            # Process list blocks separately with proper nesting
            for list_block in list_blocks:
                list_requests, list_length = self.process_markdown_list_block(list_block, index)
                requests.extend(list_requests)
                index += list_length

            # Handle Obsidian-specific syntax (links, etc. - images are handled separately)
            requests = self.process_obsidian_links(requests, markdown_content)

            return requests

        except Exception as e:
            logger.error(f"Error converting markdown: {e}")
            return []

    def process_paragraph_formatting(self, element) -> str:
        """Process paragraph with inline formatting"""
        text = ""
        for content in element.contents:
            if hasattr(content, 'name'):
                if content.name == 'strong' or content.name == 'b':
                    text += content.get_text()
                elif content.name == 'em' or content.name == 'i':
                    text += content.get_text()
                elif content.name == 'code':
                    text += content.get_text()
                elif content.name == 'a':
                    text += content.get_text()
                else:
                    text += content.get_text()
            else:
                text += str(content)
        return text

    def process_list(self, element, start_index: int, level: int = 0) -> Tuple[List[Dict], int]:
        """Process list elements with proper nested list support"""
        requests = []
        total_length = 0

        # Process only direct children li elements (not nested ones)
        direct_li_elements = element.find_all('li', recursive=False)

        for li in direct_li_elements:
            # Extract text content excluding nested lists
            item_text = self.extract_list_item_text(li)

            if item_text.strip():
                text_with_newline = item_text + '\n'
                text_length = grapheme_len(text_with_newline)

                # Insert the text
                requests.append({
                    'insertText': {
                        'location': {'index': start_index + total_length},
                        'text': text_with_newline
                    }
                })

                # Apply appropriate bullet formatting based on level
                bullet_preset = self.get_bullet_preset_for_level(level)
                requests.append({
                    'createParagraphBullets': {
                        'range': {
                            'startIndex': start_index + total_length,
                            'endIndex': start_index + total_length + text_length - 1
                        },
                        'bulletPreset': bullet_preset
                    }
                })

                total_length += text_length

            # Process nested lists within this li element
            nested_lists = li.find_all(['ul', 'ol'], recursive=False)
            for nested_list in nested_lists:
                nested_requests, nested_length = self.process_list(
                    nested_list,
                    start_index + total_length,
                    level + 1
                )
                requests.extend(nested_requests)
                total_length += nested_length

        return requests, total_length

    def preprocess_nested_lists(self, content: str) -> str:
        """
        Preprocess markdown content to better handle nested lists.
        The standard markdown library doesn't handle nested lists well,
        so we'll add some preprocessing to improve the structure.
        """
        lines = content.split('\n')
        processed_lines = []

        i = 0
        while i < len(lines):
            line = lines[i]

            # Check if this is a list item
            if self.is_list_item(line):
                # Process this list item and any following nested items
                list_block, next_i = self.process_list_block(lines, i)
                processed_lines.extend(list_block)
                i = next_i
            else:
                processed_lines.append(line)
                i += 1

        return '\n'.join(processed_lines)

    def is_list_item(self, line: str) -> bool:
        """Check if a line is a list item"""
        stripped = line.lstrip()
        # Unordered list: starts with -, *, or +
        if stripped.startswith(('- ', '* ', '+ ')):
            return True
        # Ordered list: starts with number followed by . or )
        import re
        if re.match(r'^\d+[.)]\s', stripped):
            return True
        return False

    def get_list_indent_level(self, line: str) -> int:
        """Get the indentation level of a list item"""
        return len(line) - len(line.lstrip())

    def process_list_block(self, lines: List[str], start_index: int) -> Tuple[List[str], int]:
        """Process a block of list items, maintaining proper nesting"""
        processed_lines = []
        i = start_index

        while i < len(lines):
            line = lines[i]

            if not line.strip():
                # Empty line - might be end of list or just spacing
                processed_lines.append(line)
                i += 1
                continue

            if self.is_list_item(line):
                # This is a list item, add it as-is
                processed_lines.append(line)
                i += 1
            else:
                # Not a list item - check if it's continuation of previous item
                if i > start_index and line.startswith('  '):
                    # Looks like continuation, keep it
                    processed_lines.append(line)
                    i += 1
                else:
                    # End of list block
                    break

        return processed_lines, i

    def extract_list_blocks_from_markdown(self, content: str) -> Tuple[List[List[str]], str]:
        """Extract list blocks from markdown and return them separately from other content"""
        lines = content.split('\n')
        list_blocks = []
        non_list_lines = []

        i = 0
        while i < len(lines):
            line = lines[i]

            if self.is_list_item(line):
                # Start of a list block
                list_block = []
                base_indent = self.get_list_indent_level(line)

                # Collect all lines that belong to this list block
                while i < len(lines):
                    current_line = lines[i]

                    if not current_line.strip():
                        # Empty line - might be part of list or end of list
                        list_block.append(current_line)
                        i += 1
                        continue

                    if self.is_list_item(current_line):
                        # Another list item
                        current_indent = self.get_list_indent_level(current_line)
                        if current_indent >= base_indent:
                            # Part of the same list block
                            list_block.append(current_line)
                            i += 1
                        else:
                            # New list block at different level
                            break
                    else:
                        # Check if it's continuation of a list item
                        if current_line.startswith('  '):
                            list_block.append(current_line)
                            i += 1
                        else:
                            # End of list block
                            break

                if list_block:
                    list_blocks.append(list_block)
                    # Add placeholder for list position
                    non_list_lines.append(f"__LIST_BLOCK_{len(list_blocks)-1}__")
            else:
                non_list_lines.append(line)
                i += 1

        return list_blocks, '\n'.join(non_list_lines)

    def process_markdown_list_block(self, list_lines: List[str], start_index: int) -> Tuple[List[Dict], int]:
        """Process a list block directly from markdown lines"""
        requests = []
        total_length = 0

        # Parse the list structure
        list_items = self.parse_list_structure(list_lines)

        # Convert each item to Google Docs requests
        for item in list_items:
            text = item['text'] + '\n'
            text_length = grapheme_len(text)

            # Insert text
            requests.append({
                'insertText': {
                    'location': {'index': start_index + total_length},
                    'text': text
                }
            })

            # Apply bullet formatting based on level
            bullet_preset = self.get_bullet_preset_for_level(item['level'])
            requests.append({
                'createParagraphBullets': {
                    'range': {
                        'startIndex': start_index + total_length,
                        'endIndex': start_index + total_length + text_length - 1
                    },
                    'bulletPreset': bullet_preset
                }
            })

            total_length += text_length

        return requests, total_length

    def parse_list_structure(self, list_lines: List[str]) -> List[Dict]:
        """Parse list lines into structured items with levels"""
        items = []

        for line in list_lines:
            if not line.strip():
                continue

            if self.is_list_item(line):
                indent_level = self.get_list_indent_level(line)
                # Convert indent to nesting level (assuming 2 spaces per level)
                nesting_level = indent_level // 2

                # Extract text content
                stripped = line.lstrip()
                # Remove list marker
                if stripped.startswith(('- ', '* ', '+ ')):
                    text = stripped[2:].strip()
                else:
                    # Ordered list - remove number and dot/paren
                    import re
                    match = re.match(r'^\d+[.)]\s*(.*)$', stripped)
                    if match:
                        text = match.group(1).strip()
                    else:
                        text = stripped

                items.append({
                    'text': text,
                    'level': nesting_level,
                    'indent': indent_level
                })

        return items

    def extract_list_item_text(self, li_element) -> str:
        """Extract text content from li element, excluding nested lists"""
        text_parts = []

        for child in li_element.children:
            if hasattr(child, 'name'):
                # Skip nested ul/ol elements
                if child.name in ['ul', 'ol']:
                    continue
                else:
                    # Include other elements (like <strong>, <em>, etc.)
                    text_parts.append(child.get_text())
            else:
                # Text node
                text_content = str(child).strip()
                if text_content:
                    text_parts.append(text_content)

        return ''.join(text_parts).strip()

    def get_bullet_preset_for_level(self, level: int) -> str:
        """Get appropriate bullet preset based on nesting level"""
        # Google Docs supports different bullet styles for different levels
        bullet_presets = [
            'BULLET_DISC_CIRCLE_SQUARE',      # Level 0: •
            'BULLET_DIAMONDX_ARROW3D_SQUARE', # Level 1: ◆
            'BULLET_CHECKBOX',                 # Level 2: ☐
            'BULLET_ARROW_DIAMOND_DISC',      # Level 3: ➤
            'BULLET_STAR_CIRCLE_SQUARE',      # Level 4: ★
            'BULLET_DISC_CIRCLE_SQUARE'       # Level 5+: back to •
        ]

        # Use modulo to cycle through presets for very deep nesting
        return bullet_presets[level % len(bullet_presets)]

    def process_table(self, element, start_index: int) -> Tuple[List[Dict], int]:
        """Process table elements with proper Google Docs table support"""
        requests = []

        # Extract table data
        table_data = self.extract_table_data(element)

        if not table_data or len(table_data) == 0:
            # Empty table, just add a placeholder
            requests.append({
                'insertText': {
                    'location': {'index': start_index},
                    'text': '[Empty Table]\n'
                }
            })
            return requests, grapheme_len('[Empty Table]') + 1

        rows = len(table_data)
        cols = max(len(row) for row in table_data) if table_data else 1

        # For now, use simple text-based table as Google Docs table API is complex
        # and requires careful handling of table structure and cell references
        logger.debug(f"Processing table with {rows} rows and {cols} columns")

        # Use fallback text-based table formatting
        return self.create_simple_table_fallback(table_data, start_index)

    def extract_table_data(self, table_element) -> List[List[str]]:
        """Extract table data from HTML table element"""
        table_data = []

        # Find all rows
        rows = table_element.find_all('tr')

        for row in rows:
            row_data = []
            # Find all cells (th or td)
            cells = row.find_all(['th', 'td'])

            for cell in cells:
                # Get cell text and clean it up
                cell_text = cell.get_text().strip()
                # Handle line breaks within cells
                cell_text = cell_text.replace('\n', ' ').replace('\r', ' ')
                # Collapse multiple spaces
                cell_text = ' '.join(cell_text.split())
                row_data.append(cell_text)

            if row_data:  # Only add non-empty rows
                table_data.append(row_data)

        return table_data

    def create_simple_table_fallback(self, table_data: List[List[str]], start_index: int) -> Tuple[List[Dict], int]:
        """Create a simple text-based table as fallback when Google Docs table API fails"""
        requests = []
        table_text = ""

        if not table_data:
            return requests, 0

        # Calculate column widths for better formatting
        col_widths = []
        max_cols = max(len(row) for row in table_data) if table_data else 0

        for col_idx in range(max_cols):
            max_width = 0
            for row in table_data:
                if col_idx < len(row):
                    max_width = max(max_width, len(row[col_idx]))
            col_widths.append(min(max_width, 20))  # Cap at 20 characters

        # Create table header separator
        separator = "+" + "+".join(["-" * (width + 2) for width in col_widths]) + "+\n"

        # Format each row
        for row_idx, row_data in enumerate(table_data):
            # Add separator before first row and after header
            if row_idx == 0 or row_idx == 1:
                table_text += separator

            # Format row
            formatted_cells = []
            for col_idx in range(max_cols):
                cell_text = row_data[col_idx] if col_idx < len(row_data) else ""
                width = col_widths[col_idx]
                formatted_cells.append(f" {cell_text:<{width}} ")

            table_text += "|" + "|".join(formatted_cells) + "|\n"

        # Add final separator
        table_text += separator + "\n"

        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': table_text
            }
        })

        return requests, grapheme_len(table_text)

    def process_obsidian_links(self, requests: List[Dict], markdown_content: str) -> List[Dict]:
        """Process Obsidian-specific syntax like [[links]]"""
        # Handle Obsidian internal links [[link]]
        # For now, we'll just keep them as text since Google Docs doesn't have equivalent
        # In the future, this could be enhanced to create actual links between documents

        return requests

    def validate_requests(self, requests: List[Dict]) -> List[Dict]:
        """Validate and clean requests before sending to Google Docs API"""
        valid_requests = []

        for i, request in enumerate(requests):
            try:
                # Check for common issues
                if 'updateTextStyle' in request:
                    text_style = request['updateTextStyle'].get('textStyle', {})

                    # Ensure fontFamily is in correct format
                    if 'fontFamily' in text_style:
                        font_family = text_style.pop('fontFamily')
                        text_style['weightedFontFamily'] = {'fontFamily': font_family}

                        # Update fields
                        fields = request['updateTextStyle'].get('fields', '')
                        fields = fields.replace('fontFamily', 'weightedFontFamily')
                        request['updateTextStyle']['fields'] = fields

                # Validate insertion indices for grapheme cluster issues
                if 'insertText' in request:
                    location = request['insertText'].get('location', {})
                    index = location.get('index', 0)
                    text = request['insertText'].get('text', '')

                    # Log details for debugging
                    logger.debug(f"Request {i}: insertText at index {index}, text length: {len(text)}, grapheme length: {grapheme_len(text)}")

                    # Ensure index is not negative and handle edge cases
                    if index < 0:
                        logger.warning(f"Request {i}: Negative index {index}, setting to 1")
                        location['index'] = 1
                    elif index == 0:
                        # Google Docs documents start at index 1, not 0
                        logger.debug(f"Request {i}: Index 0 changed to 1")
                        location['index'] = 1

                valid_requests.append(request)

            except Exception as e:
                logger.warning(f"Skipping invalid request {i}: {e}")
                logger.warning(f"Request content: {request}")
                continue

        logger.info(f"Validated {len(valid_requests)}/{len(requests)} requests")
        return valid_requests

    def find_existing_doc(self, title: str, folder_id: str) -> Optional[str]:
        """Find existing Google Doc in folder"""
        try:
            query = f"name='{title}' and '{folder_id}' in parents and mimeType='application/vnd.google-apps.document' and trashed=false"

            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name, modifiedTime)"
            ).execute()

            items = results.get('files', [])

            if items:
                doc_info = items[0]  # Get first match
                doc_id = doc_info['id']
                logger.info(f"Found existing document: {title} (ID: {doc_id})")
                return doc_id

            return None

        except HttpError as e:
            logger.error(f"Error searching for document {title}: {e}")
            return None

    def clear_document_content(self, doc_id: str) -> bool:
        """Clear all content from existing document"""
        try:
            # Get document to find content length
            document = self.docs_service.documents().get(documentId=doc_id).execute()
            content = document.get('body', {}).get('content', [])

            # Find the end index of content
            end_index = 1
            for element in content:
                if 'endIndex' in element:
                    end_index = max(end_index, element['endIndex'])

            if end_index > 1:
                # Delete all content except the first character (required by API)
                requests = [{
                    'deleteContentRange': {
                        'range': {
                            'startIndex': 1,
                            'endIndex': end_index - 1
                        }
                    }
                }]

                self.docs_service.documents().batchUpdate(
                    documentId=doc_id,
                    body={'requests': requests}
                ).execute()

                logger.info(f"Cleared content from document: {doc_id}")

            return True

        except HttpError as e:
            logger.error(f"Error clearing document content {doc_id}: {e}")
            return False

    def insert_image_into_doc(self, doc_id: str, image_url: str, image_name: str, insert_index: int = 1) -> bool:
        """Insert an image into Google Doc at specified position"""
        try:
            # Insert image request
            requests = [{
                'insertInlineImage': {
                    'location': {
                        'index': insert_index
                    },
                    'uri': image_url,
                    'objectSize': {
                        'height': {
                            'magnitude': 300,
                            'unit': 'PT'
                        },
                        'width': {
                            'magnitude': 400,
                            'unit': 'PT'
                        }
                    }
                }
            }]

            self.docs_service.documents().batchUpdate(
                documentId=doc_id,
                body={'requests': requests}
            ).execute()

            logger.info(f"Inserted image {image_name} into document")
            return True

        except HttpError as e:
            logger.error(f"Error inserting image {image_name}: {e}")
            return False

    def process_images_in_markdown(self, markdown_content: str, note_path: Path) -> Tuple[str, List[Tuple[str, Path, str]]]:
        """Process markdown and return modified content with image placeholders"""
        image_positions = []
        # Clean whitespace-only lines first
        modified_content = clean_whitespace_lines(markdown_content)

        # Find embedded images and their positions
        embedded_image_pattern = r'!\[\[([^\]]+)\]\]'

        for match in re.finditer(embedded_image_pattern, markdown_content):
            image_name = match.group(1)
            image_path = self.find_media_file(note_path.parent, image_name)

            if image_path and image_path.exists():
                # Create a unique placeholder that we can find later
                placeholder_id = f"IMAGE_PLACEHOLDER_{len(image_positions)}"
                image_positions.append((image_name, image_path, placeholder_id))

                logger.info(f"Found embedded image: {image_name}")

        # Replace all image syntax with placeholders
        for image_name, image_path, placeholder_id in image_positions:
            image_pattern = rf'!\[\[{re.escape(image_name)}\]\]'
            placeholder_text = f"\n[{placeholder_id}]\n"
            modified_content = re.sub(image_pattern, placeholder_text, modified_content)

        return modified_content, image_positions

    def calculate_image_positions_in_doc(self, content_requests: List[Dict], image_positions: List[Tuple[str, Path, str]]) -> Tuple[List[Tuple[str, Path, int]], List[Dict]]:
        """Calculate the exact positions where images should be inserted and remove placeholders from requests"""
        image_positions_with_indices = []
        updated_requests = []

        # Build a map of text content and their positions
        current_index = 1  # Google Docs start at index 1
        text_content = ""

        # Process all insertText requests to build the full text content
        for request in content_requests:
            if 'insertText' in request:
                text = request['insertText']['text']
                text_content += text

        # Find placeholder positions and prepare for removal
        placeholder_ranges = []  # Store ranges to remove

        for image_name, image_path, placeholder_id in image_positions:
            placeholder_text = f"[{placeholder_id}]"
            placeholder_pos = text_content.find(placeholder_text)

            if placeholder_pos != -1:
                # Calculate the actual index in the Google Doc
                # We need to account for grapheme clusters
                text_before_placeholder = text_content[:placeholder_pos]
                doc_index = 1 + grapheme_len(text_before_placeholder)

                image_positions_with_indices.append((image_name, image_path, doc_index))

                # Store placeholder range for removal
                placeholder_start = doc_index
                placeholder_end = doc_index + grapheme_len(placeholder_text)
                placeholder_ranges.append((placeholder_start, placeholder_end, placeholder_text))

                logger.debug(f"Image {image_name} will be inserted at index {doc_index}, placeholder range: {placeholder_start}-{placeholder_end}")
            else:
                logger.warning(f"Could not find placeholder for image: {image_name}")

        # Create updated requests with placeholders removed
        updated_requests = self.remove_placeholders_from_requests(content_requests, placeholder_ranges)

        return image_positions_with_indices, updated_requests

    def remove_placeholders_from_requests(self, content_requests: List[Dict], placeholder_ranges: List[Tuple[int, int, str]]) -> List[Dict]:
        """Remove image placeholders from content requests"""
        if not placeholder_ranges:
            return content_requests

        updated_requests = []
        current_index = 1

        for request in content_requests:
            if 'insertText' in request:
                text = request['insertText']['text']
                location_index = request['insertText']['location']['index']

                # Check if this text contains any placeholders
                updated_text = text
                for placeholder_start, placeholder_end, placeholder_text in placeholder_ranges:
                    # Remove placeholder from text
                    updated_text = updated_text.replace(placeholder_text, '')

                # Only add request if there's still text after removing placeholders
                if updated_text.strip():
                    updated_request = {
                        'insertText': {
                            'location': {'index': location_index},
                            'text': updated_text
                        }
                    }
                    updated_requests.append(updated_request)
            else:
                # Non-text requests, keep as-is
                updated_requests.append(request)

        return updated_requests

    def update_document_content(self, doc_id: str, updated_requests: List[Dict]):
        """Update document content by replacing it with updated requests (without placeholders)"""
        try:
            # Get current document to find content length
            doc = self.docs_service.documents().get(documentId=doc_id).execute()
            content = doc.get('body', {}).get('content', [])

            # Find the end index of the document content
            end_index = 1  # Start with minimum
            for element in content:
                if 'endIndex' in element:
                    end_index = max(end_index, element['endIndex'])

            # Delete all current content (except the first character which is required)
            if end_index > 1:
                delete_request = {
                    'deleteContentRange': {
                        'range': {
                            'startIndex': 1,
                            'endIndex': end_index - 1  # Keep the last newline
                        }
                    }
                }

                self.docs_service.documents().batchUpdate(
                    documentId=doc_id,
                    body={'requests': [delete_request]}
                ).execute()

                logger.debug(f"Deleted content from index 1 to {end_index - 1}")

            # Insert updated content with recalculated indices
            if updated_requests:
                # Recalculate all indices to be sequential starting from index 1
                recalculated_requests = self.recalculate_all_indices(updated_requests)

                # Validate requests before sending
                validated_requests = self.validate_requests(recalculated_requests)

                if validated_requests:
                    self.docs_service.documents().batchUpdate(
                        documentId=doc_id,
                        body={'requests': validated_requests}
                    ).execute()

                    logger.info(f"Updated document content with {len(validated_requests)} requests")

        except Exception as e:
            logger.error(f"Error updating document content: {e}")

    def recalculate_all_indices(self, requests: List[Dict]) -> List[Dict]:
        """
        Recalculate all indices in requests to be sequential starting from index 1.
        Handles insertText, updateParagraphStyle, updateTextStyle, createParagraphBullets, and insertInlineImage requests.
        """
        recalculated_requests = []
        current_index = 1  # Start from index 1 (Google Docs requirement)

        for request in requests:
            new_request = request.copy()

            if 'insertText' in request:
                # Handle insertText requests
                text = request['insertText']['text']
                text_length = grapheme_len(text)

                new_request['insertText'] = {
                    'location': {'index': current_index},
                    'text': text
                }

                logger.debug(f"Recalculated insertText: index={current_index}, text_length={text_length}, next_index={current_index + text_length}")

                # Store the range for potential formatting requests that follow
                text_start = current_index
                text_end = current_index + text_length
                current_index += text_length

            elif 'updateParagraphStyle' in request:
                # Handle updateParagraphStyle requests - apply to the most recently inserted text
                original_range = request['updateParagraphStyle'].get('range', {})
                original_length = original_range.get('endIndex', 0) - original_range.get('startIndex', 0)

                # Apply to the text that was just inserted (excluding newline)
                new_start = current_index - original_length if original_length > 0 else current_index - 1
                new_end = current_index - 1 if current_index > 1 else current_index

                new_request['updateParagraphStyle'] = request['updateParagraphStyle'].copy()
                new_request['updateParagraphStyle']['range'] = {
                    'startIndex': max(1, new_start),
                    'endIndex': max(1, new_end)
                }

                logger.debug(f"Recalculated updateParagraphStyle: range={max(1, new_start)}-{max(1, new_end)}")

            elif 'updateTextStyle' in request:
                # Handle updateTextStyle requests - apply to the most recently inserted text
                original_range = request['updateTextStyle'].get('range', {})
                original_length = original_range.get('endIndex', 0) - original_range.get('startIndex', 0)

                # Apply to the text that was just inserted
                new_start = current_index - original_length if original_length > 0 else current_index - 1
                new_end = current_index - 1 if current_index > 1 else current_index

                new_request['updateTextStyle'] = request['updateTextStyle'].copy()
                new_request['updateTextStyle']['range'] = {
                    'startIndex': max(1, new_start),
                    'endIndex': max(1, new_end)
                }

                logger.debug(f"Recalculated updateTextStyle: range={max(1, new_start)}-{max(1, new_end)}")

            elif 'createParagraphBullets' in request:
                # Handle createParagraphBullets requests - apply to the most recently inserted text
                original_range = request['createParagraphBullets'].get('range', {})
                original_length = original_range.get('endIndex', 0) - original_range.get('startIndex', 0)

                # Apply to the text that was just inserted (excluding newline)
                new_start = current_index - original_length if original_length > 0 else current_index - 1
                new_end = current_index - 1 if current_index > 1 else current_index

                new_request['createParagraphBullets'] = request['createParagraphBullets'].copy()
                new_request['createParagraphBullets']['range'] = {
                    'startIndex': max(1, new_start),
                    'endIndex': max(1, new_end)
                }

                logger.debug(f"Recalculated createParagraphBullets: range={max(1, new_start)}-{max(1, new_end)}")

            elif 'insertInlineImage' in request:
                # Handle insertInlineImage requests
                new_request['insertInlineImage'] = request['insertInlineImage'].copy()
                new_request['insertInlineImage']['location'] = {'index': current_index}

                logger.debug(f"Recalculated insertInlineImage: index={current_index}")
                # Images don't add to text length, so current_index stays the same

            # Add the recalculated request
            recalculated_requests.append(new_request)

        return recalculated_requests

    def create_google_doc(self, title: str, content_requests: List[Dict], folder_id: str) -> Optional[str]:
        """Create or update a Google Doc with formatted content"""
        try:
            # Check if document already exists
            existing_doc_id = self.find_existing_doc(title, folder_id)

            if existing_doc_id:
                # Update existing document
                logger.info(f"Document {title} already exists, updating content...")

                # Clear existing content
                if self.clear_document_content(existing_doc_id):
                    doc_id = existing_doc_id
                    logger.info(f"Using existing Google Doc: {title} (ID: {doc_id})")
                else:
                    # If clearing fails, add to failed list and create new
                    self.failed_uploads.append({
                        'file_path': title,
                        'action': 'update_doc',
                        'error': 'Failed to clear existing document content'
                    })
                    logger.warning(f"Failed to update {title}, creating new document...")
                    existing_doc_id = None

            if not existing_doc_id:
                # Create new document
                doc = {
                    'title': title
                }

                document = self.docs_service.documents().create(body=doc).execute()
                doc_id = document.get('documentId')
                logger.info(f"Created new Google Doc: {title} (ID: {doc_id})")

                # Move document to the specified folder
                if folder_id:
                    self.drive_service.files().update(
                        fileId=doc_id,
                        addParents=folder_id,
                        removeParents='root'
                    ).execute()
                    logger.info(f"Moved document to folder: {folder_id}")

            # Apply content formatting if there are requests
            if content_requests:
                logger.info(f"Applying {len(content_requests)} formatting requests...")

                # Validate requests before sending
                validated_requests = self.validate_requests(content_requests)
                logger.info(f"Validated {len(validated_requests)}/{len(content_requests)} requests")

                if validated_requests:
                    try:
                        self.docs_service.documents().batchUpdate(
                            documentId=doc_id,
                            body={'requests': validated_requests}
                        ).execute()
                        logger.info(f"Successfully applied formatting to document: {title}")
                    except HttpError as format_error:
                        logger.error(f"Error applying formatting to {title}: {format_error}")
                        logger.error(f"Problematic requests: {validated_requests}")

                        # Try a fallback approach for grapheme cluster errors
                        if "grapheme cluster" in str(format_error):
                            logger.info(f"Attempting fallback approach for grapheme cluster error...")
                            fallback_success = self.apply_content_with_fallback(doc_id, validated_requests, title)
                            if fallback_success:
                                return doc_id

                        # Add to failed list
                        self.failed_uploads.append({
                            'file_path': title,
                            'action': 'format_doc',
                            'error': str(format_error)
                        })
                        # Document is created but without formatting
                        return doc_id
                else:
                    logger.warning(f"No valid requests to apply for document: {title}")

            logger.info(f"Successfully processed Google Doc: {title}")
            return doc_id

        except HttpError as e:
            logger.error(f"Error processing Google Doc {title}: {e}")
            # Add to failed list
            self.failed_uploads.append({
                'file_path': title,
                'action': 'create_doc',
                'error': str(e)
            })
            return None

    def apply_content_with_fallback(self, doc_id: str, requests: List[Dict], title: str) -> bool:
        """
        Fallback method to apply content when grapheme cluster errors occur.
        Tries to apply requests one by one and skips problematic ones.
        """
        try:
            logger.info(f"Applying {len(requests)} requests individually as fallback...")
            successful_requests = 0

            for i, request in enumerate(requests):
                try:
                    # Apply each request individually
                    self.docs_service.documents().batchUpdate(
                        documentId=doc_id,
                        body={'requests': [request]}
                    ).execute()
                    successful_requests += 1

                except HttpError as e:
                    if "grapheme cluster" in str(e):
                        logger.warning(f"Skipping request {i} due to grapheme cluster error: {request}")
                        # Try to adjust the request if it's an insertText
                        if 'insertText' in request:
                            adjusted_request = self.adjust_insert_request(request)
                            if adjusted_request:
                                try:
                                    self.docs_service.documents().batchUpdate(
                                        documentId=doc_id,
                                        body={'requests': [adjusted_request]}
                                    ).execute()
                                    successful_requests += 1
                                    logger.info(f"Successfully applied adjusted request {i}")
                                except HttpError:
                                    logger.warning(f"Adjusted request {i} also failed, skipping")
                    else:
                        logger.warning(f"Skipping request {i} due to error: {e}")

            logger.info(f"Fallback completed: {successful_requests}/{len(requests)} requests applied")
            return successful_requests > 0

        except Exception as e:
            logger.error(f"Fallback method failed: {e}")
            return False

    def adjust_insert_request(self, request: Dict) -> Optional[Dict]:
        """
        Adjust an insertText request to avoid grapheme cluster issues.
        """
        if 'insertText' not in request:
            return None

        try:
            # Create a copy of the request
            adjusted = request.copy()
            location = adjusted['insertText']['location']
            current_index = location.get('index', 1)

            # Try moving the index to a safer position
            # Google Docs documents typically start at index 1
            if current_index < 1:
                location['index'] = 1
                logger.debug(f"Adjusted index from {current_index} to 1")
                return adjusted

            # For other cases, try the next index
            location['index'] = current_index + 1
            logger.debug(f"Adjusted index from {current_index} to {current_index + 1}")
            return adjusted

        except Exception as e:
            logger.error(f"Error adjusting request: {e}")
            return None

    def setup_folder_structure(self, root_folder_name: str = "Obsidian Sync") -> Optional[str]:
        """Setup the main folder structure in Google Drive"""
        try:
            # Create or find root folder
            root_folder_id = self.find_or_create_folder(root_folder_name)
            if not root_folder_id:
                return None

            # Create media subfolder
            media_folder_id = self.find_or_create_folder("Media", root_folder_id)
            if media_folder_id:
                self.folder_mapping['_media'] = media_folder_id

            self.folder_mapping['_root'] = root_folder_id
            return root_folder_id

        except Exception as e:
            logger.error(f"Error setting up folder structure: {e}")
            return None

    def sync_notes(self, root_folder_name: str = "Obsidian Sync") -> bool:
        """Main sync function"""
        try:
            logger.info("Starting Obsidian to Google Docs/Drive sync...")

            # Authenticate
            if not self.authenticate():
                return False

            # Setup folder structure
            root_folder_id = self.setup_folder_structure(root_folder_name)
            if not root_folder_id:
                return False

            # Get notes from Obsidian
            notes = self.get_obsidian_notes()
            if not notes:
                logger.warning("No notes found in Obsidian vault")
                return True

            # Organize notes by folder
            organized_notes = self.organize_notes_by_folder(notes)

            # Sync each folder
            for folder_name, folder_notes in organized_notes.items():
                logger.info(f"Syncing folder: {folder_name} ({len(folder_notes)} notes)")

                # Create folder in Google Drive
                if folder_name == "Root":
                    folder_id = root_folder_id
                else:
                    folder_id = self.find_or_create_folder(folder_name, root_folder_id)
                    if not folder_id:
                        logger.error(f"Failed to create folder: {folder_name}")
                        continue

                self.folder_mapping[folder_name] = folder_id

                # Sync notes in this folder
                for note_path in folder_notes:
                    self.sync_single_note(note_path, folder_id)

            # Report sync results
            self.report_sync_results()

            logger.info("Sync completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Sync failed: {e}")
            self.report_sync_results()
            return False

    def sync_single_note(self, note_path: Path, folder_id: str) -> bool:
        """Sync a single note to Google Docs"""
        try:
            logger.info(f"Syncing note: {note_path.name}")

            # Read markdown content
            with open(note_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # Process images in markdown and get their positions
            processed_content, image_positions = self.process_images_in_markdown(markdown_content, note_path)

            # Convert to Google Docs format
            content_requests = self.convert_markdown_to_docs_requests(processed_content, note_path)

            # Use filename (without extension) as title
            title = note_path.stem

            # Create Google Doc
            doc_id = self.create_google_doc(title, content_requests, folder_id)

            if doc_id:
                # Calculate exact positions for images and get updated requests without placeholders
                image_positions_with_indices, updated_content_requests = self.calculate_image_positions_in_doc(content_requests, image_positions)

                # If we have updated requests (with placeholders removed), we need to recreate the document
                if updated_content_requests != content_requests:
                    logger.info("Recreating document with placeholders removed...")
                    # Delete the current document content and insert updated content
                    self.update_document_content(doc_id, updated_content_requests)

                # Process and insert embedded images at correct positions
                self.insert_embedded_images(doc_id, image_positions_with_indices)
                return True

            return False

        except Exception as e:
            logger.error(f"Error syncing note {note_path}: {e}")
            return False

    def insert_embedded_images(self, doc_id: str, image_positions: List[Tuple[str, Path, int]]):
        """Upload images to Drive and insert them into Google Doc at correct positions"""
        try:
            media_folder_id = self.folder_mapping.get('_media')
            if not media_folder_id:
                logger.warning("Media folder not found, skipping image insertion")
                return

            # Process images in reverse order to maintain correct positions
            # (inserting from end to beginning prevents index shifting)
            sorted_positions = sorted(image_positions, key=lambda x: x[2], reverse=True)

            for image_name, image_path, position in sorted_positions:
                logger.info(f"Processing image: {image_name} at position {position}")

                # Upload image to Drive
                upload_result = self.upload_media_file(image_path, media_folder_id)
                if upload_result:
                    file_id, public_url = upload_result

                    # Insert image into document at the calculated position
                    success = self.insert_image_into_doc(doc_id, public_url, image_name, position)

                    if success:
                        logger.info(f"Successfully inserted image: {image_name} at index {position}")
                    else:
                        logger.error(f"Failed to insert image: {image_name}")
                else:
                    logger.error(f"Failed to upload image: {image_name}")

        except Exception as e:
            logger.error(f"Error inserting embedded images: {e}")

    def report_sync_results(self):
        """Report sync results and failed uploads"""
        try:
            logger.info("=" * 60)
            logger.info("SYNC RESULTS SUMMARY")
            logger.info("=" * 60)

            if not self.failed_uploads:
                logger.info("✅ All files processed successfully!")
            else:
                logger.warning(f"⚠️  {len(self.failed_uploads)} files failed to process:")

                # Group failures by action type
                failures_by_action = {}
                for failure in self.failed_uploads:
                    action = failure['action']
                    if action not in failures_by_action:
                        failures_by_action[action] = []
                    failures_by_action[action].append(failure)

                # Report each type of failure
                for action, failures in failures_by_action.items():
                    logger.warning(f"\n{action.upper()} FAILURES ({len(failures)}):")
                    for i, failure in enumerate(failures, 1):
                        logger.warning(f"  {i}. {failure['file_path']}")
                        logger.warning(f"     Error: {failure['error']}")

                # Save failed uploads to file for later processing
                failed_file = "failed_uploads.json"
                with open(failed_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(self.failed_uploads, f, indent=2, ensure_ascii=False)

                logger.warning(f"\n💾 Failed uploads saved to: {failed_file}")
                logger.warning("You can review and retry these files later.")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"Error generating sync report: {e}")

    def retry_failed_uploads(self, failed_file: str = "failed_uploads.json"):
        """Retry previously failed uploads"""
        try:
            if not os.path.exists(failed_file):
                logger.info(f"No failed uploads file found: {failed_file}")
                return True

            import json
            with open(failed_file, 'r', encoding='utf-8') as f:
                failed_uploads = json.load(f)

            if not failed_uploads:
                logger.info("No failed uploads to retry")
                return True

            logger.info(f"Retrying {len(failed_uploads)} failed uploads...")

            # Clear current failed list
            self.failed_uploads = []

            # Retry each failed upload
            retry_success = 0
            for failure in failed_uploads:
                file_path = failure['file_path']
                action = failure['action']

                logger.info(f"Retrying {action}: {file_path}")

                # This would need specific retry logic based on action type
                # For now, just log the attempt
                logger.info(f"  Retry logic for {action} not yet implemented")

            logger.info(f"Retry completed: {retry_success}/{len(failed_uploads)} successful")

            # Remove the failed file if all retries succeeded
            if not self.failed_uploads:
                os.remove(failed_file)
                logger.info(f"Removed {failed_file} - all retries successful")

            return True

        except Exception as e:
            logger.error(f"Error retrying failed uploads: {e}")
            return False

    def create_config_file(self, config_path: str = "google_sync_config.json"):
        """Create a configuration file template"""
        config = {
            "obsidian_path": "./obsidian",
            "credentials_path": "credentials.json",
            "root_folder_name": "Obsidian Sync",
            "excluded_folders": [".obsidian", ".trash"],
            "excluded_files": ["*.tmp", "*.bak"]
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)

        logger.info(f"Configuration file created: {config_path}")
        print(f"Please edit {config_path} and add your Google credentials.json file")


def main():
    parser = argparse.ArgumentParser(description="Sync Obsidian notes to Google Docs/Drive")
    parser.add_argument("--obsidian-path", default="./obsidian",
                       help="Path to Obsidian vault (default: ./obsidian)")
    parser.add_argument("--config", default="google_sync_config.json",
                       help="Configuration file path (default: google_sync_config.json)")
    parser.add_argument("--create-config", action="store_true",
                       help="Create a configuration file template")
    parser.add_argument("--credentials", default="credentials.json",
                       help="Google credentials file path (default: credentials.json)")
    parser.add_argument("--root-folder", default="Obsidian Sync",
                       help="Root folder name in Google Drive (default: Obsidian Sync)")
    parser.add_argument("--retry-failed", action="store_true",
                       help="Retry previously failed uploads from failed_uploads.json")

    args = parser.parse_args()

    if args.create_config:
        sync_tool = ObsidianToGoogleSync("", "")
        sync_tool.create_config_file(args.config)
        return

    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)

        obsidian_path = config.get("obsidian_path", args.obsidian_path)
        credentials_path = config.get("credentials_path", args.credentials)
        root_folder_name = config.get("root_folder_name", args.root_folder)
    else:
        obsidian_path = args.obsidian_path
        credentials_path = args.credentials
        root_folder_name = args.root_folder

    if not os.path.exists(credentials_path):
        print(f"Google credentials file not found: {credentials_path}")
        print("Please download credentials.json from Google Cloud Console")
        print("Instructions:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a new project or select existing one")
        print("3. Enable Google Drive API and Google Docs API")
        print("4. Create credentials (OAuth 2.0 Client ID)")
        print("5. Download the JSON file and save as credentials.json")
        return

    # Initialize sync tool
    sync_tool = ObsidianToGoogleSync(obsidian_path, credentials_path)

    if args.retry_failed:
        # Retry failed uploads
        print("🔄 Retrying previously failed uploads...")
        success = sync_tool.retry_failed_uploads()

        if success:
            print("✅ Retry completed!")
        else:
            print("❌ Retry failed. Check the log for details.")
    else:
        # Run normal sync
        success = sync_tool.sync_notes(root_folder_name)

        if success:
            print("✅ Sync completed successfully!")
            print(f"Your notes have been uploaded to Google Drive in folder: {root_folder_name}")

            # Check if there were any failures
            if sync_tool.failed_uploads:
                print(f"⚠️  {len(sync_tool.failed_uploads)} files failed to process.")
                print("Check failed_uploads.json for details.")
                print("Run with --retry-failed to retry failed uploads.")
        else:
            print("❌ Sync failed. Check the log for details.")


if __name__ == "__main__":
    main()
