#!/usr/bin/env python3
"""
Test image positioning with the real Vietnamese file
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_real_file_image_positioning():
    """Test image positioning with the actual Vietnamese file"""
    
    print("Testing image positioning with real Vietnamese file...")
    print("=" * 60)
    
    # Use the actual problematic file
    note_path = Path("obsidian/Top 10 câu hỏi phỏng vấn System Design và Microservices.md")
    
    if not note_path.exists():
        print(f"File not found: {note_path}")
        return False
    
    # Read the file content
    with open(note_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📄 File: {note_path.name}")
    print(f"📏 Content length: {len(content)} characters")
    print()
    
    # Create a mock sync tool for testing
    class MockSync:
        def find_media_file(self, base_path, image_name):
            # Look for the actual image file
            image_path = base_path / image_name
            if image_path.exists():
                return image_path
            # Also check in the obsidian directory
            obsidian_path = Path("obsidian") / image_name
            if obsidian_path.exists():
                return obsidian_path
            return None
        
        def process_images_in_markdown(self, content, note_path):
            # Manually implement the logic to avoid initialization issues
            import re

            image_positions = []
            modified_content = content

            # Find embedded images and their positions
            embedded_image_pattern = r'!\[\[([^\]]+)\]\]'

            for match in re.finditer(embedded_image_pattern, content):
                image_name = match.group(1)
                image_path = self.find_media_file(note_path.parent, image_name)

                if image_path and image_path.exists():
                    # Create a unique placeholder that we can find later
                    placeholder_id = f"IMAGE_PLACEHOLDER_{len(image_positions)}"
                    image_positions.append((image_name, image_path, placeholder_id))

                    print(f"   Found embedded image: {image_name}")

            # Replace all image syntax with placeholders
            for image_name, image_path, placeholder_id in image_positions:
                image_pattern = rf'!\[\[{re.escape(image_name)}\]\]'
                placeholder_text = f"\n[{placeholder_id}]\n"
                modified_content = re.sub(image_pattern, placeholder_text, modified_content)

            return modified_content, image_positions
        
        def convert_markdown_to_docs_requests(self, content, note_path):
            # Simplified implementation for testing
            import markdown
            from bs4 import BeautifulSoup

            try:
                html = markdown.markdown(content, extensions=['tables', 'fenced_code'])
                soup = BeautifulSoup(html, 'html.parser')

                requests = []
                index = 1

                for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre']):
                    if element.name.startswith('h'):
                        text = element.get_text()
                        text_length = grapheme_len(text)

                        requests.append({
                            'insertText': {
                                'location': {'index': index},
                                'text': text + '\n'
                            }
                        })

                        index += text_length + 1

                    elif element.name == 'p':
                        text = element.get_text()
                        if text.strip():
                            requests.append({
                                'insertText': {
                                    'location': {'index': index},
                                    'text': text + '\n'
                                }
                            })
                            index += grapheme_len(text) + 1

                return requests

            except Exception as e:
                print(f"Error converting markdown: {e}")
                return []
        
        def calculate_image_positions_in_doc(self, content_requests, image_positions):
            # Implement the position calculation logic
            image_positions_with_indices = []

            # Build a map of text content and their positions
            current_index = 1  # Google Docs start at index 1
            text_content = ""

            # Process all insertText requests to build the full text content
            for request in content_requests:
                if 'insertText' in request:
                    text = request['insertText']['text']
                    text_content += text

            # Find placeholder positions in the final text content
            for image_name, image_path, placeholder_id in image_positions:
                placeholder_text = f"[{placeholder_id}]"
                placeholder_pos = text_content.find(placeholder_text)

                if placeholder_pos != -1:
                    # Calculate the actual index in the Google Doc
                    # We need to account for grapheme clusters
                    text_before_placeholder = text_content[:placeholder_pos]
                    doc_index = 1 + grapheme_len(text_before_placeholder)

                    image_positions_with_indices.append((image_name, image_path, doc_index))
                    print(f"   Debug: Image {image_name} placeholder found at text position {placeholder_pos}, doc index {doc_index}")
                else:
                    print(f"   Warning: Could not find placeholder for image: {image_name}")

            return image_positions_with_indices
    
    sync_tool = MockSync()
    
    # Step 1: Process images in markdown
    print("1. Processing images in markdown...")
    processed_content, image_positions = sync_tool.process_images_in_markdown(content, note_path)
    
    print(f"   Found {len(image_positions)} images:")
    for name, path, placeholder_id in image_positions:
        print(f"   - {name} -> {placeholder_id}")
        print(f"     Path exists: {path.exists() if path else False}")
    
    if not image_positions:
        print("   No images found in the file")
        return True
    
    # Step 2: Show where placeholders are in the content
    print(f"\n2. Placeholder positions in processed content:")
    lines = processed_content.split('\n')
    for i, line in enumerate(lines):
        if 'IMAGE_PLACEHOLDER_' in line:
            print(f"   Line {i+1:2d}: >>> {line.strip()} <<<")
            # Show context (2 lines before and after)
            for j in range(max(0, i-2), min(len(lines), i+3)):
                if j != i:
                    print(f"   Line {j+1:2d}:     {lines[j]}")
            break
    
    # Step 3: Convert to Google Docs requests
    print(f"\n3. Converting to Google Docs requests...")
    content_requests = sync_tool.convert_markdown_to_docs_requests(processed_content, note_path)
    
    print(f"   Generated {len(content_requests)} requests")
    
    # Show some sample requests
    insert_requests = [r for r in content_requests if 'insertText' in r]
    print(f"   Insert text requests: {len(insert_requests)}")
    
    # Step 4: Calculate image positions
    print(f"\n4. Calculating exact image positions...")
    try:
        image_positions_with_indices = sync_tool.calculate_image_positions_in_doc(content_requests, image_positions)
        
        print(f"   Image positions in Google Doc:")
        for name, path, index in image_positions_with_indices:
            print(f"   - {name} will be inserted at index {index}")
        
        # Step 5: Verify the positions make sense
        print(f"\n5. Verifying positions...")
        
        # Build the text content to verify positions
        full_text = ""
        for request in content_requests:
            if 'insertText' in request:
                full_text += request['insertText']['text']
        
        print(f"   Full document length: {grapheme_len(full_text)} grapheme clusters")
        
        for name, path, index in image_positions_with_indices:
            if index <= grapheme_len(full_text):
                # Show context around the insertion point
                context_start = max(0, index - 50)
                context_end = min(grapheme_len(full_text), index + 50)
                context = full_text[context_start:context_end]
                
                print(f"   - {name} at index {index}:")
                print(f"     Context: ...{context}...")
            else:
                print(f"   - {name} at index {index}: WARNING - beyond document end!")
        
        return True
        
    except Exception as e:
        print(f"   Error calculating positions: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing Image Positioning with Real Vietnamese File")
    print("=" * 70)
    print()
    
    success = test_real_file_image_positioning()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("The image positioning logic should work with the Vietnamese file.")
    else:
        print("\n❌ Test failed!")
        print("There are issues with the image positioning logic.")
