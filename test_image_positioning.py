#!/usr/bin/env python3
"""
Test script to verify image positioning fix
"""

import sys
import os
from pathlib import Path
import tempfile
import shutil

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def create_test_content():
    """Create test markdown content with images"""
    return """# Test Note với Hình ảnh

Đây là đoạn văn đầu tiên trước hình ảnh.

![[test_image1.png]]

Đây là đoạn văn giữa hai hình ảnh. Nội dung này sẽ xuất hiện giữa hình ảnh đầu tiên và hình ảnh thứ hai.

## Phần 2

Đây là một header và sau đó là hình ảnh thứ hai:

![[test_image2.jpg]]

Và đây là đoạn văn cuối cùng sau tất cả hình ảnh.

### Kết luận

Tất cả hình ảnh phải xuất hiện đúng vị trí trong document.
"""

def test_image_position_calculation():
    """Test the image position calculation logic"""
    
    print("Testing image position calculation...")
    print("=" * 50)
    
    # Create temporary test environment
    with tempfile.TemporaryDirectory() as temp_dir:
        vault_path = Path(temp_dir) / "test_vault"
        vault_path.mkdir()
        
        # Create test images
        (vault_path / "test_image1.png").write_bytes(b"fake_png_data")
        (vault_path / "test_image2.jpg").write_bytes(b"fake_jpg_data")
        
        # Create test note
        test_content = create_test_content()
        note_path = vault_path / "test_note.md"
        note_path.write_text(test_content, encoding='utf-8')
        
        # Initialize sync tool (without credentials for this test)
        try:
            sync_tool = ObsidianToGoogleSync(str(vault_path), "dummy_credentials.json")
        except:
            # Create a minimal mock for testing
            class MockSync:
                def find_media_file(self, base_path, image_name):
                    return base_path / image_name
                
                def process_images_in_markdown(self, content, note_path):
                    # Import the actual method
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.process_images_in_markdown(content, note_path)
                
                def convert_markdown_to_docs_requests(self, content, note_path):
                    # Simplified version for testing
                    import markdown
                    from bs4 import BeautifulSoup
                    
                    html = markdown.markdown(content, extensions=['tables', 'fenced_code'])
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    requests = []
                    index = 1
                    
                    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p']):
                        text = element.get_text()
                        if text.strip():
                            requests.append({
                                'insertText': {
                                    'location': {'index': index},
                                    'text': text + '\n'
                                }
                            })
                            index += grapheme_len(text) + 1
                    
                    return requests
                
                def calculate_image_positions_in_doc(self, content_requests, image_positions):
                    # Import the actual method
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.calculate_image_positions_in_doc(content_requests, image_positions)
            
            sync_tool = MockSync()
        
        # Test image processing
        print("1. Processing images in markdown...")
        processed_content, image_positions = sync_tool.process_images_in_markdown(test_content, note_path)
        
        print(f"   Found {len(image_positions)} images:")
        for i, (name, path, placeholder_id) in enumerate(image_positions):
            print(f"   - {name} -> {placeholder_id}")
        
        print(f"\n2. Processed content preview:")
        lines = processed_content.split('\n')
        for i, line in enumerate(lines[:15]):  # Show first 15 lines
            if 'IMAGE_PLACEHOLDER_' in line:
                print(f"   {i+1:2d}: >>> {line.strip()} <<<")
            else:
                print(f"   {i+1:2d}: {line}")
        
        # Test conversion to Google Docs requests
        print(f"\n3. Converting to Google Docs requests...")
        content_requests = sync_tool.convert_markdown_to_docs_requests(processed_content, note_path)
        
        print(f"   Generated {len(content_requests)} requests")
        
        # Test position calculation
        print(f"\n4. Calculating image positions...")
        try:
            image_positions_with_indices = sync_tool.calculate_image_positions_in_doc(content_requests, image_positions)
            
            print(f"   Calculated positions:")
            for name, path, index in image_positions_with_indices:
                print(f"   - {name} will be inserted at index {index}")
            
            return True
            
        except Exception as e:
            print(f"   Error calculating positions: {e}")
            return False

def test_placeholder_replacement():
    """Test that placeholders are correctly replaced"""
    
    print("\nTesting placeholder replacement...")
    print("=" * 50)
    
    test_content = create_test_content()
    
    # Simulate the image processing
    import re
    
    # Find images
    embedded_image_pattern = r'!\[\[([^\]]+)\]\]'
    images = []
    
    for i, match in enumerate(re.finditer(embedded_image_pattern, test_content)):
        image_name = match.group(1)
        placeholder_id = f"IMAGE_PLACEHOLDER_{i}"
        images.append((image_name, placeholder_id))
    
    # Replace with placeholders
    modified_content = test_content
    for image_name, placeholder_id in images:
        image_pattern = rf'!\[\[{re.escape(image_name)}\]\]'
        placeholder_text = f"\n[{placeholder_id}]\n"
        modified_content = re.sub(image_pattern, placeholder_text, modified_content)
    
    print("Original content with images:")
    for i, line in enumerate(test_content.split('\n')[:10]):
        if '![[' in line:
            print(f"   {i+1:2d}: >>> {line} <<<")
        else:
            print(f"   {i+1:2d}: {line}")
    
    print("\nModified content with placeholders:")
    for i, line in enumerate(modified_content.split('\n')[:10]):
        if 'IMAGE_PLACEHOLDER_' in line:
            print(f"   {i+1:2d}: >>> {line.strip()} <<<")
        else:
            print(f"   {i+1:2d}: {line}")
    
    return True

if __name__ == "__main__":
    print("Testing Image Positioning Fix")
    print("=" * 60)
    print()
    
    success1 = test_placeholder_replacement()
    success2 = test_image_position_calculation()
    
    if success1 and success2:
        print("\n✅ All tests passed!")
        print("The image positioning fix should work correctly.")
    else:
        print("\n❌ Some tests failed!")
        print("There may still be issues with the image positioning logic.")
