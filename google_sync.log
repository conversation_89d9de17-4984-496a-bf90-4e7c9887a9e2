2025-07-23 17:59:06,440 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 17:59:06,597 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 17:59:06,599 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 17:59:06,601 - INFO - Google authentication successful
2025-07-23 17:59:07,467 - INFO - Found existing folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 17:59:08,211 - INFO - Found existing folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 17:59:08,213 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 17:59:08,214 - INFO - Syncing folder: Root (214 notes)
2025-07-23 17:59:08,214 - INFO - Syncing note: Kafka.md
2025-07-23 17:59:08,813 - INFO - Found existing document: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 17:59:08,813 - INFO - Document Kafka already exists, updating content...
2025-07-23 17:59:10,370 - INFO - Cleared content from document: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0
2025-07-23 17:59:10,370 - INFO - Using existing Google Doc: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 17:59:10,370 - INFO - Applying 7 formatting requests...
2025-07-23 17:59:10,370 - INFO - Validated 7/7 requests
2025-07-23 17:59:10,370 - INFO - Validated 7/7 requests
2025-07-23 17:59:11,276 - INFO - Successfully applied formatting to document: Kafka
2025-07-23 17:59:11,276 - INFO - Successfully processed Google Doc: Kafka
2025-07-23 17:59:11,277 - INFO - Syncing note: Bài toán liệt kê.md
2025-07-23 17:59:11,785 - INFO - Found existing document: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 17:59:11,785 - INFO - Document Bài toán liệt kê already exists, updating content...
2025-07-23 17:59:13,436 - INFO - Cleared content from document: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4
2025-07-23 17:59:13,437 - INFO - Using existing Google Doc: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 17:59:13,437 - INFO - Applying 164 formatting requests...
2025-07-23 17:59:13,452 - INFO - Validated 164/164 requests
2025-07-23 17:59:13,452 - INFO - Validated 164/164 requests
2025-07-23 17:59:14,531 - INFO - Successfully applied formatting to document: Bài toán liệt kê
2025-07-23 17:59:14,532 - INFO - Successfully processed Google Doc: Bài toán liệt kê
2025-07-23 17:59:14,532 - INFO - Syncing note: Vue - Nuxt.md
2025-07-23 17:59:15,048 - INFO - Found existing document: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 17:59:15,048 - INFO - Document Vue - Nuxt already exists, updating content...
2025-07-23 17:59:17,185 - INFO - Cleared content from document: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8
2025-07-23 17:59:17,186 - INFO - Using existing Google Doc: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 17:59:17,186 - INFO - Applying 422 formatting requests...
2025-07-23 17:59:17,203 - INFO - Validated 422/422 requests
2025-07-23 17:59:17,204 - INFO - Validated 422/422 requests
2025-07-23 17:59:18,579 - INFO - Successfully applied formatting to document: Vue - Nuxt
2025-07-23 17:59:18,579 - INFO - Successfully processed Google Doc: Vue - Nuxt
2025-07-23 17:59:18,580 - INFO - Syncing note: Kudofoto.md
2025-07-23 17:59:19,043 - INFO - Found existing document: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 17:59:19,044 - INFO - Document Kudofoto already exists, updating content...
2025-07-23 17:59:20,308 - INFO - Cleared content from document: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs
2025-07-23 17:59:20,309 - INFO - Using existing Google Doc: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 17:59:20,309 - INFO - Applying 3 formatting requests...
2025-07-23 17:59:20,309 - INFO - Validated 3/3 requests
2025-07-23 17:59:20,309 - INFO - Validated 3/3 requests
2025-07-23 17:59:21,202 - INFO - Successfully applied formatting to document: Kudofoto
2025-07-23 17:59:21,202 - INFO - Successfully processed Google Doc: Kudofoto
2025-07-23 17:59:21,202 - INFO - Syncing note: Solutions & System Designs & Design Patterns.md
2025-07-23 17:59:21,204 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 17:59:21,205 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 17:59:21,205 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 17:59:21,206 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 17:59:21,206 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 17:59:21,206 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 17:59:21,206 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 17:59:21,206 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 17:59:21,206 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 17:59:21,206 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 17:59:21,207 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 17:59:21,207 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 17:59:21,207 - INFO - Found embedded image: Untitled 3.png
2025-07-23 17:59:21,207 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 17:59:21,208 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 17:59:21,208 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 17:59:21,208 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 17:59:21,795 - INFO - Found existing document: Solutions & System Designs & Design Patterns (ID: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0)
2025-07-23 17:59:21,795 - INFO - Document Solutions & System Designs & Design Patterns already exists, updating content...
2025-07-23 17:59:24,742 - INFO - Cleared content from document: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0
2025-07-23 17:59:24,746 - INFO - Using existing Google Doc: Solutions & System Designs & Design Patterns (ID: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0)
2025-07-23 17:59:24,746 - INFO - Applying 844 formatting requests...
2025-07-23 17:59:24,808 - INFO - Validated 844/844 requests
2025-07-23 17:59:24,808 - INFO - Validated 844/844 requests
2025-07-23 17:59:27,172 - INFO - Successfully applied formatting to document: Solutions & System Designs & Design Patterns
2025-07-23 17:59:27,173 - INFO - Successfully processed Google Doc: Solutions & System Designs & Design Patterns
2025-07-23 17:59:27,456 - WARNING - Could not find placeholder for image: Pasted image 20241012194316.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: Pasted image 20240927232457.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: Pasted image 20240903223244.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: Pasted image 20240903230303.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: Pasted image 20240903230309.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: Pasted image 20240425163824.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: Pasted image 20240425163928.png
2025-07-23 17:59:27,497 - WARNING - Could not find placeholder for image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 17:59:27,498 - WARNING - Could not find placeholder for image: Pasted image 20240904085651.png
2025-07-23 17:59:27,498 - INFO - Processing image: Untitled 3.png at position 67068
2025-07-23 17:59:27,925 - INFO - Found existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 17:59:27,926 - INFO - File Untitled 3.png already exists, updating...
2025-07-23 17:59:32,837 - INFO - Updated existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 17:59:36,234 - INFO - Inserted image Untitled 3.png into document
2025-07-23 17:59:36,235 - INFO - Successfully inserted image: Untitled 3.png at index 67068
2025-07-23 17:59:36,925 - INFO - Processing image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg at position 54083
2025-07-23 17:59:37,348 - INFO - Found existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 17:59:37,348 - INFO - File 390dd032e50c3364eec22e71a19b2113_MD5.jpg already exists, updating...
2025-07-23 17:59:41,965 - INFO - Updated existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 17:59:44,340 - INFO - Inserted image 390dd032e50c3364eec22e71a19b2113_MD5.jpg into document
2025-07-23 17:59:44,340 - INFO - Successfully inserted image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg at index 54083
2025-07-23 17:59:45,067 - INFO - Processing image: Pasted image 20240904085651.png at position 53445
2025-07-23 17:59:45,558 - INFO - Found existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 17:59:45,558 - INFO - File Pasted image 20240904085651.png already exists, updating...
2025-07-23 17:59:49,824 - INFO - Updated existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 17:59:52,207 - INFO - Inserted image Pasted image 20240904085651.png into document
2025-07-23 17:59:52,208 - INFO - Successfully inserted image: Pasted image 20240904085651.png at index 53445
2025-07-23 17:59:52,882 - INFO - Processing image: Pasted image 20240903223244.png at position 51392
2025-07-23 17:59:53,336 - INFO - Found existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 17:59:53,336 - INFO - File Pasted image 20240903223244.png already exists, updating...
2025-07-23 17:59:57,772 - INFO - Updated existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 17:59:59,761 - INFO - Inserted image Pasted image 20240903223244.png into document
2025-07-23 17:59:59,762 - INFO - Successfully inserted image: Pasted image 20240903223244.png at index 51392
2025-07-23 18:00:00,441 - INFO - Processing image: Pasted image 20240903230309.png at position 50034
2025-07-23 18:00:00,916 - INFO - Found existing file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 18:00:00,916 - INFO - File Pasted image 20240903230309.png already exists, updating...
2025-07-23 18:00:06,286 - INFO - Updated existing file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 18:00:09,253 - INFO - Inserted image Pasted image 20240903230309.png into document
2025-07-23 18:00:09,254 - INFO - Successfully inserted image: Pasted image 20240903230309.png at index 50034
2025-07-23 18:00:09,972 - INFO - Processing image: Pasted image 20240903230303.png at position 50012
2025-07-23 18:00:10,419 - INFO - Found existing file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 18:00:10,419 - INFO - File Pasted image 20240903230303.png already exists, updating...
2025-07-23 18:00:17,378 - INFO - Updated existing file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 18:00:20,454 - INFO - Inserted image Pasted image 20240903230303.png into document
2025-07-23 18:00:20,454 - INFO - Successfully inserted image: Pasted image 20240903230303.png at index 50012
2025-07-23 18:00:21,106 - INFO - Processing image: bddf3546-c720-4313-9046-36d8c4a97019.png at position 46448
2025-07-23 18:00:21,561 - INFO - Found existing file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 18:00:21,561 - INFO - File bddf3546-c720-4313-9046-36d8c4a97019.png already exists, updating...
2025-07-23 18:00:28,615 - INFO - Updated existing file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 18:00:31,424 - INFO - Inserted image bddf3546-c720-4313-9046-36d8c4a97019.png into document
2025-07-23 18:00:31,424 - INFO - Successfully inserted image: bddf3546-c720-4313-9046-36d8c4a97019.png at index 46448
2025-07-23 18:00:32,086 - INFO - Processing image: Pasted image 20240927232457.png at position 37430
2025-07-23 18:00:32,514 - INFO - Found existing file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 18:00:32,515 - INFO - File Pasted image 20240927232457.png already exists, updating...
2025-07-23 18:01:06,807 - INFO - Updated existing file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 18:01:09,010 - INFO - Inserted image Pasted image 20240927232457.png into document
2025-07-23 18:01:09,010 - INFO - Successfully inserted image: Pasted image 20240927232457.png at index 37430
2025-07-23 18:01:09,674 - INFO - Processing image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg at position 31910
2025-07-23 18:01:10,163 - INFO - Found existing file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 18:01:10,163 - INFO - File telegram-cloud-photo-size-5-6311899726957623527-y.jpg already exists, updating...
2025-07-23 18:01:14,658 - INFO - Updated existing file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 18:01:16,974 - INFO - Inserted image telegram-cloud-photo-size-5-6311899726957623527-y.jpg into document
2025-07-23 18:01:16,974 - INFO - Successfully inserted image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg at index 31910
2025-07-23 18:01:17,736 - INFO - Processing image: Pasted image 20240425163928.png at position 17273
2025-07-23 18:01:18,288 - INFO - Found existing file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 18:01:18,289 - INFO - File Pasted image 20240425163928.png already exists, updating...
2025-07-23 18:01:22,472 - INFO - Updated existing file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 18:01:24,583 - INFO - Inserted image Pasted image 20240425163928.png into document
2025-07-23 18:01:24,583 - INFO - Successfully inserted image: Pasted image 20240425163928.png at index 17273
2025-07-23 18:01:25,285 - INFO - Processing image: Pasted image 20240425163824.png at position 16811
2025-07-23 18:01:25,706 - INFO - Found existing file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 18:01:25,706 - INFO - File Pasted image 20240425163824.png already exists, updating...
2025-07-23 18:01:31,844 - INFO - Updated existing file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 18:01:33,999 - INFO - Inserted image Pasted image 20240425163824.png into document
2025-07-23 18:01:34,000 - INFO - Successfully inserted image: Pasted image 20240425163824.png at index 16811
2025-07-23 18:01:34,665 - INFO - Processing image: Pasted image 20241012194316.png at position 10335
2025-07-23 18:01:35,115 - INFO - Found existing file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 18:01:35,115 - INFO - File Pasted image 20241012194316.png already exists, updating...
2025-07-23 18:01:40,380 - INFO - Updated existing file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 18:01:43,458 - INFO - Inserted image Pasted image 20241012194316.png into document
2025-07-23 18:01:43,458 - INFO - Successfully inserted image: Pasted image 20241012194316.png at index 10335
2025-07-23 18:01:44,135 - INFO - Syncing note: Domain knowledge.md
2025-07-23 18:01:44,578 - INFO - Found existing document: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 18:01:44,578 - INFO - Document Domain knowledge already exists, updating content...
2025-07-23 18:01:46,013 - INFO - Cleared content from document: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA
2025-07-23 18:01:46,014 - INFO - Using existing Google Doc: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 18:01:46,014 - INFO - Applying 6 formatting requests...
2025-07-23 18:01:46,014 - INFO - Validated 6/6 requests
2025-07-23 18:01:46,015 - INFO - Validated 6/6 requests
2025-07-23 18:01:47,050 - INFO - Successfully applied formatting to document: Domain knowledge
2025-07-23 18:01:47,051 - INFO - Successfully processed Google Doc: Domain knowledge
2025-07-23 18:01:47,052 - INFO - Syncing note: VPN - Proxy - Firewall.md
2025-07-23 18:01:47,594 - INFO - Found existing document: VPN - Proxy - Firewall (ID: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc)
2025-07-23 18:01:47,595 - INFO - Document VPN - Proxy - Firewall already exists, updating content...
2025-07-23 18:01:48,996 - INFO - Cleared content from document: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc
2025-07-23 18:01:48,996 - INFO - Using existing Google Doc: VPN - Proxy - Firewall (ID: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc)
2025-07-23 18:01:48,996 - INFO - Applying 19 formatting requests...
2025-07-23 18:01:48,997 - INFO - Validated 19/19 requests
2025-07-23 18:01:48,997 - INFO - Validated 19/19 requests
2025-07-23 18:01:49,872 - INFO - Successfully applied formatting to document: VPN - Proxy - Firewall
2025-07-23 18:01:49,873 - INFO - Successfully processed Google Doc: VPN - Proxy - Firewall
2025-07-23 18:01:49,873 - INFO - Syncing note: Cách làm sạch và bảo quản boots.md
2025-07-23 18:01:49,876 - INFO - Found embedded image: Untitled 16.png
2025-07-23 18:01:49,877 - INFO - Found embedded image: Untitled 1 9.png
2025-07-23 18:01:49,878 - INFO - Found embedded image: Untitled 2 6.png
2025-07-23 18:01:50,292 - INFO - Found existing document: Cách làm sạch và bảo quản boots (ID: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI)
2025-07-23 18:01:50,292 - INFO - Document Cách làm sạch và bảo quản boots already exists, updating content...
2025-07-23 18:01:51,801 - INFO - Cleared content from document: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI
2025-07-23 18:01:51,802 - INFO - Using existing Google Doc: Cách làm sạch và bảo quản boots (ID: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI)
2025-07-23 18:01:51,802 - INFO - Applying 24 formatting requests...
2025-07-23 18:01:51,806 - INFO - Validated 24/24 requests
2025-07-23 18:01:51,806 - INFO - Validated 24/24 requests
2025-07-23 18:01:52,602 - INFO - Successfully applied formatting to document: Cách làm sạch và bảo quản boots
2025-07-23 18:01:52,603 - INFO - Successfully processed Google Doc: Cách làm sạch và bảo quản boots
2025-07-23 18:01:52,610 - INFO - Processing image: Untitled 2 6.png at position 1303
2025-07-23 18:01:53,057 - INFO - Found existing file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 18:01:53,057 - INFO - File Untitled 2 6.png already exists, updating...
2025-07-23 18:01:58,695 - INFO - Updated existing file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 18:02:01,026 - INFO - Inserted image Untitled 2 6.png into document
2025-07-23 18:02:01,026 - INFO - Successfully inserted image: Untitled 2 6.png at index 1303
2025-07-23 18:02:01,364 - INFO - Processing image: Untitled 1 9.png at position 991
2025-07-23 18:02:01,786 - INFO - Found existing file: Untitled 1 9.png (ID: 1ERmvjwsTJgxP5FThseM_0k5fUnev1XBF)
2025-07-23 18:02:01,786 - INFO - File Untitled 1 9.png already exists, updating...
2025-07-23 18:02:07,728 - INFO - Updated existing file: Untitled 1 9.png (ID: 1ERmvjwsTJgxP5FThseM_0k5fUnev1XBF)
2025-07-23 18:02:09,738 - INFO - Inserted image Untitled 1 9.png into document
2025-07-23 18:02:09,738 - INFO - Successfully inserted image: Untitled 1 9.png at index 991
2025-07-23 18:02:10,080 - INFO - Processing image: Untitled 16.png at position 545
2025-07-23 18:02:10,656 - INFO - Found existing file: Untitled 16.png (ID: 1CLRtvemBdWXXHAB5ANbyDbNHMjMQVYce)
2025-07-23 18:02:10,656 - INFO - File Untitled 16.png already exists, updating...
2025-07-23 18:02:14,996 - INFO - Updated existing file: Untitled 16.png (ID: 1CLRtvemBdWXXHAB5ANbyDbNHMjMQVYce)
2025-07-23 18:02:17,820 - INFO - Inserted image Untitled 16.png into document
2025-07-23 18:02:17,820 - INFO - Successfully inserted image: Untitled 16.png at index 545
2025-07-23 18:02:18,146 - INFO - Syncing note: English.md
2025-07-23 18:02:18,148 - INFO - Found embedded image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png
2025-07-23 18:02:18,608 - INFO - Found existing document: English (ID: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ)
2025-07-23 18:02:18,608 - INFO - Document English already exists, updating content...
2025-07-23 18:02:19,764 - INFO - Cleared content from document: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ
2025-07-23 18:02:19,765 - INFO - Using existing Google Doc: English (ID: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ)
2025-07-23 18:02:19,765 - INFO - Applying 24 formatting requests...
2025-07-23 18:02:19,765 - INFO - Validated 24/24 requests
2025-07-23 18:02:19,765 - INFO - Validated 24/24 requests
2025-07-23 18:02:20,416 - INFO - Successfully applied formatting to document: English
2025-07-23 18:02:20,417 - INFO - Successfully processed Google Doc: English
2025-07-23 18:02:20,418 - INFO - Processing image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png at position 481
2025-07-23 18:02:20,832 - INFO - Found existing file: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png (ID: 1G_V_NGVX1-jY70yrOZr5Zf6uRhm6UTax)
2025-07-23 18:02:20,832 - INFO - File 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png already exists, updating...
2025-07-23 18:02:25,510 - INFO - Updated existing file: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png (ID: 1G_V_NGVX1-jY70yrOZr5Zf6uRhm6UTax)
2025-07-23 18:02:28,240 - INFO - Inserted image 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png into document
2025-07-23 18:02:28,241 - INFO - Successfully inserted image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png at index 481
2025-07-23 18:02:28,543 - INFO - Syncing note: Kotlin.md
2025-07-23 18:02:29,001 - INFO - Found existing document: Kotlin (ID: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0)
2025-07-23 18:02:29,002 - INFO - Document Kotlin already exists, updating content...
2025-07-23 18:02:30,577 - INFO - Cleared content from document: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0
2025-07-23 18:02:30,578 - INFO - Using existing Google Doc: Kotlin (ID: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0)
2025-07-23 18:02:30,578 - INFO - Applying 178 formatting requests...
2025-07-23 18:02:30,597 - INFO - Validated 178/178 requests
2025-07-23 18:02:30,597 - INFO - Validated 178/178 requests
2025-07-23 18:02:31,523 - INFO - Successfully applied formatting to document: Kotlin
2025-07-23 18:02:31,523 - INFO - Successfully processed Google Doc: Kotlin
2025-07-23 18:02:31,524 - INFO - Syncing note: Golang.md
2025-07-23 18:02:31,525 - INFO - Found embedded image: Basic Golang Project.jpg
2025-07-23 18:02:32,054 - INFO - Found existing document: Golang (ID: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ)
2025-07-23 18:02:32,054 - INFO - Document Golang already exists, updating content...
2025-07-23 18:02:35,432 - INFO - Cleared content from document: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ
2025-07-23 18:02:35,433 - INFO - Using existing Google Doc: Golang (ID: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ)
2025-07-23 18:02:35,433 - INFO - Applying 854 formatting requests...
2025-07-23 18:02:35,499 - INFO - Validated 854/854 requests
2025-07-23 18:02:35,500 - INFO - Validated 854/854 requests
2025-07-23 18:02:40,254 - INFO - Successfully applied formatting to document: Golang
2025-07-23 18:02:40,254 - INFO - Successfully processed Google Doc: Golang
2025-07-23 18:02:40,278 - INFO - Processing image: Basic Golang Project.jpg at position 17119
2025-07-23 18:02:40,735 - INFO - Found existing file: Basic Golang Project.jpg (ID: 15S4lbWCP74oN2upTxkF7HdfzPsUZV8BV)
2025-07-23 18:02:40,735 - INFO - File Basic Golang Project.jpg already exists, updating...
2025-07-23 18:02:44,991 - INFO - Updated existing file: Basic Golang Project.jpg (ID: 15S4lbWCP74oN2upTxkF7HdfzPsUZV8BV)
2025-07-23 18:02:47,681 - INFO - Inserted image Basic Golang Project.jpg into document
2025-07-23 18:02:47,681 - INFO - Successfully inserted image: Basic Golang Project.jpg at index 17119
2025-07-23 18:02:48,274 - INFO - Syncing note: Fresher Java Interview.md
2025-07-23 18:02:48,721 - INFO - Found existing document: Fresher Java Interview (ID: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I)
2025-07-23 18:02:48,722 - INFO - Document Fresher Java Interview already exists, updating content...
2025-07-23 18:02:50,357 - INFO - Cleared content from document: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I
2025-07-23 18:02:50,357 - INFO - Using existing Google Doc: Fresher Java Interview (ID: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I)
2025-07-23 18:02:50,357 - INFO - Applying 250 formatting requests...
2025-07-23 18:02:50,371 - INFO - Validated 250/250 requests
2025-07-23 18:02:50,371 - INFO - Validated 250/250 requests
2025-07-23 18:02:51,414 - INFO - Successfully applied formatting to document: Fresher Java Interview
2025-07-23 18:02:51,415 - INFO - Successfully processed Google Doc: Fresher Java Interview
2025-07-23 18:02:51,415 - INFO - Syncing note: Blockchain.md
2025-07-23 18:02:51,846 - INFO - Found existing document: Blockchain (ID: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8)
2025-07-23 18:02:51,846 - INFO - Document Blockchain already exists, updating content...
2025-07-23 18:02:54,321 - INFO - Cleared content from document: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8
2025-07-23 18:02:54,322 - INFO - Using existing Google Doc: Blockchain (ID: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8)
2025-07-23 18:02:54,322 - INFO - Applying 4 formatting requests...
2025-07-23 18:02:54,322 - INFO - Validated 4/4 requests
2025-07-23 18:02:54,322 - INFO - Validated 4/4 requests
2025-07-23 18:02:55,047 - INFO - Successfully applied formatting to document: Blockchain
2025-07-23 18:02:55,048 - INFO - Successfully processed Google Doc: Blockchain
2025-07-23 18:02:55,048 - INFO - Syncing note: No-code - nocode - low-code - lowcode.md
2025-07-23 18:02:55,049 - INFO - Found embedded image: 1675871539203.png
2025-07-23 18:02:55,482 - INFO - Found existing document: No-code - nocode - low-code - lowcode (ID: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y)
2025-07-23 18:02:55,482 - INFO - Document No-code - nocode - low-code - lowcode already exists, updating content...
2025-07-23 18:02:56,819 - INFO - Cleared content from document: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y
2025-07-23 18:02:56,819 - INFO - Using existing Google Doc: No-code - nocode - low-code - lowcode (ID: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y)
2025-07-23 18:02:56,819 - INFO - Applying 6 formatting requests...
2025-07-23 18:02:56,819 - INFO - Validated 6/6 requests
2025-07-23 18:02:56,819 - INFO - Validated 6/6 requests
2025-07-23 18:02:57,659 - INFO - Successfully applied formatting to document: No-code - nocode - low-code - lowcode
2025-07-23 18:02:57,659 - INFO - Successfully processed Google Doc: No-code - nocode - low-code - lowcode
2025-07-23 18:02:57,659 - INFO - Processing image: 1675871539203.png at position 90
2025-07-23 18:02:58,089 - INFO - Found existing file: 1675871539203.png (ID: 1HrngqADYngMmqsbVgiL4wkU9DOLypCNv)
2025-07-23 18:02:58,090 - INFO - File 1675871539203.png already exists, updating...
2025-07-23 18:03:03,091 - INFO - Updated existing file: 1675871539203.png (ID: 1HrngqADYngMmqsbVgiL4wkU9DOLypCNv)
2025-07-23 18:03:04,606 - ERROR - Error inserting image 1675871539203.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.". Details: "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.">
2025-07-23 18:03:04,606 - ERROR - Failed to insert image: 1675871539203.png
2025-07-23 18:03:04,606 - INFO - Syncing note: React - Next.md
2025-07-23 18:03:05,058 - INFO - Found existing document: React - Next (ID: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8)
2025-07-23 18:03:05,058 - INFO - Document React - Next already exists, updating content...
2025-07-23 18:03:06,754 - INFO - Cleared content from document: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8
2025-07-23 18:03:06,755 - INFO - Using existing Google Doc: React - Next (ID: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8)
2025-07-23 18:03:06,755 - INFO - Applying 479 formatting requests...
2025-07-23 18:03:06,767 - INFO - Validated 479/479 requests
2025-07-23 18:03:06,768 - INFO - Validated 479/479 requests
2025-07-23 18:03:08,118 - INFO - Successfully applied formatting to document: React - Next
2025-07-23 18:03:08,118 - INFO - Successfully processed Google Doc: React - Next
2025-07-23 18:03:08,118 - INFO - Syncing note: SAP - Systems, Applications, and Products.md
2025-07-23 18:03:08,553 - INFO - Found existing document: SAP - Systems, Applications, and Products (ID: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU)
2025-07-23 18:03:08,553 - INFO - Document SAP - Systems, Applications, and Products already exists, updating content...
2025-07-23 18:03:10,153 - INFO - Cleared content from document: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU
2025-07-23 18:03:10,154 - INFO - Using existing Google Doc: SAP - Systems, Applications, and Products (ID: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU)
2025-07-23 18:03:10,154 - INFO - Applying 29 formatting requests...
2025-07-23 18:03:10,156 - INFO - Validated 29/29 requests
2025-07-23 18:03:10,156 - INFO - Validated 29/29 requests
2025-07-23 18:03:11,016 - INFO - Successfully applied formatting to document: SAP - Systems, Applications, and Products
2025-07-23 18:03:11,016 - INFO - Successfully processed Google Doc: SAP - Systems, Applications, and Products
2025-07-23 18:03:11,016 - INFO - Syncing note: Airblade - AB - Air blade.md
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502172424.png
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502173135.png
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502175000.png
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502175940.png
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502180730.png
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502193802.png
2025-07-23 18:03:11,017 - INFO - Found embedded image: Pasted image 20240502195106.png
2025-07-23 18:03:11,018 - INFO - Found embedded image: Pasted image 20240502195226.png
2025-07-23 18:03:11,018 - INFO - Found embedded image: Pasted image 20240502203227.png
2025-07-23 18:03:11,018 - INFO - Found embedded image: Pasted image 20240505160601.png
2025-07-23 18:03:11,446 - INFO - Found existing document: Airblade - AB - Air blade (ID: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg)
2025-07-23 18:03:11,447 - INFO - Document Airblade - AB - Air blade already exists, updating content...
2025-07-23 18:03:12,691 - INFO - Cleared content from document: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg
2025-07-23 18:03:12,692 - INFO - Using existing Google Doc: Airblade - AB - Air blade (ID: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg)
2025-07-23 18:03:12,692 - INFO - Applying 19 formatting requests...
2025-07-23 18:03:12,692 - INFO - Validated 19/19 requests
2025-07-23 18:03:12,692 - INFO - Validated 19/19 requests
2025-07-23 18:03:13,553 - INFO - Successfully applied formatting to document: Airblade - AB - Air blade
2025-07-23 18:03:13,553 - INFO - Successfully processed Google Doc: Airblade - AB - Air blade
2025-07-23 18:03:13,555 - INFO - Processing image: Pasted image 20240505160601.png at position 278
2025-07-23 18:03:13,955 - INFO - Found existing file: Pasted image 20240505160601.png (ID: 19vHqQRDhIXQnafrFsSIdtF5v-8Tx0oMu)
2025-07-23 18:03:13,955 - INFO - File Pasted image 20240505160601.png already exists, updating...
2025-07-23 18:03:19,267 - INFO - Updated existing file: Pasted image 20240505160601.png (ID: 19vHqQRDhIXQnafrFsSIdtF5v-8Tx0oMu)
2025-07-23 18:03:21,760 - INFO - Inserted image Pasted image 20240505160601.png into document
2025-07-23 18:03:21,760 - INFO - Successfully inserted image: Pasted image 20240505160601.png at index 278
2025-07-23 18:03:22,140 - INFO - Processing image: Pasted image 20240502203227.png at position 256
2025-07-23 18:03:22,596 - INFO - Found existing file: Pasted image 20240502203227.png (ID: 184yBiOBwiC1BEjPcUZUBkLAbxDRWF6Dx)
2025-07-23 18:03:22,596 - INFO - File Pasted image 20240502203227.png already exists, updating...
2025-07-23 18:03:27,328 - INFO - Updated existing file: Pasted image 20240502203227.png (ID: 184yBiOBwiC1BEjPcUZUBkLAbxDRWF6Dx)
2025-07-23 18:03:29,435 - INFO - Inserted image Pasted image 20240502203227.png into document
2025-07-23 18:03:29,435 - INFO - Successfully inserted image: Pasted image 20240502203227.png at index 256
2025-07-23 18:03:29,762 - INFO - Processing image: Pasted image 20240502195226.png at position 234
2025-07-23 18:03:30,182 - INFO - Found existing file: Pasted image 20240502195226.png (ID: 1AfS3bqZ8UZ2tAw3CtDnazyHrVJ8rDUCO)
2025-07-23 18:03:30,182 - INFO - File Pasted image 20240502195226.png already exists, updating...
2025-07-23 18:03:34,492 - INFO - Updated existing file: Pasted image 20240502195226.png (ID: 1AfS3bqZ8UZ2tAw3CtDnazyHrVJ8rDUCO)
2025-07-23 18:03:36,718 - INFO - Inserted image Pasted image 20240502195226.png into document
2025-07-23 18:03:36,718 - INFO - Successfully inserted image: Pasted image 20240502195226.png at index 234
2025-07-23 18:03:37,066 - INFO - Processing image: Pasted image 20240502195106.png at position 212
2025-07-23 18:03:37,496 - INFO - Found existing file: Pasted image 20240502195106.png (ID: 1wCOr0N3Ccg733qGpvFOga0s4QxJFIvNd)
2025-07-23 18:03:37,496 - INFO - File Pasted image 20240502195106.png already exists, updating...
2025-07-23 18:03:42,481 - INFO - Updated existing file: Pasted image 20240502195106.png (ID: 1wCOr0N3Ccg733qGpvFOga0s4QxJFIvNd)
2025-07-23 18:03:45,188 - INFO - Inserted image Pasted image 20240502195106.png into document
2025-07-23 18:03:45,189 - INFO - Successfully inserted image: Pasted image 20240502195106.png at index 212
2025-07-23 18:03:45,528 - INFO - Processing image: Pasted image 20240502193802.png at position 190
2025-07-23 18:03:45,992 - INFO - Found existing file: Pasted image 20240502193802.png (ID: 1vHljhifrKfzEfVhZGhLPMG8I83-h1fOq)
2025-07-23 18:03:45,992 - INFO - File Pasted image 20240502193802.png already exists, updating...
2025-07-23 18:03:52,369 - INFO - Updated existing file: Pasted image 20240502193802.png (ID: 1vHljhifrKfzEfVhZGhLPMG8I83-h1fOq)
2025-07-23 18:03:54,610 - INFO - Inserted image Pasted image 20240502193802.png into document
2025-07-23 18:03:54,610 - INFO - Successfully inserted image: Pasted image 20240502193802.png at index 190
2025-07-23 18:03:54,956 - INFO - Processing image: Pasted image 20240502180730.png at position 168
2025-07-23 18:03:55,376 - INFO - Found existing file: Pasted image 20240502180730.png (ID: 1480xkeA-YEMkHs-xceHyzWl8tt6NsGmR)
2025-07-23 18:03:55,377 - INFO - File Pasted image 20240502180730.png already exists, updating...
2025-07-23 18:04:00,397 - INFO - Updated existing file: Pasted image 20240502180730.png (ID: 1480xkeA-YEMkHs-xceHyzWl8tt6NsGmR)
2025-07-23 18:04:03,116 - INFO - Inserted image Pasted image 20240502180730.png into document
2025-07-23 18:04:03,117 - INFO - Successfully inserted image: Pasted image 20240502180730.png at index 168
2025-07-23 18:04:03,458 - INFO - Processing image: Pasted image 20240502175940.png at position 146
2025-07-23 18:04:03,878 - INFO - Found existing file: Pasted image 20240502175940.png (ID: 1Z6jpvse7ChAJiT6-lK2oHXJY_QQ3Ok1b)
2025-07-23 18:04:03,878 - INFO - File Pasted image 20240502175940.png already exists, updating...
2025-07-23 18:04:11,840 - INFO - Updated existing file: Pasted image 20240502175940.png (ID: 1Z6jpvse7ChAJiT6-lK2oHXJY_QQ3Ok1b)
2025-07-23 18:04:14,078 - INFO - Inserted image Pasted image 20240502175940.png into document
2025-07-23 18:04:14,078 - INFO - Successfully inserted image: Pasted image 20240502175940.png at index 146
2025-07-23 18:04:14,412 - INFO - Processing image: Pasted image 20240502175000.png at position 124
2025-07-23 18:04:14,863 - INFO - Found existing file: Pasted image 20240502175000.png (ID: 1-h1sSlqTb8oNdK8mi_wevHLnvPfyVNrM)
2025-07-23 18:04:14,863 - INFO - File Pasted image 20240502175000.png already exists, updating...
2025-07-23 18:04:19,110 - INFO - Updated existing file: Pasted image 20240502175000.png (ID: 1-h1sSlqTb8oNdK8mi_wevHLnvPfyVNrM)
2025-07-23 18:04:21,480 - INFO - Inserted image Pasted image 20240502175000.png into document
2025-07-23 18:04:21,482 - INFO - Successfully inserted image: Pasted image 20240502175000.png at index 124
2025-07-23 18:04:21,824 - INFO - Processing image: Pasted image 20240502173135.png at position 102
2025-07-23 18:04:22,307 - INFO - Found existing file: Pasted image 20240502173135.png (ID: 1Hu600W9W1IMUmk0jXwPouutZDnPIUBTl)
2025-07-23 18:04:22,308 - INFO - File Pasted image 20240502173135.png already exists, updating...
2025-07-23 18:04:26,777 - INFO - Updated existing file: Pasted image 20240502173135.png (ID: 1Hu600W9W1IMUmk0jXwPouutZDnPIUBTl)
2025-07-23 18:04:29,063 - INFO - Inserted image Pasted image 20240502173135.png into document
2025-07-23 18:04:29,063 - INFO - Successfully inserted image: Pasted image 20240502173135.png at index 102
2025-07-23 18:04:29,401 - INFO - Processing image: Pasted image 20240502172424.png at position 80
2025-07-23 18:04:29,805 - INFO - Found existing file: Pasted image 20240502172424.png (ID: 16S3g6HOx_gMZ9Icj_CC-dF5Y2XAvndLU)
2025-07-23 18:04:29,805 - INFO - File Pasted image 20240502172424.png already exists, updating...
2025-07-23 18:04:33,713 - INFO - Updated existing file: Pasted image 20240502172424.png (ID: 16S3g6HOx_gMZ9Icj_CC-dF5Y2XAvndLU)
2025-07-23 18:04:36,103 - INFO - Inserted image Pasted image 20240502172424.png into document
2025-07-23 18:04:36,103 - INFO - Successfully inserted image: Pasted image 20240502172424.png at index 80
2025-07-23 18:04:36,423 - INFO - Syncing note: Post score algorithm - Trending algorithm.md
2025-07-23 18:04:36,424 - INFO - Found embedded image: Pasted image 20231014200829.png
2025-07-23 18:04:36,424 - INFO - Found embedded image: Pasted image 20231014200850.png
2025-07-23 18:04:36,862 - INFO - Found existing document: Post score algorithm - Trending algorithm (ID: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8)
2025-07-23 18:04:36,862 - INFO - Document Post score algorithm - Trending algorithm already exists, updating content...
2025-07-23 18:04:37,990 - INFO - Cleared content from document: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8
2025-07-23 18:04:37,990 - INFO - Using existing Google Doc: Post score algorithm - Trending algorithm (ID: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8)
2025-07-23 18:04:37,990 - INFO - Applying 7 formatting requests...
2025-07-23 18:04:37,990 - INFO - Validated 7/7 requests
2025-07-23 18:04:37,990 - INFO - Validated 7/7 requests
2025-07-23 18:04:38,807 - INFO - Successfully applied formatting to document: Post score algorithm - Trending algorithm
2025-07-23 18:04:38,807 - INFO - Successfully processed Google Doc: Post score algorithm - Trending algorithm
2025-07-23 18:04:38,807 - INFO - Processing image: Pasted image 20231014200850.png at position 151
2025-07-23 18:04:39,248 - INFO - Found existing file: Pasted image 20231014200850.png (ID: 1Meqv2ihZPf2jTVvKS1ucSA1fF8wuYa2J)
2025-07-23 18:04:39,248 - INFO - File Pasted image 20231014200850.png already exists, updating...
2025-07-23 18:04:46,090 - INFO - Updated existing file: Pasted image 20231014200850.png (ID: 1Meqv2ihZPf2jTVvKS1ucSA1fF8wuYa2J)
2025-07-23 18:04:48,529 - INFO - Inserted image Pasted image 20231014200850.png into document
2025-07-23 18:04:48,529 - INFO - Successfully inserted image: Pasted image 20231014200850.png at index 151
2025-07-23 18:04:48,844 - INFO - Processing image: Pasted image 20231014200829.png at position 129
2025-07-23 18:04:49,272 - INFO - Found existing file: Pasted image 20231014200829.png (ID: 1djVOj-7KJ6Jo3e_MWii2dh6clUdOio0s)
2025-07-23 18:04:49,273 - INFO - File Pasted image 20231014200829.png already exists, updating...
2025-07-23 18:04:54,774 - INFO - Updated existing file: Pasted image 20231014200829.png (ID: 1djVOj-7KJ6Jo3e_MWii2dh6clUdOio0s)
2025-07-23 18:04:56,856 - INFO - Inserted image Pasted image 20231014200829.png into document
2025-07-23 18:04:56,856 - INFO - Successfully inserted image: Pasted image 20231014200829.png at index 129
2025-07-23 18:04:57,171 - INFO - Syncing note: Windows Tips.md
2025-07-23 18:04:57,609 - INFO - Found existing document: Windows Tips (ID: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU)
2025-07-23 18:04:57,609 - INFO - Document Windows Tips already exists, updating content...
2025-07-23 18:04:59,194 - INFO - Cleared content from document: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU
2025-07-23 18:04:59,194 - INFO - Using existing Google Doc: Windows Tips (ID: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU)
2025-07-23 18:04:59,194 - INFO - Applying 49 formatting requests...
2025-07-23 18:04:59,199 - INFO - Validated 49/49 requests
2025-07-23 18:04:59,199 - INFO - Validated 49/49 requests
2025-07-23 18:04:59,996 - INFO - Successfully applied formatting to document: Windows Tips
2025-07-23 18:04:59,996 - INFO - Successfully processed Google Doc: Windows Tips
2025-07-23 18:04:59,996 - INFO - Syncing note: Du lịch Huế.md
2025-07-23 18:05:00,458 - INFO - Found existing document: Du lịch Huế (ID: 1tsw_aeq_8hk4ZRgMwIcJvT6BEM2Zz85tgER8Jk0Uip8)
2025-07-23 18:05:00,458 - INFO - Document Du lịch Huế already exists, updating content...
2025-07-23 18:05:01,774 - INFO - Cleared content from document: 1tsw_aeq_8hk4ZRgMwIcJvT6BEM2Zz85tgER8Jk0Uip8
2025-07-23 18:05:01,774 - INFO - Using existing Google Doc: Du lịch Huế (ID: 1tsw_aeq_8hk4ZRgMwIcJvT6BEM2Zz85tgER8Jk0Uip8)
2025-07-23 18:05:01,775 - INFO - Applying 212 formatting requests...
2025-07-23 18:05:01,783 - INFO - Validated 212/212 requests
2025-07-23 18:05:01,783 - INFO - Validated 212/212 requests
2025-07-23 18:05:02,704 - INFO - Successfully applied formatting to document: Du lịch Huế
2025-07-23 18:05:02,704 - INFO - Successfully processed Google Doc: Du lịch Huế
2025-07-23 18:05:02,704 - INFO - Syncing note: Research websites.md
2025-07-23 18:05:03,138 - INFO - Found existing document: Research websites (ID: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58)
2025-07-23 18:05:03,138 - INFO - Document Research websites already exists, updating content...
2025-07-23 18:05:04,532 - INFO - Cleared content from document: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58
2025-07-23 18:05:04,533 - INFO - Using existing Google Doc: Research websites (ID: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58)
2025-07-23 18:05:04,533 - INFO - Applying 20 formatting requests...
2025-07-23 18:05:04,534 - INFO - Validated 20/20 requests
2025-07-23 18:05:04,534 - INFO - Validated 20/20 requests
2025-07-23 18:05:05,217 - INFO - Successfully applied formatting to document: Research websites
2025-07-23 18:05:05,217 - INFO - Successfully processed Google Doc: Research websites
2025-07-23 18:05:05,217 - INFO - Syncing note: Terminal UI - TUI.md
2025-07-23 18:05:05,648 - INFO - Found existing document: Terminal UI - TUI (ID: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4)
2025-07-23 18:05:05,648 - INFO - Document Terminal UI - TUI already exists, updating content...
2025-07-23 18:05:07,250 - INFO - Cleared content from document: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4
2025-07-23 18:05:07,250 - INFO - Using existing Google Doc: Terminal UI - TUI (ID: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4)
2025-07-23 18:05:07,250 - INFO - Applying 47 formatting requests...
2025-07-23 18:05:07,251 - INFO - Validated 47/47 requests
2025-07-23 18:05:07,251 - INFO - Validated 47/47 requests
2025-07-23 18:05:07,973 - INFO - Successfully applied formatting to document: Terminal UI - TUI
2025-07-23 18:05:07,975 - INFO - Successfully processed Google Doc: Terminal UI - TUI
2025-07-23 18:05:07,976 - INFO - Syncing note: Các loại quần nên có trong tủ đồ.md
2025-07-23 18:05:07,979 - INFO - Found embedded image: Untitled 15.png
2025-07-23 18:05:07,983 - INFO - Found embedded image: Untitled 1 8.png
2025-07-23 18:05:07,983 - INFO - Found embedded image: Untitled 2 5.png
2025-07-23 18:05:08,399 - INFO - Found existing document: Các loại quần nên có trong tủ đồ (ID: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU)
2025-07-23 18:05:08,399 - INFO - Document Các loại quần nên có trong tủ đồ already exists, updating content...
2025-07-23 18:05:09,829 - INFO - Cleared content from document: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU
2025-07-23 18:05:09,829 - INFO - Using existing Google Doc: Các loại quần nên có trong tủ đồ (ID: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU)
2025-07-23 18:05:09,829 - INFO - Applying 13 formatting requests...
2025-07-23 18:05:09,830 - INFO - Validated 13/13 requests
2025-07-23 18:05:09,830 - INFO - Validated 13/13 requests
2025-07-23 18:05:10,504 - INFO - Successfully applied formatting to document: Các loại quần nên có trong tủ đồ
2025-07-23 18:05:10,504 - INFO - Successfully processed Google Doc: Các loại quần nên có trong tủ đồ
2025-07-23 18:05:10,506 - INFO - Processing image: Untitled 2 5.png at position 904
2025-07-23 18:05:10,956 - INFO - Found existing file: Untitled 2 5.png (ID: 1XEPzWgDsqiLVu8qaI-QythxFslGZqTQR)
2025-07-23 18:05:10,956 - INFO - File Untitled 2 5.png already exists, updating...
2025-07-23 18:05:15,293 - INFO - Updated existing file: Untitled 2 5.png (ID: 1XEPzWgDsqiLVu8qaI-QythxFslGZqTQR)
2025-07-23 18:05:18,321 - INFO - Inserted image Untitled 2 5.png into document
2025-07-23 18:05:18,322 - INFO - Successfully inserted image: Untitled 2 5.png at index 904
2025-07-23 18:05:18,744 - INFO - Processing image: Untitled 1 8.png at position 691
2025-07-23 18:05:19,182 - INFO - Found existing file: Untitled 1 8.png (ID: 1xQL65i5prT573u1YuZrBi83cV7CZrA_v)
2025-07-23 18:05:19,182 - INFO - File Untitled 1 8.png already exists, updating...
2025-07-23 18:05:23,403 - INFO - Updated existing file: Untitled 1 8.png (ID: 1xQL65i5prT573u1YuZrBi83cV7CZrA_v)
2025-07-23 18:05:26,158 - INFO - Inserted image Untitled 1 8.png into document
2025-07-23 18:05:26,158 - INFO - Successfully inserted image: Untitled 1 8.png at index 691
2025-07-23 18:05:26,580 - INFO - Processing image: Untitled 15.png at position 415
2025-07-23 18:05:27,027 - INFO - Found existing file: Untitled 15.png (ID: 1Y7No72BnqKOp9_fHTGcnfwXMXw7MkFxu)
2025-07-23 18:05:27,027 - INFO - File Untitled 15.png already exists, updating...
2025-07-23 18:05:31,327 - INFO - Updated existing file: Untitled 15.png (ID: 1Y7No72BnqKOp9_fHTGcnfwXMXw7MkFxu)
2025-07-23 18:05:33,764 - INFO - Inserted image Untitled 15.png into document
2025-07-23 18:05:33,765 - INFO - Successfully inserted image: Untitled 15.png at index 415
2025-07-23 18:05:34,189 - INFO - Syncing note: Management.md
2025-07-23 18:05:34,628 - INFO - Found existing document: Management (ID: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg)
2025-07-23 18:05:34,628 - INFO - Document Management already exists, updating content...
2025-07-23 18:05:36,077 - INFO - Cleared content from document: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg
2025-07-23 18:05:36,078 - INFO - Using existing Google Doc: Management (ID: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg)
2025-07-23 18:05:36,078 - INFO - Applying 38 formatting requests...
2025-07-23 18:05:36,089 - INFO - Validated 38/38 requests
2025-07-23 18:05:36,089 - INFO - Validated 38/38 requests
2025-07-23 18:05:36,781 - INFO - Successfully applied formatting to document: Management
2025-07-23 18:05:36,781 - INFO - Successfully processed Google Doc: Management
2025-07-23 18:05:36,782 - INFO - Syncing note: Monitor server.md
2025-07-23 18:05:37,227 - INFO - Found existing document: Monitor server (ID: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w)
2025-07-23 18:05:37,227 - INFO - Document Monitor server already exists, updating content...
2025-07-23 18:05:38,460 - INFO - Cleared content from document: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w
2025-07-23 18:05:38,461 - INFO - Using existing Google Doc: Monitor server (ID: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w)
2025-07-23 18:05:38,461 - INFO - Applying 3 formatting requests...
2025-07-23 18:05:38,462 - INFO - Validated 3/3 requests
2025-07-23 18:05:38,462 - INFO - Validated 3/3 requests
2025-07-23 18:05:39,132 - INFO - Successfully applied formatting to document: Monitor server
2025-07-23 18:05:39,132 - INFO - Successfully processed Google Doc: Monitor server
2025-07-23 18:05:39,132 - INFO - Syncing note: English with LLM.md
2025-07-23 18:05:39,569 - INFO - Found existing document: English with LLM (ID: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk)
2025-07-23 18:05:39,570 - INFO - Document English with LLM already exists, updating content...
2025-07-23 18:05:40,952 - INFO - Cleared content from document: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk
2025-07-23 18:05:40,953 - INFO - Using existing Google Doc: English with LLM (ID: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk)
2025-07-23 18:05:40,953 - INFO - Applying 67 formatting requests...
2025-07-23 18:05:40,960 - INFO - Validated 67/67 requests
2025-07-23 18:05:40,961 - INFO - Validated 67/67 requests
2025-07-23 18:05:41,781 - INFO - Successfully applied formatting to document: English with LLM
2025-07-23 18:05:41,781 - INFO - Successfully processed Google Doc: English with LLM
2025-07-23 18:05:41,782 - INFO - Syncing note: Network.md
2025-07-23 18:05:42,253 - INFO - Found existing document: Network (ID: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0)
2025-07-23 18:05:42,253 - INFO - Document Network already exists, updating content...
2025-07-23 18:05:43,699 - INFO - Cleared content from document: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0
2025-07-23 18:05:43,699 - INFO - Using existing Google Doc: Network (ID: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0)
2025-07-23 18:05:43,699 - INFO - Applying 18 formatting requests...
2025-07-23 18:05:43,701 - INFO - Validated 18/18 requests
2025-07-23 18:05:43,701 - INFO - Validated 18/18 requests
2025-07-23 18:05:44,121 - INFO - Successfully applied formatting to document: Network
2025-07-23 18:05:44,121 - INFO - Successfully processed Google Doc: Network
2025-07-23 18:05:44,121 - INFO - Syncing note: Python.md
2025-07-23 18:05:44,542 - INFO - Found existing document: Python (ID: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg)
2025-07-23 18:05:44,542 - INFO - Document Python already exists, updating content...
2025-07-23 18:05:45,971 - INFO - Cleared content from document: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg
2025-07-23 18:05:45,972 - INFO - Using existing Google Doc: Python (ID: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg)
2025-07-23 18:05:45,973 - INFO - Applying 15 formatting requests...
2025-07-23 18:05:46,019 - INFO - Validated 15/15 requests
2025-07-23 18:05:46,019 - INFO - Validated 15/15 requests
2025-07-23 18:05:46,652 - INFO - Successfully applied formatting to document: Python
2025-07-23 18:05:46,652 - INFO - Successfully processed Google Doc: Python
2025-07-23 18:05:46,653 - INFO - Syncing note: Tạo video bằng các tool AI.md
2025-07-23 18:05:47,138 - INFO - Found existing document: Tạo video bằng các tool AI (ID: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY)
2025-07-23 18:05:47,138 - INFO - Document Tạo video bằng các tool AI already exists, updating content...
2025-07-23 18:05:48,672 - INFO - Cleared content from document: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY
2025-07-23 18:05:48,672 - INFO - Using existing Google Doc: Tạo video bằng các tool AI (ID: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY)
2025-07-23 18:05:48,672 - INFO - Applying 34 formatting requests...
2025-07-23 18:05:48,673 - INFO - Validated 34/34 requests
2025-07-23 18:05:48,673 - INFO - Validated 34/34 requests
2025-07-23 18:05:49,374 - INFO - Successfully applied formatting to document: Tạo video bằng các tool AI
2025-07-23 18:05:49,374 - INFO - Successfully processed Google Doc: Tạo video bằng các tool AI
2025-07-23 18:05:49,374 - INFO - Syncing note: English with LLM - Irregular verbs.md
2025-07-23 18:05:49,879 - INFO - Found existing document: English with LLM - Irregular verbs (ID: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno)
2025-07-23 18:05:49,879 - INFO - Document English with LLM - Irregular verbs already exists, updating content...
2025-07-23 18:05:51,586 - INFO - Cleared content from document: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno
2025-07-23 18:05:51,586 - INFO - Using existing Google Doc: English with LLM - Irregular verbs (ID: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno)
2025-07-23 18:05:51,586 - INFO - Applying 301 formatting requests...
2025-07-23 18:05:51,625 - INFO - Validated 301/301 requests
2025-07-23 18:05:51,625 - INFO - Validated 301/301 requests
2025-07-23 18:05:52,847 - INFO - Successfully applied formatting to document: English with LLM - Irregular verbs
2025-07-23 18:05:52,848 - INFO - Successfully processed Google Doc: English with LLM - Irregular verbs
2025-07-23 18:05:52,848 - INFO - Syncing note: Work.md
2025-07-23 18:05:53,310 - INFO - Found existing document: Work (ID: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU)
2025-07-23 18:05:53,310 - INFO - Document Work already exists, updating content...
2025-07-23 18:05:54,499 - INFO - Cleared content from document: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU
2025-07-23 18:05:54,500 - INFO - Using existing Google Doc: Work (ID: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU)
2025-07-23 18:05:54,500 - INFO - Applying 9 formatting requests...
2025-07-23 18:05:54,500 - INFO - Validated 9/9 requests
2025-07-23 18:05:54,500 - INFO - Validated 9/9 requests
2025-07-23 18:05:55,796 - INFO - Successfully applied formatting to document: Work
2025-07-23 18:05:55,796 - INFO - Successfully processed Google Doc: Work
2025-07-23 18:05:55,796 - INFO - Syncing note: Chuyến Du Lịch Công Ty Inception Labs 2025.md
2025-07-23 18:05:56,313 - INFO - Found existing document: Chuyến Du Lịch Công Ty Inception Labs 2025 (ID: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s)
2025-07-23 18:05:56,314 - INFO - Document Chuyến Du Lịch Công Ty Inception Labs 2025 already exists, updating content...
2025-07-23 18:05:58,056 - INFO - Cleared content from document: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s
2025-07-23 18:05:58,056 - INFO - Using existing Google Doc: Chuyến Du Lịch Công Ty Inception Labs 2025 (ID: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s)
2025-07-23 18:05:58,056 - INFO - Applying 225 formatting requests...
2025-07-23 18:05:58,065 - INFO - Validated 225/225 requests
2025-07-23 18:05:58,065 - INFO - Validated 225/225 requests
2025-07-23 18:05:59,276 - INFO - Successfully applied formatting to document: Chuyến Du Lịch Công Ty Inception Labs 2025
2025-07-23 18:05:59,276 - INFO - Successfully processed Google Doc: Chuyến Du Lịch Công Ty Inception Labs 2025
2025-07-23 18:05:59,276 - INFO - Syncing note: Dagger & Koin.md
2025-07-23 18:06:00,286 - INFO - Found existing document: Dagger & Koin (ID: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s)
2025-07-23 18:06:00,286 - INFO - Document Dagger & Koin already exists, updating content...
2025-07-23 18:06:01,694 - INFO - Cleared content from document: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s
2025-07-23 18:06:01,694 - INFO - Using existing Google Doc: Dagger & Koin (ID: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s)
2025-07-23 18:06:01,694 - INFO - Applying 16 formatting requests...
2025-07-23 18:06:01,696 - INFO - Validated 16/16 requests
2025-07-23 18:06:01,696 - INFO - Validated 16/16 requests
2025-07-23 18:06:02,342 - INFO - Successfully applied formatting to document: Dagger & Koin
2025-07-23 18:06:02,342 - INFO - Successfully processed Google Doc: Dagger & Koin
2025-07-23 18:06:02,342 - INFO - Syncing note: SolidJS.md
2025-07-23 18:06:02,952 - INFO - Found existing document: SolidJS (ID: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0)
2025-07-23 18:06:02,952 - INFO - Document SolidJS already exists, updating content...
2025-07-23 18:06:04,300 - INFO - Cleared content from document: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0
2025-07-23 18:06:04,301 - INFO - Using existing Google Doc: SolidJS (ID: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0)
2025-07-23 18:06:04,301 - INFO - Applying 2 formatting requests...
2025-07-23 18:06:04,301 - INFO - Validated 2/2 requests
2025-07-23 18:06:04,301 - INFO - Validated 2/2 requests
2025-07-23 18:06:04,839 - INFO - Successfully applied formatting to document: SolidJS
2025-07-23 18:06:04,839 - INFO - Successfully processed Google Doc: SolidJS
2025-07-23 18:06:04,839 - INFO - Syncing note: Fresher Back-end Interview.md
2025-07-23 18:06:05,270 - INFO - Found existing document: Fresher Back-end Interview (ID: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU)
2025-07-23 18:06:05,271 - INFO - Document Fresher Back-end Interview already exists, updating content...
2025-07-23 18:06:06,796 - INFO - Cleared content from document: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU
2025-07-23 18:06:06,797 - INFO - Using existing Google Doc: Fresher Back-end Interview (ID: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU)
2025-07-23 18:06:06,797 - INFO - Applying 40 formatting requests...
2025-07-23 18:06:06,800 - INFO - Validated 40/40 requests
2025-07-23 18:06:06,801 - INFO - Validated 40/40 requests
2025-07-23 18:06:07,456 - INFO - Successfully applied formatting to document: Fresher Back-end Interview
2025-07-23 18:06:07,457 - INFO - Successfully processed Google Doc: Fresher Back-end Interview
2025-07-23 18:06:07,457 - INFO - Syncing note: CSS.md
2025-07-23 18:06:07,936 - INFO - Found existing document: CSS (ID: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4)
2025-07-23 18:06:07,936 - INFO - Document CSS already exists, updating content...
2025-07-23 18:06:09,422 - INFO - Cleared content from document: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4
2025-07-23 18:06:09,422 - INFO - Using existing Google Doc: CSS (ID: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4)
2025-07-23 18:06:09,422 - INFO - Applying 84 formatting requests...
2025-07-23 18:06:09,426 - INFO - Validated 84/84 requests
2025-07-23 18:06:09,426 - INFO - Validated 84/84 requests
2025-07-23 18:06:10,109 - INFO - Successfully applied formatting to document: CSS
2025-07-23 18:06:10,109 - INFO - Successfully processed Google Doc: CSS
2025-07-23 18:06:10,109 - INFO - Syncing note: 25 phương pháp giúp bạn ngừng overthinking.md
2025-07-23 18:06:10,570 - INFO - Found existing document: 25 phương pháp giúp bạn ngừng overthinking (ID: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY)
2025-07-23 18:06:10,570 - INFO - Document 25 phương pháp giúp bạn ngừng overthinking already exists, updating content...
2025-07-23 18:06:12,162 - INFO - Cleared content from document: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY
2025-07-23 18:06:12,163 - INFO - Using existing Google Doc: 25 phương pháp giúp bạn ngừng overthinking (ID: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY)
2025-07-23 18:06:12,163 - INFO - Applying 57 formatting requests...
2025-07-23 18:06:12,169 - INFO - Validated 57/57 requests
2025-07-23 18:06:12,169 - INFO - Validated 57/57 requests
2025-07-23 18:06:12,941 - INFO - Successfully applied formatting to document: 25 phương pháp giúp bạn ngừng overthinking
2025-07-23 18:06:12,941 - INFO - Successfully processed Google Doc: 25 phương pháp giúp bạn ngừng overthinking
2025-07-23 18:06:12,941 - INFO - Syncing note: Working.md
2025-07-23 18:06:13,374 - INFO - Found existing document: Working (ID: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk)
2025-07-23 18:06:13,375 - INFO - Document Working already exists, updating content...
2025-07-23 18:06:15,382 - INFO - Cleared content from document: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk
2025-07-23 18:06:15,383 - INFO - Using existing Google Doc: Working (ID: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk)
2025-07-23 18:06:15,383 - INFO - Applying 280 formatting requests...
2025-07-23 18:06:15,396 - INFO - Validated 280/280 requests
2025-07-23 18:06:15,397 - INFO - Validated 280/280 requests
2025-07-23 18:06:16,727 - INFO - Successfully applied formatting to document: Working
2025-07-23 18:06:16,727 - INFO - Successfully processed Google Doc: Working
2025-07-23 18:06:16,727 - INFO - Syncing note: Thống kê tủ đồ hiện tại.md
2025-07-23 18:06:17,316 - INFO - Found existing document: Thống kê tủ đồ hiện tại (ID: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0)
2025-07-23 18:06:17,317 - INFO - Document Thống kê tủ đồ hiện tại already exists, updating content...
2025-07-23 18:06:18,886 - INFO - Cleared content from document: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0
2025-07-23 18:06:18,886 - INFO - Using existing Google Doc: Thống kê tủ đồ hiện tại (ID: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0)
2025-07-23 18:06:18,886 - INFO - Applying 18 formatting requests...
2025-07-23 18:06:18,886 - INFO - Validated 18/18 requests
2025-07-23 18:06:18,887 - INFO - Validated 18/18 requests
2025-07-23 18:06:19,549 - INFO - Successfully applied formatting to document: Thống kê tủ đồ hiện tại
2025-07-23 18:06:19,549 - INFO - Successfully processed Google Doc: Thống kê tủ đồ hiện tại
2025-07-23 18:06:19,549 - INFO - Syncing note: Note lại từ Huyền Chip.md
2025-07-23 18:06:19,981 - INFO - Found existing document: Note lại từ Huyền Chip (ID: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c)
2025-07-23 18:06:19,981 - INFO - Document Note lại từ Huyền Chip already exists, updating content...
2025-07-23 18:06:21,411 - INFO - Cleared content from document: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c
2025-07-23 18:06:21,411 - INFO - Using existing Google Doc: Note lại từ Huyền Chip (ID: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c)
2025-07-23 18:06:21,411 - INFO - Applying 35 formatting requests...
2025-07-23 18:06:21,418 - INFO - Validated 35/35 requests
2025-07-23 18:06:21,418 - INFO - Validated 35/35 requests
2025-07-23 18:06:22,061 - INFO - Successfully applied formatting to document: Note lại từ Huyền Chip
2025-07-23 18:06:22,061 - INFO - Successfully processed Google Doc: Note lại từ Huyền Chip
2025-07-23 18:06:22,061 - INFO - Syncing note: Công cụ học tiếng Anh.md
2025-07-23 18:06:22,492 - INFO - Found existing document: Công cụ học tiếng Anh (ID: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE)
2025-07-23 18:06:22,492 - INFO - Document Công cụ học tiếng Anh already exists, updating content...
2025-07-23 18:06:24,207 - INFO - Cleared content from document: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE
2025-07-23 18:06:24,207 - INFO - Using existing Google Doc: Công cụ học tiếng Anh (ID: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE)
2025-07-23 18:06:24,207 - INFO - Applying 62 formatting requests...
2025-07-23 18:06:24,209 - INFO - Validated 62/62 requests
2025-07-23 18:06:24,209 - INFO - Validated 62/62 requests
2025-07-23 18:06:24,997 - INFO - Successfully applied formatting to document: Công cụ học tiếng Anh
2025-07-23 18:06:24,997 - INFO - Successfully processed Google Doc: Công cụ học tiếng Anh
2025-07-23 18:06:24,997 - INFO - Syncing note: Software Engineer Roadmap 2025 - The Complete Guide.md
2025-07-23 18:06:25,528 - INFO - Found existing document: Software Engineer Roadmap 2025 - The Complete Guide (ID: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE)
2025-07-23 18:06:25,528 - INFO - Document Software Engineer Roadmap 2025 - The Complete Guide already exists, updating content...
2025-07-23 18:06:27,150 - INFO - Cleared content from document: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE
2025-07-23 18:06:27,151 - INFO - Using existing Google Doc: Software Engineer Roadmap 2025 - The Complete Guide (ID: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE)
2025-07-23 18:06:27,151 - INFO - Applying 141 formatting requests...
2025-07-23 18:06:27,157 - INFO - Validated 141/141 requests
2025-07-23 18:06:27,157 - INFO - Validated 141/141 requests
2025-07-23 18:06:27,985 - INFO - Successfully applied formatting to document: Software Engineer Roadmap 2025 - The Complete Guide
2025-07-23 18:06:27,985 - INFO - Successfully processed Google Doc: Software Engineer Roadmap 2025 - The Complete Guide
2025-07-23 18:06:27,986 - INFO - Syncing note: English with LLM - Mệnh đề quan hệ.md
2025-07-23 18:06:28,451 - INFO - Found existing document: English with LLM - Mệnh đề quan hệ (ID: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8)
2025-07-23 18:06:28,451 - INFO - Document English with LLM - Mệnh đề quan hệ already exists, updating content...
2025-07-23 18:06:29,854 - INFO - Cleared content from document: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8
2025-07-23 18:06:29,854 - INFO - Using existing Google Doc: English with LLM - Mệnh đề quan hệ (ID: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8)
2025-07-23 18:06:29,854 - INFO - Applying 67 formatting requests...
2025-07-23 18:06:29,858 - INFO - Validated 67/67 requests
2025-07-23 18:06:29,858 - INFO - Validated 67/67 requests
2025-07-23 18:06:30,623 - INFO - Successfully applied formatting to document: English with LLM - Mệnh đề quan hệ
2025-07-23 18:06:30,623 - INFO - Successfully processed Google Doc: English with LLM - Mệnh đề quan hệ
2025-07-23 18:06:30,623 - INFO - Syncing note: Development documentations.md
2025-07-23 18:06:31,206 - INFO - Found existing document: Development documentations (ID: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k)
2025-07-23 18:06:31,206 - INFO - Document Development documentations already exists, updating content...
2025-07-23 18:06:32,475 - INFO - Cleared content from document: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k
2025-07-23 18:06:32,475 - INFO - Using existing Google Doc: Development documentations (ID: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k)
2025-07-23 18:06:32,475 - INFO - Applying 9 formatting requests...
2025-07-23 18:06:32,475 - INFO - Validated 9/9 requests
2025-07-23 18:06:32,475 - INFO - Validated 9/9 requests
2025-07-23 18:06:33,010 - INFO - Successfully applied formatting to document: Development documentations
2025-07-23 18:06:33,011 - INFO - Successfully processed Google Doc: Development documentations
2025-07-23 18:06:33,011 - INFO - Syncing note: Freelance.md
2025-07-23 18:06:33,652 - INFO - Found existing document: Freelance (ID: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg)
2025-07-23 18:06:33,652 - INFO - Document Freelance already exists, updating content...
2025-07-23 18:06:35,044 - INFO - Cleared content from document: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg
2025-07-23 18:06:35,044 - INFO - Using existing Google Doc: Freelance (ID: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg)
2025-07-23 18:06:35,044 - INFO - Applying 33 formatting requests...
2025-07-23 18:06:35,044 - INFO - Validated 33/33 requests
2025-07-23 18:06:35,044 - INFO - Validated 33/33 requests
2025-07-23 18:06:35,656 - INFO - Successfully applied formatting to document: Freelance
2025-07-23 18:06:35,656 - INFO - Successfully processed Google Doc: Freelance
2025-07-23 18:06:35,656 - INFO - Syncing note: Phượt.md
2025-07-23 18:06:36,089 - INFO - Found existing document: Phượt (ID: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE)
2025-07-23 18:06:36,089 - INFO - Document Phượt already exists, updating content...
2025-07-23 18:06:37,657 - INFO - Cleared content from document: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE
2025-07-23 18:06:37,657 - INFO - Using existing Google Doc: Phượt (ID: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE)
2025-07-23 18:06:37,657 - INFO - Applying 5 formatting requests...
2025-07-23 18:06:37,657 - INFO - Validated 5/5 requests
2025-07-23 18:06:37,657 - INFO - Validated 5/5 requests
2025-07-23 18:06:38,495 - INFO - Successfully applied formatting to document: Phượt
2025-07-23 18:06:38,495 - INFO - Successfully processed Google Doc: Phượt
2025-07-23 18:06:38,495 - INFO - Syncing note: Luận bàn về async.md
2025-07-23 18:06:38,879 - INFO - Found existing document: Luận bàn về async (ID: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y)
2025-07-23 18:06:38,879 - INFO - Document Luận bàn về async already exists, updating content...
2025-07-23 18:06:40,319 - INFO - Cleared content from document: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y
2025-07-23 18:06:40,320 - INFO - Using existing Google Doc: Luận bàn về async (ID: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y)
2025-07-23 18:06:40,320 - INFO - Applying 10 formatting requests...
2025-07-23 18:06:40,321 - INFO - Validated 10/10 requests
2025-07-23 18:06:40,321 - INFO - Validated 10/10 requests
2025-07-23 18:06:40,984 - INFO - Successfully applied formatting to document: Luận bàn về async
2025-07-23 18:06:40,984 - INFO - Successfully processed Google Doc: Luận bàn về async
2025-07-23 18:06:40,984 - INFO - Syncing note: Clean Code notes.md
2025-07-23 18:06:41,481 - INFO - Found existing document: Clean Code notes (ID: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis)
2025-07-23 18:06:41,481 - INFO - Document Clean Code notes already exists, updating content...
2025-07-23 18:06:42,918 - INFO - Cleared content from document: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis
2025-07-23 18:06:42,918 - INFO - Using existing Google Doc: Clean Code notes (ID: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis)
2025-07-23 18:06:42,918 - INFO - Applying 154 formatting requests...
2025-07-23 18:06:42,920 - INFO - Validated 154/154 requests
2025-07-23 18:06:42,920 - INFO - Validated 154/154 requests
2025-07-23 18:06:43,668 - INFO - Successfully applied formatting to document: Clean Code notes
2025-07-23 18:06:43,668 - INFO - Successfully processed Google Doc: Clean Code notes
2025-07-23 18:06:43,668 - INFO - Syncing note: LLM promt engineering.md
2025-07-23 18:06:44,152 - INFO - Found existing document: LLM promt engineering (ID: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc)
2025-07-23 18:06:44,152 - INFO - Document LLM promt engineering already exists, updating content...
2025-07-23 18:06:45,593 - INFO - Cleared content from document: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc
2025-07-23 18:06:45,594 - INFO - Using existing Google Doc: LLM promt engineering (ID: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc)
2025-07-23 18:06:45,594 - INFO - Applying 125 formatting requests...
2025-07-23 18:06:45,602 - INFO - Validated 125/125 requests
2025-07-23 18:06:45,602 - INFO - Validated 125/125 requests
2025-07-23 18:06:46,214 - INFO - Successfully applied formatting to document: LLM promt engineering
2025-07-23 18:06:46,214 - INFO - Successfully processed Google Doc: LLM promt engineering
2025-07-23 18:06:46,214 - INFO - Syncing note: Fullstack - Full-stack.md
2025-07-23 18:06:46,667 - INFO - Found existing document: Fullstack - Full-stack (ID: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM)
2025-07-23 18:06:46,667 - INFO - Document Fullstack - Full-stack already exists, updating content...
2025-07-23 18:06:48,080 - INFO - Cleared content from document: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM
2025-07-23 18:06:48,081 - INFO - Using existing Google Doc: Fullstack - Full-stack (ID: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM)
2025-07-23 18:06:48,081 - INFO - Applying 9 formatting requests...
2025-07-23 18:06:48,082 - INFO - Validated 9/9 requests
2025-07-23 18:06:48,082 - INFO - Validated 9/9 requests
2025-07-23 18:06:48,673 - INFO - Successfully applied formatting to document: Fullstack - Full-stack
2025-07-23 18:06:48,673 - INFO - Successfully processed Google Doc: Fullstack - Full-stack
2025-07-23 18:06:48,673 - INFO - Syncing note: Life.md
2025-07-23 18:06:49,097 - INFO - Found existing document: Life (ID: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs)
2025-07-23 18:06:49,097 - INFO - Document Life already exists, updating content...
2025-07-23 18:06:50,690 - INFO - Cleared content from document: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs
2025-07-23 18:06:50,691 - INFO - Using existing Google Doc: Life (ID: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs)
2025-07-23 18:06:50,691 - INFO - Applying 5 formatting requests...
2025-07-23 18:06:50,691 - INFO - Validated 5/5 requests
2025-07-23 18:06:50,691 - INFO - Validated 5/5 requests
2025-07-23 18:06:51,563 - INFO - Successfully applied formatting to document: Life
2025-07-23 18:06:51,564 - INFO - Successfully processed Google Doc: Life
2025-07-23 18:06:51,565 - INFO - Syncing note: Business.md
2025-07-23 18:06:51,993 - INFO - Found existing document: Business (ID: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg)
2025-07-23 18:06:51,993 - INFO - Document Business already exists, updating content...
2025-07-23 18:06:53,470 - INFO - Cleared content from document: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg
2025-07-23 18:06:53,470 - INFO - Using existing Google Doc: Business (ID: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg)
2025-07-23 18:06:53,471 - INFO - Applying 4 formatting requests...
2025-07-23 18:06:53,471 - INFO - Validated 4/4 requests
2025-07-23 18:06:53,471 - INFO - Validated 4/4 requests
2025-07-23 18:06:54,117 - INFO - Successfully applied formatting to document: Business
2025-07-23 18:06:54,117 - INFO - Successfully processed Google Doc: Business
2025-07-23 18:06:54,117 - INFO - Syncing note: ARBO.md
2025-07-23 18:06:54,119 - INFO - Found embedded image: Untitled 2 7.png
2025-07-23 18:06:54,119 - INFO - Found embedded image: Untitled 3 4.png
2025-07-23 18:06:54,120 - INFO - Found embedded image: Untitled 4 2.png
2025-07-23 18:06:54,575 - INFO - Found existing document: ARBO (ID: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8)
2025-07-23 18:06:54,575 - INFO - Document ARBO already exists, updating content...
2025-07-23 18:06:55,852 - INFO - Cleared content from document: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8
2025-07-23 18:06:55,852 - INFO - Using existing Google Doc: ARBO (ID: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8)
2025-07-23 18:06:55,852 - INFO - Applying 6 formatting requests...
2025-07-23 18:06:55,853 - INFO - Validated 6/6 requests
2025-07-23 18:06:55,853 - INFO - Validated 6/6 requests
2025-07-23 18:06:56,528 - INFO - Successfully applied formatting to document: ARBO
2025-07-23 18:06:56,528 - INFO - Successfully processed Google Doc: ARBO
2025-07-23 18:06:56,530 - INFO - Processing image: Untitled 4 2.png at position 628
2025-07-23 18:06:56,973 - INFO - Found existing file: Untitled 4 2.png (ID: 1SSmhf8SDT3EyNTDDbn7FH9wzvkg2wrkF)
2025-07-23 18:06:56,973 - INFO - File Untitled 4 2.png already exists, updating...
2025-07-23 18:07:02,590 - INFO - Updated existing file: Untitled 4 2.png (ID: 1SSmhf8SDT3EyNTDDbn7FH9wzvkg2wrkF)
2025-07-23 18:07:06,015 - INFO - Inserted image Untitled 4 2.png into document
2025-07-23 18:07:06,016 - INFO - Successfully inserted image: Untitled 4 2.png at index 628
2025-07-23 18:07:06,428 - INFO - Processing image: Untitled 3 4.png at position 606
2025-07-23 18:07:06,922 - INFO - Found existing file: Untitled 3 4.png (ID: 1IxtfsRZI_UulU08TwIsVjBwjTNGOb1L9)
2025-07-23 18:07:06,922 - INFO - File Untitled 3 4.png already exists, updating...
2025-07-23 18:07:11,661 - INFO - Updated existing file: Untitled 3 4.png (ID: 1IxtfsRZI_UulU08TwIsVjBwjTNGOb1L9)
2025-07-23 18:07:14,463 - INFO - Inserted image Untitled 3 4.png into document
2025-07-23 18:07:14,463 - INFO - Successfully inserted image: Untitled 3 4.png at index 606
2025-07-23 18:07:14,885 - INFO - Processing image: Untitled 2 7.png at position 584
2025-07-23 18:07:15,371 - INFO - Found existing file: Untitled 2 7.png (ID: 1xe_8fd-SjQFVYSIR92eLX03RFCJjKE0H)
2025-07-23 18:07:15,371 - INFO - File Untitled 2 7.png already exists, updating...
2025-07-23 18:07:20,147 - INFO - Updated existing file: Untitled 2 7.png (ID: 1xe_8fd-SjQFVYSIR92eLX03RFCJjKE0H)
2025-07-23 18:07:22,794 - INFO - Inserted image Untitled 2 7.png into document
2025-07-23 18:07:22,795 - INFO - Successfully inserted image: Untitled 2 7.png at index 584
2025-07-23 18:07:23,216 - INFO - Syncing note: Note câu hỏi phỏng vấn Laravel.md
2025-07-23 18:07:23,217 - INFO - Found embedded image: Screenshot 2023-05-27 144607 1.png
2025-07-23 18:07:23,666 - INFO - Found existing document: Note câu hỏi phỏng vấn Laravel (ID: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A)
2025-07-23 18:07:23,666 - INFO - Document Note câu hỏi phỏng vấn Laravel already exists, updating content...
2025-07-23 18:07:24,998 - INFO - Cleared content from document: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A
2025-07-23 18:07:24,998 - INFO - Using existing Google Doc: Note câu hỏi phỏng vấn Laravel (ID: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A)
2025-07-23 18:07:24,998 - INFO - Applying 11 formatting requests...
2025-07-23 18:07:24,999 - INFO - Validated 11/11 requests
2025-07-23 18:07:24,999 - INFO - Validated 11/11 requests
2025-07-23 18:07:25,606 - INFO - Successfully applied formatting to document: Note câu hỏi phỏng vấn Laravel
2025-07-23 18:07:25,607 - INFO - Successfully processed Google Doc: Note câu hỏi phỏng vấn Laravel
2025-07-23 18:07:25,607 - INFO - Processing image: Screenshot 2023-05-27 144607 1.png at position 1
2025-07-23 18:07:26,034 - INFO - Found existing file: Screenshot 2023-05-27 144607 1.png (ID: 1fNqpl_OCBRSYkibWH9TXSXDYnneODe4R)
2025-07-23 18:07:26,034 - INFO - File Screenshot 2023-05-27 144607 1.png already exists, updating...
2025-07-23 18:07:30,267 - INFO - Updated existing file: Screenshot 2023-05-27 144607 1.png (ID: 1fNqpl_OCBRSYkibWH9TXSXDYnneODe4R)
2025-07-23 18:07:32,903 - INFO - Inserted image Screenshot 2023-05-27 144607 1.png into document
2025-07-23 18:07:32,903 - INFO - Successfully inserted image: Screenshot 2023-05-27 144607 1.png at index 1
2025-07-23 18:07:33,411 - INFO - Syncing note: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md
2025-07-23 18:07:33,857 - INFO - Found existing document: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow (ID: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw)
2025-07-23 18:07:33,857 - INFO - Document viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow already exists, updating content...
2025-07-23 18:07:35,658 - INFO - Cleared content from document: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw
2025-07-23 18:07:35,659 - INFO - Using existing Google Doc: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow (ID: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw)
2025-07-23 18:07:35,659 - INFO - Applying 346 formatting requests...
2025-07-23 18:07:35,666 - INFO - Validated 346/346 requests
2025-07-23 18:07:35,667 - INFO - Validated 346/346 requests
2025-07-23 18:07:37,389 - INFO - Successfully applied formatting to document: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow
2025-07-23 18:07:37,389 - INFO - Successfully processed Google Doc: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow
2025-07-23 18:07:37,389 - INFO - Syncing note: Machine learning - Deep Learning - AI - ML - DL.md
2025-07-23 18:07:37,877 - INFO - Found existing document: Machine learning - Deep Learning - AI - ML - DL (ID: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y)
2025-07-23 18:07:37,877 - INFO - Document Machine learning - Deep Learning - AI - ML - DL already exists, updating content...
2025-07-23 18:07:39,599 - INFO - Cleared content from document: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y
2025-07-23 18:07:39,600 - INFO - Using existing Google Doc: Machine learning - Deep Learning - AI - ML - DL (ID: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y)
2025-07-23 18:07:39,600 - INFO - Applying 412 formatting requests...
2025-07-23 18:07:39,614 - INFO - Validated 412/412 requests
2025-07-23 18:07:39,614 - INFO - Validated 412/412 requests
2025-07-23 18:07:40,905 - INFO - Successfully applied formatting to document: Machine learning - Deep Learning - AI - ML - DL
2025-07-23 18:07:40,906 - INFO - Successfully processed Google Doc: Machine learning - Deep Learning - AI - ML - DL
2025-07-23 18:07:40,906 - INFO - Syncing note: Note ebook Thuật toán của thầy Lê Minh Hoàng.md
2025-07-23 18:07:41,352 - INFO - Found existing document: Note ebook Thuật toán của thầy Lê Minh Hoàng (ID: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM)
2025-07-23 18:07:41,353 - INFO - Document Note ebook Thuật toán của thầy Lê Minh Hoàng already exists, updating content...
2025-07-23 18:07:42,880 - INFO - Cleared content from document: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM
2025-07-23 18:07:42,880 - INFO - Using existing Google Doc: Note ebook Thuật toán của thầy Lê Minh Hoàng (ID: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM)
2025-07-23 18:07:42,880 - INFO - Applying 5 formatting requests...
2025-07-23 18:07:42,880 - INFO - Validated 5/5 requests
2025-07-23 18:07:42,880 - INFO - Validated 5/5 requests
2025-07-23 18:07:43,479 - INFO - Successfully applied formatting to document: Note ebook Thuật toán của thầy Lê Minh Hoàng
2025-07-23 18:07:43,479 - INFO - Successfully processed Google Doc: Note ebook Thuật toán của thầy Lê Minh Hoàng
2025-07-23 18:07:43,479 - INFO - Syncing note: English with LLM - Câu nhấn mạnh.md
2025-07-23 18:07:43,911 - INFO - Found existing document: English with LLM - Câu nhấn mạnh (ID: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg)
2025-07-23 18:07:43,911 - INFO - Document English with LLM - Câu nhấn mạnh already exists, updating content...
2025-07-23 18:07:45,477 - INFO - Cleared content from document: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg
2025-07-23 18:07:45,477 - INFO - Using existing Google Doc: English with LLM - Câu nhấn mạnh (ID: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg)
2025-07-23 18:07:45,477 - INFO - Applying 60 formatting requests...
2025-07-23 18:07:45,481 - INFO - Validated 60/60 requests
2025-07-23 18:07:45,481 - INFO - Validated 60/60 requests
2025-07-23 18:07:46,233 - INFO - Successfully applied formatting to document: English with LLM - Câu nhấn mạnh
2025-07-23 18:07:46,233 - INFO - Successfully processed Google Doc: English with LLM - Câu nhấn mạnh
2025-07-23 18:07:46,233 - INFO - Syncing note: Nginx.md
2025-07-23 18:07:46,610 - INFO - Found existing document: Nginx (ID: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso)
2025-07-23 18:07:46,610 - INFO - Document Nginx already exists, updating content...
2025-07-23 18:07:48,159 - INFO - Cleared content from document: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso
2025-07-23 18:07:48,159 - INFO - Using existing Google Doc: Nginx (ID: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso)
2025-07-23 18:07:48,159 - INFO - Applying 11 formatting requests...
2025-07-23 18:07:48,160 - INFO - Validated 11/11 requests
2025-07-23 18:07:48,160 - INFO - Validated 11/11 requests
2025-07-23 18:07:48,974 - INFO - Successfully applied formatting to document: Nginx
2025-07-23 18:07:48,974 - INFO - Successfully processed Google Doc: Nginx
2025-07-23 18:07:48,974 - INFO - Syncing note: Chỗ mua đồ.md
2025-07-23 18:07:49,434 - INFO - Found existing document: Chỗ mua đồ (ID: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8)
2025-07-23 18:07:49,435 - INFO - Document Chỗ mua đồ already exists, updating content...
2025-07-23 18:07:50,922 - INFO - Cleared content from document: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8
2025-07-23 18:07:50,922 - INFO - Using existing Google Doc: Chỗ mua đồ (ID: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8)
2025-07-23 18:07:50,922 - INFO - Applying 18 formatting requests...
2025-07-23 18:07:50,922 - INFO - Validated 18/18 requests
2025-07-23 18:07:50,922 - INFO - Validated 18/18 requests
2025-07-23 18:07:51,512 - INFO - Successfully applied formatting to document: Chỗ mua đồ
2025-07-23 18:07:51,512 - INFO - Successfully processed Google Doc: Chỗ mua đồ
2025-07-23 18:07:51,512 - INFO - Syncing note: AI support for coding.md
2025-07-23 18:07:52,093 - INFO - Found existing document: AI support for coding (ID: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q)
2025-07-23 18:07:52,093 - INFO - Document AI support for coding already exists, updating content...
2025-07-23 18:07:53,663 - INFO - Cleared content from document: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q
2025-07-23 18:07:53,663 - INFO - Using existing Google Doc: AI support for coding (ID: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q)
2025-07-23 18:07:53,663 - INFO - Applying 88 formatting requests...
2025-07-23 18:07:53,668 - INFO - Validated 88/88 requests
2025-07-23 18:07:53,669 - INFO - Validated 88/88 requests
2025-07-23 18:07:54,356 - INFO - Successfully applied formatting to document: AI support for coding
2025-07-23 18:07:54,357 - INFO - Successfully processed Google Doc: AI support for coding
2025-07-23 18:07:54,357 - INFO - Syncing note: Cách đặt câu hỏi cho ChatGPT.md
2025-07-23 18:07:54,798 - INFO - Found existing document: Cách đặt câu hỏi cho ChatGPT (ID: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q)
2025-07-23 18:07:54,798 - INFO - Document Cách đặt câu hỏi cho ChatGPT already exists, updating content...
2025-07-23 18:07:56,274 - INFO - Cleared content from document: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q
2025-07-23 18:07:56,274 - INFO - Using existing Google Doc: Cách đặt câu hỏi cho ChatGPT (ID: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q)
2025-07-23 18:07:56,274 - INFO - Applying 49 formatting requests...
2025-07-23 18:07:56,285 - INFO - Validated 49/49 requests
2025-07-23 18:07:56,285 - INFO - Validated 49/49 requests
2025-07-23 18:07:57,017 - INFO - Successfully applied formatting to document: Cách đặt câu hỏi cho ChatGPT
2025-07-23 18:07:57,017 - INFO - Successfully processed Google Doc: Cách đặt câu hỏi cho ChatGPT
2025-07-23 18:07:57,017 - INFO - Syncing note: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC.md
2025-07-23 18:07:57,524 - INFO - Found existing document: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC (ID: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI)
2025-07-23 18:07:57,524 - INFO - Document English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC already exists, updating content...
2025-07-23 18:07:59,031 - INFO - Cleared content from document: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI
2025-07-23 18:07:59,031 - INFO - Using existing Google Doc: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC (ID: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI)
2025-07-23 18:07:59,031 - INFO - Applying 413 formatting requests...
2025-07-23 18:07:59,041 - INFO - Validated 413/413 requests
2025-07-23 18:07:59,042 - INFO - Validated 413/413 requests
2025-07-23 18:08:00,068 - INFO - Successfully applied formatting to document: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC
2025-07-23 18:08:00,068 - INFO - Successfully processed Google Doc: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC
2025-07-23 18:08:00,068 - INFO - Syncing note: Râm Generation.md
2025-07-23 18:08:00,508 - INFO - Found existing document: Râm Generation (ID: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8)
2025-07-23 18:08:00,509 - INFO - Document Râm Generation already exists, updating content...
2025-07-23 18:08:01,661 - INFO - Cleared content from document: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8
2025-07-23 18:08:01,661 - INFO - Using existing Google Doc: Râm Generation (ID: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8)
2025-07-23 18:08:01,661 - INFO - Applying 24 formatting requests...
2025-07-23 18:08:01,662 - INFO - Validated 24/24 requests
2025-07-23 18:08:01,662 - INFO - Validated 24/24 requests
2025-07-23 18:08:02,296 - INFO - Successfully applied formatting to document: Râm Generation
2025-07-23 18:08:02,296 - INFO - Successfully processed Google Doc: Râm Generation
2025-07-23 18:08:02,296 - INFO - Syncing note: Ollama.md
2025-07-23 18:08:02,762 - INFO - Found existing document: Ollama (ID: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8)
2025-07-23 18:08:02,762 - INFO - Document Ollama already exists, updating content...
2025-07-23 18:08:04,022 - INFO - Cleared content from document: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8
2025-07-23 18:08:04,022 - INFO - Using existing Google Doc: Ollama (ID: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8)
2025-07-23 18:08:04,022 - INFO - Applying 5 formatting requests...
2025-07-23 18:08:04,022 - INFO - Validated 5/5 requests
2025-07-23 18:08:04,022 - INFO - Validated 5/5 requests
2025-07-23 18:08:04,559 - INFO - Successfully applied formatting to document: Ollama
2025-07-23 18:08:04,559 - INFO - Successfully processed Google Doc: Ollama
2025-07-23 18:08:04,559 - INFO - Syncing note: English with LLM - Từ nối.md
2025-07-23 18:08:05,005 - INFO - Found existing document: English with LLM - Từ nối (ID: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw)
2025-07-23 18:08:05,005 - INFO - Document English with LLM - Từ nối already exists, updating content...
2025-07-23 18:08:06,354 - INFO - Cleared content from document: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw
2025-07-23 18:08:06,354 - INFO - Using existing Google Doc: English with LLM - Từ nối (ID: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw)
2025-07-23 18:08:06,355 - INFO - Applying 51 formatting requests...
2025-07-23 18:08:06,358 - INFO - Validated 51/51 requests
2025-07-23 18:08:06,358 - INFO - Validated 51/51 requests
2025-07-23 18:08:07,066 - INFO - Successfully applied formatting to document: English with LLM - Từ nối
2025-07-23 18:08:07,066 - INFO - Successfully processed Google Doc: English with LLM - Từ nối
2025-07-23 18:08:07,066 - INFO - Syncing note: Sống Platform.md
2025-07-23 18:08:07,521 - INFO - Found existing document: Sống Platform (ID: 1HSh2u3SnqpVWCXWcwqrDLTc---iEj8B4j3kzo9WBh4w)
2025-07-23 18:08:07,521 - INFO - Document Sống Platform already exists, updating content...
2025-07-23 18:08:08,768 - INFO - Cleared content from document: 1HSh2u3SnqpVWCXWcwqrDLTc---iEj8B4j3kzo9WBh4w
2025-07-23 18:08:08,768 - INFO - Using existing Google Doc: Sống Platform (ID: 1HSh2u3SnqpVWCXWcwqrDLTc---iEj8B4j3kzo9WBh4w)
2025-07-23 18:08:08,768 - INFO - Applying 32 formatting requests...
2025-07-23 18:08:08,768 - INFO - Validated 32/32 requests
2025-07-23 18:08:08,768 - INFO - Validated 32/32 requests
2025-07-23 18:08:09,454 - INFO - Successfully applied formatting to document: Sống Platform
2025-07-23 18:08:09,455 - INFO - Successfully processed Google Doc: Sống Platform
2025-07-23 18:08:09,455 - INFO - Syncing note: Top 10 câu hỏi phỏng vấn System Design và Microservices.md
2025-07-23 18:08:09,456 - INFO - Found embedded image: a03077b24546810e9aabd32f2afe2608_MD5.webp
2025-07-23 18:08:09,946 - INFO - Found existing document: Top 10 câu hỏi phỏng vấn System Design và Microservices (ID: 1egh2KktHKZYo2GBFc2PjkurmMv5seJ0T0Eji_wJbODQ)
2025-07-23 18:08:09,946 - INFO - Document Top 10 câu hỏi phỏng vấn System Design và Microservices already exists, updating content...
2025-07-23 18:08:11,085 - ERROR - Error clearing document content 1egh2KktHKZYo2GBFc2PjkurmMv5seJ0T0Eji_wJbODQ: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1egh2KktHKZYo2GBFc2PjkurmMv5seJ0T0Eji_wJbODQ:batchUpdate?alt=json returned "Invalid requests[0].deleteContentRange: The range should not be empty.". Details: "Invalid requests[0].deleteContentRange: The range should not be empty.">
2025-07-23 18:08:11,086 - WARNING - Failed to update Top 10 câu hỏi phỏng vấn System Design và Microservices, creating new document...
2025-07-23 18:08:12,535 - INFO - Created new Google Doc: Top 10 câu hỏi phỏng vấn System Design và Microservices (ID: 12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4)
2025-07-23 18:08:14,629 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:08:14,630 - INFO - Applying 50 formatting requests...
2025-07-23 18:08:14,639 - INFO - Validated 50/50 requests
2025-07-23 18:08:14,639 - INFO - Validated 50/50 requests
2025-07-23 18:08:15,273 - ERROR - Error applying formatting to Top 10 câu hỏi phỏng vấn System Design và Microservices: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Invalid requests[48].insertText: The insertion index cannot be within a grapheme cluster.". Details: "Invalid requests[48].insertText: The insertion index cannot be within a grapheme cluster.">
2025-07-23 18:08:15,273 - ERROR - Problematic requests: [{'insertText': {'location': {'index': 1}, 'text': 'Nhà tuyển dụng thường yêu cầu ứng viên ở vị trí Senior Software Engineer (Backend) cần giải quyết được một số vấn đề của hệ thống Microservices và khả năng thiết kế hệ thống. Dưới đây là một số câu hỏi và gợi ý cách trả lời để bạn tham khảo về 2 chủ đề trên.\n'}}, {'insertText': {'location': {'index': 260}, 'text': 'Lưu ý: những câu hỏi và gợi ý dưới đây chỉ mang tính tham khảo. Bạn nên tự xây dựng câu trả lời hoàn chỉnh của riêng mình.\n'}}, {'insertText': {'location': {'index': 383}, 'text': '0.1. System Design:\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 383, 'endIndex': 402}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 403}, 'text': '0.1.1. Bạn trình bày giúp mình về định lý CAP?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 403, 'endIndex': 449}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 450}, 'text': 'Gợi ý:\nBạn có thể tham khảo\xa0video này - https://www.youtube.com/watch?v=BHqjEjzAicA\nNgoài ra, bạn nên tìm hiểu thêm sự khác nhau sự Consistency trong định lý CAP và Consistency trong tính chất ACID.\n'}}, {'insertText': {'location': {'index': 649}, 'text': '0.1.2. Một bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khiến cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 649, 'endIndex': 779}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 780}, 'text': 'Gợi ý:\nĐầu tiên, bạn nên làm rõ ý nghĩa của dữ liệu trong bảng và độ lớn của bảng để đưa ra những giải pháp phù hợp. Và nên trình bày theo trình tự từ đơn giản đến phức tạp.\n'}}, {'insertText': {'location': {'index': 954}, 'text': '0.1.3. Thiết kế hệ thống TinyURL?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 954, 'endIndex': 987}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 988}, 'text': 'Gợi ý:\nĐây là một câu hỏi rất phổ biến vì nó có tính phân hoá cao. Độ phức tạp của hệ thống phụ thuộc vào yêu cầu của nhà tuyển dụng. Bạn nên làm rõ những yêu cầu và chú ý một số vấn đề sau:\n'}}, {'insertText': {'location': {'index': 1179}, 'text': 'Độ dài của short URL\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1179, 'endIndex': 1199}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1200}, 'text': 'Các yêu cầu về check trùng. Ví dụ khi shorten 1 URL nhiều lần, có cần trả về cùng 1 short URL không?\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1200, 'endIndex': 1300}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1301}, 'text': 'Bạn sử dụng DB nào?\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1301, 'endIndex': 1320}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1321}, 'text': 'Bạn xử lý đụng độ (collision) như nào?\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1321, 'endIndex': 1359}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1360}, 'text': '[IMAGE_PLACEHOLDER_0]\n\xa0Vẹt xám châu Phi được cho rằng là loài chim thông minh nhất thế giới. Chúng có những khả năng đặc biệt như bước chước giọng người, giải toán và có khả năng nhận thức. Đây là Alex (1976 - 2007) được biết đến là chú vẹt xám châu Phi nổi tiếng nhất trong lịch sử. Alex sở hữu hơn 100 âm thanh của các đồ vật và hành động khác nhau như tiếng gà gáy, tiếng lợn kêu, âm thanh của phi thuyền, tiếng thở dài, … Ngoài ra, Alex có thể sắp xếp đồ vật, có thể đếm đến 6, …\n'}}, {'insertText': {'location': {'index': 1844}, 'text': '0.2. Microservice:\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1844, 'endIndex': 1862}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1863}, 'text': '0.2.1. Bạn hãy so sánh ưu nhược điểm của monolithic và microservices?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1863, 'endIndex': 1932}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1933}, 'text': 'Gợi ý: Để câu trả lời thuyết phục hơn, bạn có thể nêu ra những yếu tố ảnh hưởng tới từng ưu nhược điểm.\n'}}, {'insertText': {'location': {'index': 2037}, 'text': '0.2.2. Tại sao bạn lại chia service như này?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2037, 'endIndex': 2081}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2082}, 'text': 'Gợi ý: Đây là một câu hỏi khó. Nhiều bạn đề cập tới Domain-Driven Design nhưng hãy cẩn thận khi đề cập tới Domain-Driven Design. Bạn cần nắm rõ nghiệp vụ và cách model business bạn sử dụng là gì.\n'}}, {'insertText': {'location': {'index': 2278}, 'text': '0.2.3. Order được xử lý lần lượt qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2278, 'endIndex': 2458}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2459}, 'text': 'Gợi ý: Bạn có thể sử dụng message queue có khả năng persist được message.\n'}}, {'insertText': {'location': {'index': 2533}, 'text': '0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2533, 'endIndex': 2675}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2676}, 'text': 'Gợi ý: Bạn cần dựa vào yêu cầu về tính nhất quán và tính đúng đắn của dữ liệu. Từ đó để đưa ra giải pháp như thêm một adapter layer hoặc đồng bộ dữ liệu từ B sang A. Lưu ý để đảm bảo tính đúng đắn của dữ liệu so với tài liệu, logic không thể dựa hoàn toàn vào tài liệu, ta cần kiểm tra dữ liệu thực tế trên môi trường prod và nonprod.\n'}}, {'insertText': {'location': {'index': 3011}, 'text': '0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order được điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. Làm sao để hệ thống trả lại response cho frontend?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3011, 'endIndex': 3295}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3296}, 'text': 'Gợi ý: Có nhiều cách đáp ứng được yêu cầu trên. Bạn nên đánh giá về mặt hiệu năng và khả năng mở rộng của từng giải pháp.\n'}}, {'insertText': {'location': {'index': 3418}, 'text': '0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3418, 'endIndex': 3578}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3579}, 'text': 'Gợi ý: Đầu tiên bạn nên review lại thiết kế luồng, đặc biệt chú ý tới điểm giao tiếp với các third party nếu có. Sau đó cần nêu ra 1 quy trình sử dụng các công cụ monitor và distributed tracing để xác định được issue làm ở đâu. Cuối cùng đưa ra những giải pháp khắc phục như tối ưu code, query và cấp thêm tài nguyên cho các service, …\n'}}, {'insertText': {'location': {'index': 3915}, 'text': '0.3. Khác:\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3915, 'endIndex': 3925}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3926}, 'text': '0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3926, 'endIndex': 4002}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 4003}, 'text': 'Gợi ý: Đây là một câu hỏi rất hay gặp thế nên bạn cần chuẩn bị kỹ. Mỗi người có kinh nghiệm, trải nghiệm khác nhau và gặp những vấn đề khác nhau. Bạn nên chọn ra và chuẩn bị kỹ 3 vấn đề khó nhất bạn gặp phải. Thêm nữa, cần đảm bảo rằng độ phức tạp của 3 vấn đề tương xứng với level hiện của bạn hoặc level đang hướng tới.\n'}}, {'insertText': {'location': {'index': 4325}, 'text': 'Nếu mọi người có câu trả lời hoặc câu hỏi khác thì comment ở dưới giúp mình nha 👇\nHẹn mọi người ở phần 2 với những câu hỏi về những chủ đề khác 👋\n'}}, {'insertText': {'location': {'index': 4471}, 'text': 'Nếu bạn thấy hay thì cho mình xin 1 upvote 🔼 và share nhé.\nCám ơn mọi người rất nhiều 🙏\n'}}, {'insertText': {'location': {'index': 4559}, 'text': '📚️ Ronin Engineer:\xa0https://ronin-engineer.github.io/register-post\n🏢 System Design VN:\xa0https://fb.com/groups/systemdesign.vn\n'}}]
2025-07-23 18:08:15,273 - INFO - Attempting fallback approach for grapheme cluster error...
2025-07-23 18:08:15,274 - INFO - Applying 50 requests individually as fallback...
2025-07-23 18:08:33,261 - WARNING - Skipping request 48 due to grapheme cluster error: {'insertText': {'location': {'index': 4471}, 'text': 'Nếu bạn thấy hay thì cho mình xin 1 upvote 🔼 và share nhé.\nCám ơn mọi người rất nhiều 🙏\n'}}
2025-07-23 18:08:33,608 - INFO - Successfully applied adjusted request 48
2025-07-23 18:08:33,954 - INFO - Fallback completed: 50/50 requests applied
2025-07-23 18:08:33,957 - INFO - Processing image: a03077b24546810e9aabd32f2afe2608_MD5.webp at position 1360
2025-07-23 18:08:34,383 - INFO - Found existing file: a03077b24546810e9aabd32f2afe2608_MD5.webp (ID: 1iXfTISL2ztsHCJfWwjJcs1kOrdi3qYWy)
2025-07-23 18:08:34,383 - INFO - File a03077b24546810e9aabd32f2afe2608_MD5.webp already exists, updating...
2025-07-23 18:08:38,603 - INFO - Updated existing file: a03077b24546810e9aabd32f2afe2608_MD5.webp (ID: 1iXfTISL2ztsHCJfWwjJcs1kOrdi3qYWy)
2025-07-23 18:08:39,990 - ERROR - Error inserting image a03077b24546810e9aabd32f2afe2608_MD5.webp: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.". Details: "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.">
2025-07-23 18:08:39,991 - ERROR - Failed to insert image: a03077b24546810e9aabd32f2afe2608_MD5.webp
2025-07-23 18:08:39,991 - INFO - Syncing note: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai).md
2025-07-23 18:08:40,508 - INFO - Found existing document: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) (ID: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA)
2025-07-23 18:08:40,510 - INFO - Document Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) already exists, updating content...
2025-07-23 18:08:42,060 - INFO - Cleared content from document: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA
2025-07-23 18:08:42,060 - INFO - Using existing Google Doc: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) (ID: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA)
2025-07-23 18:08:42,060 - INFO - Applying 68 formatting requests...
2025-07-23 18:08:42,064 - INFO - Validated 68/68 requests
2025-07-23 18:08:42,064 - INFO - Validated 68/68 requests
2025-07-23 18:08:42,771 - INFO - Successfully applied formatting to document: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai)
2025-07-23 18:08:42,771 - INFO - Successfully processed Google Doc: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai)
2025-07-23 18:08:42,771 - INFO - Syncing note: Kho chung IT.md
2025-07-23 18:08:43,219 - INFO - Found existing document: Kho chung IT (ID: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI)
2025-07-23 18:08:43,219 - INFO - Document Kho chung IT already exists, updating content...
2025-07-23 18:08:44,685 - INFO - Cleared content from document: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI
2025-07-23 18:08:44,686 - INFO - Using existing Google Doc: Kho chung IT (ID: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI)
2025-07-23 18:08:44,686 - INFO - Applying 57 formatting requests...
2025-07-23 18:08:44,688 - INFO - Validated 57/57 requests
2025-07-23 18:08:44,688 - INFO - Validated 57/57 requests
2025-07-23 18:08:45,369 - INFO - Successfully applied formatting to document: Kho chung IT
2025-07-23 18:08:45,369 - INFO - Successfully processed Google Doc: Kho chung IT
2025-07-23 18:08:45,369 - INFO - Syncing note: Target of users in a workspace.md
2025-07-23 18:08:45,839 - INFO - Found existing document: Target of users in a workspace (ID: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM)
2025-07-23 18:08:45,839 - INFO - Document Target of users in a workspace already exists, updating content...
2025-07-23 18:08:47,367 - INFO - Cleared content from document: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM
2025-07-23 18:08:47,368 - INFO - Using existing Google Doc: Target of users in a workspace (ID: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM)
2025-07-23 18:08:47,368 - INFO - Applying 47 formatting requests...
2025-07-23 18:08:47,372 - INFO - Validated 47/47 requests
2025-07-23 18:08:47,372 - INFO - Validated 47/47 requests
2025-07-23 18:08:47,999 - INFO - Successfully applied formatting to document: Target of users in a workspace
2025-07-23 18:08:47,999 - INFO - Successfully processed Google Doc: Target of users in a workspace
2025-07-23 18:08:47,999 - INFO - Syncing note: Linter.md
2025-07-23 18:08:48,446 - INFO - Found existing document: Linter (ID: 11snTtHIqRmaVMRPbKwCmqI10qEm0RtkYH3O9FaCr2JI)
2025-07-23 18:08:48,446 - INFO - Document Linter already exists, updating content...
2025-07-23 18:08:49,997 - ERROR - Error clearing document content 11snTtHIqRmaVMRPbKwCmqI10qEm0RtkYH3O9FaCr2JI: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/11snTtHIqRmaVMRPbKwCmqI10qEm0RtkYH3O9FaCr2JI:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_unit': '1/min/{project}/{user}', 'consumer': 'projects/561286222532', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_location': 'global', 'quota_limit_value': '60', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:49,997 - WARNING - Failed to update Linter, creating new document...
2025-07-23 18:08:51,363 - INFO - Created new Google Doc: Linter (ID: 1oaTR8u3SppuLShrHnXiFO46-eJPGVjAs0FGOZ1cm02Q)
2025-07-23 18:08:53,584 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:08:53,584 - INFO - Applying 124 formatting requests...
2025-07-23 18:08:53,588 - INFO - Validated 124/124 requests
2025-07-23 18:08:53,588 - INFO - Validated 124/124 requests
2025-07-23 18:08:53,889 - ERROR - Error applying formatting to Linter: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1oaTR8u3SppuLShrHnXiFO46-eJPGVjAs0FGOZ1cm02Q:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:53,889 - ERROR - Problematic requests: [{'insertText': {'location': {'index': 1}, 'text': 'relates:\n  - "[[Backend - Back-end]]"\n  - "[[Frontend - Front-end]]"\n  - "[[Microservices]]"\n  - "[[Kho chung IT]]"\n'}}, {'insertText': {'location': {'index': 117}, 'text': '1. ESLint\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 117, 'endIndex': 126}, 'paragraphStyle': {'namedStyleType': 'HEADING_1'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 127}, 'text': '2. OXC\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 127, 'endIndex': 133}, 'paragraphStyle': {'namedStyleType': 'HEADING_1'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 134}, 'text': 'Github: https://github.com/oxc-project/oxc\n'}}, {'insertText': {'location': {'index': 177}, 'text': 'Oxc (Oxidation Compiler) is a comprehensive collection of high-performance JavaScript and TypeScript tools written in Rust. It aims to provide fast, reliable, and scalable tooling for JavaScript ecosystems, including parsing, linting, formatting, transforming, minifying, and module resolution.\n'}}, {'insertText': {'location': {'index': 472}, 'text': '2.1. Key Features and Components\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 472, 'endIndex': 504}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 505}, 'text': '2.1.1. Parser and AST\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 505, 'endIndex': 526}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 527}, 'text': 'Oxc includes its own JavaScript and TypeScript parser supporting JSX and TSX.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 527, 'endIndex': 604}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 605}, 'text': 'The parser is one of the fastest Rust-based parsers available, outperforming competitors like swc (3x faster) and Biome (5x faster).\n'}}, {'createParagraphBullets': {'range': {'startIndex': 605, 'endIndex': 737}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 738}, 'text': 'Uses memory arena allocation and compact string storage for performance.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 738, 'endIndex': 810}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 811}, 'text': 'The AST design is more precise than estree, removing ambiguous nodes and aligning closely with ECMAScript specifications (e.g., distinct types for identifiers).\n'}}, {'createParagraphBullets': {'range': {'startIndex': 811, 'endIndex': 971}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 972}, 'text': 'Parser delegates scope binding and symbol resolution to a semantic analyzer for efficiency.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 972, 'endIndex': 1063}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1064}, 'text': '2.1.2. Linter (oxlint)\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1064, 'endIndex': 1086}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1087}, 'text': 'Comes with 93 rules enabled by default out of 430+ total.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1087, 'endIndex': 1144}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1145}, 'text': 'Requires no configuration to start; can be run immediately with npx oxlint@latest.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1145, 'endIndex': 1227}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1228}, 'text': 'Extremely fast, 50-100x faster than ESLint, scales with CPU cores.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1228, 'endIndex': 1294}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1295}, 'text': 'Binary size is about 5MB, much smaller than ESLint plus plugins.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1295, 'endIndex': 1359}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1360}, 'text': 'Can be used standalone without Node.js, suitable for CI environments.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1360, 'endIndex': 1429}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1430}, 'text': '2.1.3. Resolver (oxc_resolver)\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1430, 'endIndex': 1460}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1461}, 'text': 'Handles JavaScript module resolution, crucial for multi-file analysis and bundling.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1461, 'endIndex': 1544}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1545}, 'text': 'Production-ready and used by projects like Rolldown.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1545, 'endIndex': 1597}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1598}, 'text': '2.1.4. Transformer (oxc-transform)\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1598, 'endIndex': 1632}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1633}, 'text': 'Transforms modern ECMAScript to older versions for compatibility.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1633, 'endIndex': 1698}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1699}, 'text': 'Supports TypeScript and React transformations.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1699, 'endIndex': 1745}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1746}, 'text': 'Available for experimentation.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1746, 'endIndex': 1776}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1777}, 'text': '2.1.5. Minifier\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1777, 'endIndex': 1792}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1793}, 'text': 'Aims to deliver fast minification without sacrificing compression quality.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1793, 'endIndex': 1867}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1868}, 'text': 'Prototype in progress, porting test cases from popular minifiers (Google Closure Compiler, terser, esbuild).\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1868, 'endIndex': 1976}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 1977}, 'text': 'Targets faster minification times with high compression.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 1977, 'endIndex': 2033}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2034}, 'text': '2.1.6. Formatter\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2034, 'endIndex': 2050}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2051}, 'text': 'Research ongoing to create a flexible, less opinionated formatter alternative to Prettier.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2051, 'endIndex': 2141}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2142}, 'text': 'Prototype is under development.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2142, 'endIndex': 2173}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2174}, 'text': '2.1.7. Isolated Declarations\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2174, 'endIndex': 2202}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2203}, 'text': 'Emits TypeScript declarations without using the TypeScript compiler.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2203, 'endIndex': 2271}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2272}, 'text': 'Benchmark shows it is at least 20x faster than the official TypeScript compiler.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2272, 'endIndex': 2352}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2353}, 'text': '2.2. Performance Highlights\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2353, 'endIndex': 2380}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2381}, 'text': 'Parser is the fastest Rust-based JavaScript/TypeScript parser.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2381, 'endIndex': 2443}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2444}, 'text': 'Linter is massively faster than ESLint, benefiting from multi-threading and efficient AST traversal.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2444, 'endIndex': 2544}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2545}, 'text': 'Rust compilation speed is optimized to minimize developer workflow impact.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2545, 'endIndex': 2619}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2620}, 'text': 'Linter can lint thousands of files in under a second to a few seconds depending on size.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2620, 'endIndex': 2708}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2709}, 'text': '2.3. Usage and Integration\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2709, 'endIndex': 2735}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2736}, 'text': 'Rust crates available individually and as an umbrella crate oxc.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2736, 'endIndex': 2800}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2801}, 'text': 'Node.js bindings via N-API for parser and transformer.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2801, 'endIndex': 2855}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2856}, 'text': 'WebAssembly packages available for parser.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2856, 'endIndex': 2898}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 2899}, 'text': 'Used by notable projects and companies such as Rolldown, Nova engine, Preact, Shopify, ByteDance, Shopee, Biome, and swc-node.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 2899, 'endIndex': 3025}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3026}, 'text': '2.4. Development and Testing\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3026, 'endIndex': 3054}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3055}, 'text': 'Strong emphasis on correctness and reliability.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3055, 'endIndex': 3102}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3103}, 'text': 'Extensive test infrastructure includes:\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3103, 'endIndex': 3142}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3143}, 'text': 'Conformance testing with Test262, Babel, TypeScript.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3143, 'endIndex': 3195}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3196}, 'text': 'Fuzzing.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3196, 'endIndex': 3204}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3205}, 'text': 'Linter snapshot diagnostics.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3205, 'endIndex': 3233}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3234}, 'text': 'Idempotency testing.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3234, 'endIndex': 3254}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3255}, 'text': 'Code coverage.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3255, 'endIndex': 3269}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3270}, 'text': 'End-to-end testing on top 3000 npm packages.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3270, 'endIndex': 3314}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3315}, 'text': '2.5. Community and Contribution\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3315, 'endIndex': 3346}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3347}, 'text': 'Open source under the MIT License.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3347, 'endIndex': 3381}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3382}, 'text': 'Over 15k stars and 221 contributors.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3382, 'endIndex': 3418}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3419}, 'text': 'Encourages contributions via GitHub issues, Discord, and social media.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3419, 'endIndex': 3489}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3490}, 'text': 'Provides learning resources such as tutorials and articles on JavaScript parsing and compiler performance.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3490, 'endIndex': 3596}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3597}, 'text': '2.6. Sponsorship and Credits\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3597, 'endIndex': 3625}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3626}, 'text': 'Sponsored via GitHub Sponsors and Open Collective.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3626, 'endIndex': 3676}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3677}, 'text': 'Mentored and inspired by projects like Biome, Ruff, quick-lint-js, elm-review.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3677, 'endIndex': 3755}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3756}, 'text': 'Special thanks to key contributors for bootstrapping and design.\n'}}, {'createParagraphBullets': {'range': {'startIndex': 3756, 'endIndex': 3820}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 3821}, 'text': 'This summary encapsulates the main aspects of the oxc project, its tools, performance, usage, and community engagement as described on the GitHub repository[1].\n'}}]
2025-07-23 18:08:53,890 - INFO - Syncing note: Promt.md
2025-07-23 18:08:53,891 - INFO - Found embedded image: Untitled 9.png
2025-07-23 18:08:53,892 - INFO - Found embedded image: Untitled 1 4.png
2025-07-23 18:08:54,321 - INFO - Found existing document: Promt (ID: 1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0)
2025-07-23 18:08:54,322 - INFO - Document Promt already exists, updating content...
2025-07-23 18:08:55,564 - ERROR - Error clearing document content 1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_location': 'global', 'quota_limit_value': '60', 'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:55,565 - WARNING - Failed to update Promt, creating new document...
2025-07-23 18:08:55,885 - ERROR - Error processing Google Doc Promt: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'quota_unit': '1/min/{project}/{user}', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'quota_metric': 'docs.googleapis.com/write_requests', 'consumer': 'projects/561286222532', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:55,886 - INFO - Syncing note: Rust.md
2025-07-23 18:08:56,319 - INFO - Found existing document: Rust (ID: 1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU)
2025-07-23 18:08:56,319 - INFO - Document Rust already exists, updating content...
2025-07-23 18:08:57,629 - ERROR - Error clearing document content 1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:57,629 - WARNING - Failed to update Rust, creating new document...
2025-07-23 18:08:57,983 - ERROR - Error processing Google Doc Rust: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:57,984 - INFO - Syncing note: Du lịch.md
2025-07-23 18:08:58,395 - INFO - Found existing document: Du lịch (ID: 1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus)
2025-07-23 18:08:58,396 - INFO - Document Du lịch already exists, updating content...
2025-07-23 18:08:59,810 - ERROR - Error clearing document content 1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_unit': '1/min/{project}/{user}', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_location': 'global', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:08:59,810 - WARNING - Failed to update Du lịch, creating new document...
2025-07-23 18:09:00,138 - ERROR - Error processing Google Doc Du lịch: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_unit': '1/min/{project}/{user}', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_location': 'global', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:00,138 - INFO - Syncing note: VPS - Hosting.md
2025-07-23 18:09:00,612 - INFO - Found existing document: VPS - Hosting (ID: 1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc)
2025-07-23 18:09:00,612 - INFO - Document VPS - Hosting already exists, updating content...
2025-07-23 18:09:01,878 - ERROR - Error clearing document content 1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_unit': '1/min/{project}/{user}', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:01,878 - WARNING - Failed to update VPS - Hosting, creating new document...
2025-07-23 18:09:02,211 - ERROR - Error processing Google Doc VPS - Hosting: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:02,211 - INFO - Syncing note: Trải nghiệm.md
2025-07-23 18:09:02,653 - INFO - Found existing document: Trải nghiệm (ID: 1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w)
2025-07-23 18:09:02,653 - INFO - Document Trải nghiệm already exists, updating content...
2025-07-23 18:09:03,846 - ERROR - Error clearing document content 1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:03,847 - WARNING - Failed to update Trải nghiệm, creating new document...
2025-07-23 18:09:04,200 - ERROR - Error processing Google Doc Trải nghiệm: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60', 'service': 'docs.googleapis.com', 'quota_location': 'global'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:04,201 - INFO - Syncing note: MySQL.md
2025-07-23 18:09:04,692 - INFO - Found existing document: MySQL (ID: 1-EeL5eIfJQ9N8p3JKKjVMKoZ1H9OKPRn3x7yjLXzrEc)
2025-07-23 18:09:04,692 - INFO - Document MySQL already exists, updating content...
2025-07-23 18:09:05,967 - ERROR - Error clearing document content 1-EeL5eIfJQ9N8p3JKKjVMKoZ1H9OKPRn3x7yjLXzrEc: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1-EeL5eIfJQ9N8p3JKKjVMKoZ1H9OKPRn3x7yjLXzrEc:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:05,968 - WARNING - Failed to update MySQL, creating new document...
2025-07-23 18:09:07,457 - INFO - Created new Google Doc: MySQL (ID: 1Er6C_yV1msWrNCQ3WlL16MDUxsbnDBVZ24DH2-PqdpU)
2025-07-23 18:09:08,827 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:09:08,827 - INFO - Applying 42 formatting requests...
2025-07-23 18:09:08,832 - INFO - Validated 42/42 requests
2025-07-23 18:09:08,832 - INFO - Validated 42/42 requests
2025-07-23 18:09:09,152 - ERROR - Error applying formatting to MySQL: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1Er6C_yV1msWrNCQ3WlL16MDUxsbnDBVZ24DH2-PqdpU:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:09:09,153 - ERROR - Problematic requests: [{'insertText': {'location': {'index': 1}, 'text': 'relates:\n  - "[[Postgresql]]"\n  - "[[Database]]"\n'}}, {'insertText': {'location': {'index': 50}, 'text': '1. Resources\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 50, 'endIndex': 62}, 'paragraphStyle': {'namedStyleType': 'HEADING_1'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 63}, 'text': 'https://viblo.asia/p/su-dung-index-trong-database-nhu-the-nao-cho-hieu-qua-4P856q69lY3\n'}}, {'createParagraphBullets': {'range': {'startIndex': 63, 'endIndex': 149}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 150}, 'text': 'https://viblo.asia/p/deadlock-trong-sql-transaction-ung-dung-trong-laravel-WAyK89emZxX\n'}}, {'createParagraphBullets': {'range': {'startIndex': 150, 'endIndex': 236}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 237}, 'text': 'https://viblo.asia/p/window-functions-trong-mysql-nang-cao-va-cuc-ki-huu-dung-phan-ii-QpmleNGD5rd\n'}}, {'createParagraphBullets': {'range': {'startIndex': 237, 'endIndex': 334}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 335}, 'text': 'https://viblo.asia/p/window-functions-trong-mysql-nang-cao-va-cuc-ki-huu-dung-phan-i-Do754AgXKM6\n'}}, {'createParagraphBullets': {'range': {'startIndex': 335, 'endIndex': 431}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 432}, 'text': 'Backup data: https://viblo.asia/p/mit-dac-va-biet-tuot-noi-ve-cac-phuong-phap-backup-mysql-de-tang-toc-do-backup-va-restore-len-hang-tram-lan-PAoJeQ8DJ1j\n'}}, {'createParagraphBullets': {'range': {'startIndex': 432, 'endIndex': 585}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 586}, 'text': 'MySQL Thực Thi Lệnh SELECT Như Thế Nào?: https://viblo.asia/p/mysql-thuc-thi-lenh-select-nhu-the-nao-AZoJjreyJY7\n'}}, {'createParagraphBullets': {'range': {'startIndex': 586, 'endIndex': 698}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 699}, 'text': 'Nghiên cứu về kiến trúc và cách tối ưu MySQL: https://viblo.asia/p/nghien-cuu-ve-kien-truc-va-cach-toi-uu-mysql-EbNVQww0JvR\n'}}, {'createParagraphBullets': {'range': {'startIndex': 699, 'endIndex': 822}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 823}, 'text': '2. Tools\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 823, 'endIndex': 831}, 'paragraphStyle': {'namedStyleType': 'HEADING_1'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 832}, 'text': 'ProxySQL - Tăng tốc độ truy vấn dữ liệu SQL với replication\n'}}, {'createParagraphBullets': {'range': {'startIndex': 832, 'endIndex': 891}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 892}, 'text': '3. Storage engine\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 892, 'endIndex': 909}, 'paragraphStyle': {'namedStyleType': 'HEADING_1'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 910}, 'text': 'Trong MySQL, các storage engine cung cấp các cơ chế khác nhau để lưu trữ, quản lý và truy xuất dữ liệu. Dưới đây là so sánh của một số engine phổ biến:\n'}}, {'insertText': {'location': {'index': 1062}, 'text': '1. InnoDB\n'}}, {'insertText': {'location': {'index': 1072}, 'text': '• Đặc điểm: Đây là storage engine mặc định của MySQL kể từ phiên bản 5.5.\n• Hỗ trợ transaction: Có, hỗ trợ ACID (Atomicity, Consistency, Isolation, Durability).\n• Khóa: Sử dụng khóa theo hàng (row-level locking).\n• Chỉ mục: Hỗ trợ chỉ mục khóa ngoài (foreign key constraints).\n• Tính năng nổi bật: Khôi phục sau sự cố, hỗ trợ foreign key, tối ưu cho các tác vụ giao dịch.\n• Ưu điểm: Phù hợp cho các hệ thống có lượng lớn giao dịch, đòi hỏi tính toàn vẹn dữ liệu và phục hồi sau lỗi.\n'}}, {'insertText': {'location': {'index': 1555}, 'text': '2. MyISAM\n'}}, {'insertText': {'location': {'index': 1565}, 'text': '• Đặc điểm: Là engine mặc định trước khi InnoDB được sử dụng.\n• Hỗ trợ transaction: Không.\n• Khóa: Sử dụng khóa theo bảng (table-level locking).\n• Chỉ mục: Không hỗ trợ khóa ngoài, chỉ sử dụng chỉ mục tĩnh.\n• Tính năng nổi bật: Hiệu suất cao cho các tác vụ đọc (read-heavy workloads), nhưng thiếu tính năng phục hồi dữ liệu.\n• Ưu điểm: Tốc độ đọc rất nhanh, phù hợp cho các ứng dụng chủ yếu là đọc dữ liệu, không yêu cầu tính nhất quán giao dịch.\n'}}, {'insertText': {'location': {'index': 2012}, 'text': '3. MEMORY\n'}}, {'insertText': {'location': {'index': 2022}, 'text': '• Đặc điểm: Lưu trữ dữ liệu hoàn toàn trong bộ nhớ (RAM).\n• Hỗ trợ transaction: Không.\n• Khóa: Sử dụng khóa theo bảng.\n• Chỉ mục: Hỗ trợ chỉ mục theo bảng.\n• Tính năng nổi bật: Dữ liệu không được lưu trữ lâu dài, sẽ mất khi tắt máy chủ.\n• Ưu điểm: Tốc độ cực kỳ nhanh cho các thao tác đọc và ghi tạm thời. Phù hợp cho dữ liệu tạm thời, phiên làm việc (session), hoặc dữ liệu trung gian.\n'}}, {'insertText': {'location': {'index': 2409}, 'text': '4. CSV\n'}}, {'insertText': {'location': {'index': 2416}, 'text': '• Đặc điểm: Lưu trữ dữ liệu dưới dạng tệp CSV (Comma-Separated Values).\n• Hỗ trợ transaction: Không.\n• Khóa: Không hỗ trợ khóa.\n• Chỉ mục: Không hỗ trợ chỉ mục.\n• Tính năng nổi bật: Đơn giản, dễ dàng xuất/nhập dữ liệu từ các file CSV.\n• Ưu điểm: Phù hợp cho việc di chuyển hoặc trao đổi dữ liệu dưới định dạng tệp văn bản.\n'}}, {'insertText': {'location': {'index': 2739}, 'text': '5. ARCHIVE\n'}}, {'insertText': {'location': {'index': 2750}, 'text': '• Đặc điểm: Tối ưu cho lưu trữ dữ liệu lâu dài với dung lượng nhỏ.\n• Hỗ trợ transaction: Không.\n• Khóa: Không hỗ trợ khóa ghi (write locking), chỉ có thể ghi vào bảng (INSERT), không hỗ trợ UPDATE hoặc DELETE.\n• Chỉ mục: Không hỗ trợ chỉ mục.\n• Tính năng nổi bật: Dữ liệu được nén lại khi lưu trữ, tiết kiệm không gian.\n• Ưu điểm**: Thích hợp cho việc lưu trữ dữ liệu lớn mà ít thay đổi, chẳng hạn như lưu trữ log.\n'}}, {'insertText': {'location': {'index': 3165}, 'text': '6. BLACKHOLE\n'}}, {'insertText': {'location': {'index': 3178}, 'text': '• Đặc điểm: Không lưu trữ bất kỳ dữ liệu nào, mọi dữ liệu được ghi vào đều bị “nuốt”.\n• Hỗ trợ transaction: Không.\n• Khóa: Không có.\n• Chỉ mục: Không hỗ trợ chỉ mục.\n• Tính năng nổi bật: Dữ liệu gửi vào engine này sẽ bị mất, thường được sử dụng để nhân rộng (replication) mà không cần lưu trữ bản sao.\n• Ưu điểm: Thích hợp cho việc kiểm thử hoặc replication mà không cần lưu dữ liệu.\n'}}, {'insertText': {'location': {'index': 3562}, 'text': '7. MERGE\n'}}, {'insertText': {'location': {'index': 3571}, 'text': '• Đặc điểm: Là sự kết hợp của nhiều bảng MyISAM thành một bảng ảo.\n• Hỗ trợ transaction: Không.\n• Khóa: Khóa theo bảng (table-level locking).\n• Chỉ mục: Phụ thuộc vào chỉ mục của các bảng MyISAM được kết hợp.\n• Tính năng nổi bật: Cho phép kết hợp nhiều bảng có cấu trúc giống nhau thành một bảng ảo lớn.\n• Ưu điểm: Thích hợp cho các hệ thống mà dữ liệu được chia thành nhiều bảng khác nhau, nhưng cần truy vấn như một bảng.\n'}}, {'insertText': {'location': {'index': 3995}, 'text': '8. FEDERATED\n'}}, {'insertText': {'location': {'index': 4008}, 'text': '• Đặc điểm: Cho phép kết nối với các bảng ở các máy chủ MySQL khác nhau.\n• Hỗ trợ transaction: Không.\n• Khóa: Phụ thuộc vào máy chủ từ xa.\n• Chỉ mục: Phụ thuộc vào máy chủ từ xa.\n• Tính năng nổi bật: Dữ liệu không được lưu trữ cục bộ mà được truy vấn từ máy chủ từ xa.\n• Ưu điểm: Phù hợp cho việc liên kết dữ liệu giữa các cơ sở dữ liệu khác nhau mà không cần di chuyển dữ liệu.\n'}}, {'insertText': {'location': {'index': 4387}, 'text': 'Tổng kết\n'}}, {'insertText': {'location': {'index': 4396}, 'text': '• InnoDB: Tối ưu cho các hệ thống cần giao dịch và sự toàn vẹn dữ liệu.\n• MyISAM: Hiệu suất đọc cao, phù hợp với hệ thống chủ yếu đọc dữ liệu.\n• MEMORY: Lưu trữ tạm thời trên RAM, rất nhanh nhưng không bền vững.\n• CSV: Đơn giản và hữu ích cho nhập/xuất dữ liệu.\n• ARCHIVE: Lưu trữ dữ liệu nén, thích hợp cho dữ liệu lớn, ít thay đổi.\n• BLACKHOLE: Dùng để kiểm thử hoặc replication, không lưu trữ dữ liệu.\n• MERGE: Kết hợp nhiều bảng MyISAM thành một bảng ảo.\n• FEDERATED: Kết nối và truy vấn dữ liệu từ máy chủ MySQL từ xa.\n'}}]
2025-07-23 18:09:09,153 - INFO - Syncing note: Java Spring.md
2025-07-23 18:09:09,602 - INFO - Found existing document: Java Spring (ID: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg)
2025-07-23 18:09:09,602 - INFO - Document Java Spring already exists, updating content...
2025-07-23 18:09:10,949 - INFO - Cleared content from document: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg
2025-07-23 18:09:10,949 - INFO - Using existing Google Doc: Java Spring (ID: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg)
2025-07-23 18:09:10,950 - INFO - Applying 19 formatting requests...
2025-07-23 18:09:10,950 - INFO - Validated 19/19 requests
2025-07-23 18:09:10,950 - INFO - Validated 19/19 requests
2025-07-23 18:09:11,586 - INFO - Successfully applied formatting to document: Java Spring
2025-07-23 18:09:11,586 - INFO - Successfully processed Google Doc: Java Spring
2025-07-23 18:09:11,587 - INFO - Syncing note: Chiến lược backup dữ liệu 3-2-1.md
2025-07-23 18:09:12,027 - INFO - Found existing document: Chiến lược backup dữ liệu 3-2-1 (ID: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ)
2025-07-23 18:09:12,027 - INFO - Document Chiến lược backup dữ liệu 3-2-1 already exists, updating content...
2025-07-23 18:09:13,265 - INFO - Cleared content from document: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ
2025-07-23 18:09:13,265 - INFO - Using existing Google Doc: Chiến lược backup dữ liệu 3-2-1 (ID: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ)
2025-07-23 18:09:13,265 - INFO - Applying 36 formatting requests...
2025-07-23 18:09:13,275 - INFO - Validated 36/36 requests
2025-07-23 18:09:13,275 - INFO - Validated 36/36 requests
2025-07-23 18:09:13,826 - INFO - Successfully applied formatting to document: Chiến lược backup dữ liệu 3-2-1
2025-07-23 18:09:13,826 - INFO - Successfully processed Google Doc: Chiến lược backup dữ liệu 3-2-1
2025-07-23 18:09:13,826 - INFO - Syncing note: Những thứ đã học ở Grab tech talk.md
2025-07-23 18:09:14,314 - INFO - Found existing document: Những thứ đã học ở Grab tech talk (ID: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0)
2025-07-23 18:09:14,314 - INFO - Document Những thứ đã học ở Grab tech talk already exists, updating content...
2025-07-23 18:09:15,771 - INFO - Cleared content from document: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0
2025-07-23 18:09:15,772 - INFO - Using existing Google Doc: Những thứ đã học ở Grab tech talk (ID: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0)
2025-07-23 18:09:15,772 - INFO - Applying 44 formatting requests...
2025-07-23 18:09:15,774 - INFO - Validated 44/44 requests
2025-07-23 18:09:15,774 - INFO - Validated 44/44 requests
2025-07-23 18:09:16,376 - INFO - Successfully applied formatting to document: Những thứ đã học ở Grab tech talk
2025-07-23 18:09:16,376 - INFO - Successfully processed Google Doc: Những thứ đã học ở Grab tech talk
2025-07-23 18:09:16,376 - INFO - Syncing note: English with LLM - Cấu trúc câu phức tạp.md
2025-07-23 18:09:16,902 - INFO - Found existing document: English with LLM - Cấu trúc câu phức tạp (ID: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU)
2025-07-23 18:09:16,902 - INFO - Document English with LLM - Cấu trúc câu phức tạp already exists, updating content...
2025-07-23 18:09:18,284 - INFO - Cleared content from document: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU
2025-07-23 18:09:18,285 - INFO - Using existing Google Doc: English with LLM - Cấu trúc câu phức tạp (ID: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU)
2025-07-23 18:09:18,285 - INFO - Applying 55 formatting requests...
2025-07-23 18:09:18,288 - INFO - Validated 55/55 requests
2025-07-23 18:09:18,288 - INFO - Validated 55/55 requests
2025-07-23 18:09:19,030 - INFO - Successfully applied formatting to document: English with LLM - Cấu trúc câu phức tạp
2025-07-23 18:09:19,030 - INFO - Successfully processed Google Doc: English with LLM - Cấu trúc câu phức tạp
2025-07-23 18:09:19,030 - INFO - Syncing note: viclass - 752 - Make it easier to create account for user to experience the beta system.md
2025-07-23 18:09:19,469 - INFO - Found existing document: viclass - 752 - Make it easier to create account for user to experience the beta system (ID: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4)
2025-07-23 18:09:19,470 - INFO - Document viclass - 752 - Make it easier to create account for user to experience the beta system already exists, updating content...
2025-07-23 18:09:21,107 - INFO - Cleared content from document: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4
2025-07-23 18:09:21,107 - INFO - Using existing Google Doc: viclass - 752 - Make it easier to create account for user to experience the beta system (ID: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4)
2025-07-23 18:09:21,107 - INFO - Applying 147 formatting requests...
2025-07-23 18:09:21,110 - INFO - Validated 147/147 requests
2025-07-23 18:09:21,110 - INFO - Validated 147/147 requests
2025-07-23 18:09:21,977 - INFO - Successfully applied formatting to document: viclass - 752 - Make it easier to create account for user to experience the beta system
2025-07-23 18:09:21,977 - INFO - Successfully processed Google Doc: viclass - 752 - Make it easier to create account for user to experience the beta system
2025-07-23 18:09:21,977 - INFO - Syncing note: English with LLM - Loại câu.md
2025-07-23 18:09:22,478 - INFO - Found existing document: English with LLM - Loại câu (ID: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q)
2025-07-23 18:09:22,478 - INFO - Document English with LLM - Loại câu already exists, updating content...
2025-07-23 18:09:23,984 - INFO - Cleared content from document: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q
2025-07-23 18:09:23,984 - INFO - Using existing Google Doc: English with LLM - Loại câu (ID: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q)
2025-07-23 18:09:23,984 - INFO - Applying 122 formatting requests...
2025-07-23 18:09:23,988 - INFO - Validated 122/122 requests
2025-07-23 18:09:23,988 - INFO - Validated 122/122 requests
2025-07-23 18:09:24,828 - INFO - Successfully applied formatting to document: English with LLM - Loại câu
2025-07-23 18:09:24,828 - INFO - Successfully processed Google Doc: English with LLM - Loại câu
2025-07-23 18:09:24,829 - INFO - Syncing note: Các loại trang phục cho tủ đồ.md
2025-07-23 18:09:25,318 - INFO - Found existing document: Các loại trang phục cho tủ đồ (ID: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c)
2025-07-23 18:09:25,318 - INFO - Document Các loại trang phục cho tủ đồ already exists, updating content...
2025-07-23 18:09:26,601 - INFO - Cleared content from document: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c
2025-07-23 18:09:26,602 - INFO - Using existing Google Doc: Các loại trang phục cho tủ đồ (ID: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c)
2025-07-23 18:09:26,602 - INFO - Applying 8 formatting requests...
2025-07-23 18:09:26,602 - INFO - Validated 8/8 requests
2025-07-23 18:09:26,602 - INFO - Validated 8/8 requests
2025-07-23 18:09:27,451 - INFO - Successfully applied formatting to document: Các loại trang phục cho tủ đồ
2025-07-23 18:09:27,452 - INFO - Successfully processed Google Doc: Các loại trang phục cho tủ đồ
2025-07-23 18:09:27,452 - INFO - Syncing note: Lời khuyên.md
2025-07-23 18:09:27,910 - INFO - Found existing document: Lời khuyên (ID: 1OpqwVZmNi1sQiISjF37ByLVmM1_1mVy157DcGzaLbNw)
2025-07-23 18:09:27,910 - INFO - Document Lời khuyên already exists, updating content...
2025-07-23 18:09:29,367 - INFO - Cleared content from document: 1OpqwVZmNi1sQiISjF37ByLVmM1_1mVy157DcGzaLbNw
2025-07-23 18:09:29,368 - INFO - Using existing Google Doc: Lời khuyên (ID: 1OpqwVZmNi1sQiISjF37ByLVmM1_1mVy157DcGzaLbNw)
2025-07-23 18:09:29,368 - INFO - Applying 12 formatting requests...
2025-07-23 18:09:29,368 - INFO - Validated 12/12 requests
2025-07-23 18:09:29,368 - INFO - Validated 12/12 requests
2025-07-23 18:09:29,942 - INFO - Successfully applied formatting to document: Lời khuyên
2025-07-23 18:09:29,942 - INFO - Successfully processed Google Doc: Lời khuyên
2025-07-23 18:09:29,942 - INFO - Syncing note: Airblade.md
2025-07-23 18:09:30,332 - INFO - Found existing document: Airblade (ID: 1u7gLIvfny55zBy1jKGD07jbYCG3pFcGc_a7JFOksth0)
2025-07-23 18:09:30,332 - INFO - Document Airblade already exists, updating content...
2025-07-23 18:09:31,570 - INFO - Cleared content from document: 1u7gLIvfny55zBy1jKGD07jbYCG3pFcGc_a7JFOksth0
2025-07-23 18:09:31,570 - INFO - Using existing Google Doc: Airblade (ID: 1u7gLIvfny55zBy1jKGD07jbYCG3pFcGc_a7JFOksth0)
2025-07-23 18:09:31,570 - INFO - Applying 10 formatting requests...
2025-07-23 18:09:31,570 - INFO - Validated 10/10 requests
2025-07-23 18:09:31,570 - INFO - Validated 10/10 requests
2025-07-23 18:09:32,525 - INFO - Successfully applied formatting to document: Airblade
2025-07-23 18:09:32,526 - INFO - Successfully processed Google Doc: Airblade
2025-07-23 18:09:32,526 - INFO - Syncing note: Lộ trình thi cert AWS Solutions Architect Assoc.md
2025-07-23 18:09:33,018 - INFO - Found existing document: Lộ trình thi cert AWS Solutions Architect Assoc (ID: 1fhzCANB8-vA_9gCFK79upgoEp1nHNwhKxX-gkDVrd8I)
2025-07-23 18:09:33,019 - INFO - Document Lộ trình thi cert AWS Solutions Architect Assoc already exists, updating content...
2025-07-23 18:09:34,808 - INFO - Cleared content from document: 1fhzCANB8-vA_9gCFK79upgoEp1nHNwhKxX-gkDVrd8I
2025-07-23 18:09:34,808 - INFO - Using existing Google Doc: Lộ trình thi cert AWS Solutions Architect Assoc (ID: 1fhzCANB8-vA_9gCFK79upgoEp1nHNwhKxX-gkDVrd8I)
2025-07-23 18:09:34,808 - INFO - Applying 14 formatting requests...
2025-07-23 18:09:34,810 - INFO - Validated 14/14 requests
2025-07-23 18:09:34,811 - INFO - Validated 14/14 requests
2025-07-23 18:09:35,485 - INFO - Successfully applied formatting to document: Lộ trình thi cert AWS Solutions Architect Assoc
2025-07-23 18:09:35,485 - INFO - Successfully processed Google Doc: Lộ trình thi cert AWS Solutions Architect Assoc
2025-07-23 18:09:35,486 - INFO - Syncing note: Các câu hỏi phỏng vấn Laravel.md
2025-07-23 18:09:35,486 - INFO - Found embedded image: Untitled 7.png
2025-07-23 18:09:35,487 - INFO - Found embedded image: Untitled 1 2.png
2025-07-23 18:09:35,487 - INFO - Found embedded image: Untitled 2 1.png
2025-07-23 18:09:35,487 - INFO - Found embedded image: Untitled 3 1.png
2025-07-23 18:09:35,932 - INFO - Found existing document: Các câu hỏi phỏng vấn Laravel (ID: 17cK5rMolx2yUaEkkZ2hOkSMQZu3Dsv-ijjNIVaNXlZI)
2025-07-23 18:09:35,932 - INFO - Document Các câu hỏi phỏng vấn Laravel already exists, updating content...
2025-07-23 18:09:37,567 - INFO - Cleared content from document: 17cK5rMolx2yUaEkkZ2hOkSMQZu3Dsv-ijjNIVaNXlZI
2025-07-23 18:09:37,568 - INFO - Using existing Google Doc: Các câu hỏi phỏng vấn Laravel (ID: 17cK5rMolx2yUaEkkZ2hOkSMQZu3Dsv-ijjNIVaNXlZI)
2025-07-23 18:09:37,568 - INFO - Applying 89 formatting requests...
2025-07-23 18:09:37,601 - INFO - Validated 89/89 requests
2025-07-23 18:09:37,601 - INFO - Validated 89/89 requests
2025-07-23 18:09:38,299 - INFO - Successfully applied formatting to document: Các câu hỏi phỏng vấn Laravel
2025-07-23 18:09:38,300 - INFO - Successfully processed Google Doc: Các câu hỏi phỏng vấn Laravel
2025-07-23 18:09:38,334 - INFO - Processing image: Untitled 3 1.png at position 9352
2025-07-23 18:09:38,803 - INFO - Found existing file: Untitled 3 1.png (ID: 1TQa6PLwHoGPjZjPxeWXaPnlBEhFxCdAW)
2025-07-23 18:09:38,803 - INFO - File Untitled 3 1.png already exists, updating...
2025-07-23 18:09:42,924 - INFO - Updated existing file: Untitled 3 1.png (ID: 1TQa6PLwHoGPjZjPxeWXaPnlBEhFxCdAW)
2025-07-23 18:09:46,198 - INFO - Inserted image Untitled 3 1.png into document
2025-07-23 18:09:46,198 - INFO - Successfully inserted image: Untitled 3 1.png at index 9352
2025-07-23 18:09:46,657 - INFO - Processing image: Untitled 2 1.png at position 9330
2025-07-23 18:09:47,438 - INFO - Found existing file: Untitled 2 1.png (ID: 1sNldOJMx3JL6SFODa0heSORSNrJMj3aR)
2025-07-23 18:09:47,438 - INFO - File Untitled 2 1.png already exists, updating...
2025-07-23 18:09:52,041 - INFO - Updated existing file: Untitled 2 1.png (ID: 1sNldOJMx3JL6SFODa0heSORSNrJMj3aR)
2025-07-23 18:09:55,033 - INFO - Inserted image Untitled 2 1.png into document
2025-07-23 18:09:55,033 - INFO - Successfully inserted image: Untitled 2 1.png at index 9330
2025-07-23 18:09:55,589 - INFO - Processing image: Untitled 1 2.png at position 9308
2025-07-23 18:09:56,031 - INFO - Found existing file: Untitled 1 2.png (ID: 1UrcTl4PymzQQ7O9jP8OBAeBaHVepp8Gh)
2025-07-23 18:09:56,031 - INFO - File Untitled 1 2.png already exists, updating...
2025-07-23 18:10:00,180 - INFO - Updated existing file: Untitled 1 2.png (ID: 1UrcTl4PymzQQ7O9jP8OBAeBaHVepp8Gh)
2025-07-23 18:10:02,804 - INFO - Inserted image Untitled 1 2.png into document
2025-07-23 18:10:02,804 - INFO - Successfully inserted image: Untitled 1 2.png at index 9308
2025-07-23 18:10:03,237 - INFO - Processing image: Untitled 7.png at position 5009
2025-07-23 18:10:03,793 - INFO - Found existing file: Untitled 7.png (ID: 1sPIbD2MQP6RW9MJqxMPMJNVUZZpqdAuQ)
2025-07-23 18:10:03,793 - INFO - File Untitled 7.png already exists, updating...
2025-07-23 18:10:07,896 - INFO - Updated existing file: Untitled 7.png (ID: 1sPIbD2MQP6RW9MJqxMPMJNVUZZpqdAuQ)
2025-07-23 18:10:10,791 - INFO - Inserted image Untitled 7.png into document
2025-07-23 18:10:10,792 - INFO - Successfully inserted image: Untitled 7.png at index 5009
2025-07-23 18:10:11,262 - INFO - Syncing note: Các loại giày nên có.md
2025-07-23 18:10:11,263 - INFO - Found embedded image: Untitled 14.png
2025-07-23 18:10:11,263 - INFO - Found embedded image: Untitled 1 7.png
2025-07-23 18:10:11,263 - INFO - Found embedded image: Untitled 2 4.png
2025-07-23 18:10:11,690 - INFO - Found existing document: Các loại giày nên có (ID: 1dv4dI0i_fR-CUFvH6iwYwQtGeJvSQfRKt2aFQe93mho)
2025-07-23 18:10:11,690 - INFO - Document Các loại giày nên có already exists, updating content...
2025-07-23 18:10:12,990 - INFO - Cleared content from document: 1dv4dI0i_fR-CUFvH6iwYwQtGeJvSQfRKt2aFQe93mho
2025-07-23 18:10:12,990 - INFO - Using existing Google Doc: Các loại giày nên có (ID: 1dv4dI0i_fR-CUFvH6iwYwQtGeJvSQfRKt2aFQe93mho)
2025-07-23 18:10:12,990 - INFO - Applying 14 formatting requests...
2025-07-23 18:10:12,992 - INFO - Validated 14/14 requests
2025-07-23 18:10:12,992 - INFO - Validated 14/14 requests
2025-07-23 18:10:13,571 - INFO - Successfully applied formatting to document: Các loại giày nên có
2025-07-23 18:10:13,571 - INFO - Successfully processed Google Doc: Các loại giày nên có
2025-07-23 18:10:13,574 - INFO - Processing image: Untitled 2 4.png at position 675
2025-07-23 18:10:14,000 - INFO - Found existing file: Untitled 2 4.png (ID: 1jbiAlo1bhxrcsnKZ0mhuIO94gZGj2Nbf)
2025-07-23 18:10:14,000 - INFO - File Untitled 2 4.png already exists, updating...
2025-07-23 18:10:17,788 - INFO - Updated existing file: Untitled 2 4.png (ID: 1jbiAlo1bhxrcsnKZ0mhuIO94gZGj2Nbf)
2025-07-23 18:10:20,220 - INFO - Inserted image Untitled 2 4.png into document
2025-07-23 18:10:20,221 - INFO - Successfully inserted image: Untitled 2 4.png at index 675
2025-07-23 18:10:20,823 - INFO - Processing image: Untitled 1 7.png at position 324
2025-07-23 18:10:21,244 - INFO - Found existing file: Untitled 1 7.png (ID: 1yzM3M_NcusEarqGB2ORfjWDE5wRWIKmF)
2025-07-23 18:10:21,244 - INFO - File Untitled 1 7.png already exists, updating...
2025-07-23 18:10:26,432 - INFO - Updated existing file: Untitled 1 7.png (ID: 1yzM3M_NcusEarqGB2ORfjWDE5wRWIKmF)
2025-07-23 18:10:28,977 - INFO - Inserted image Untitled 1 7.png into document
2025-07-23 18:10:28,978 - INFO - Successfully inserted image: Untitled 1 7.png at index 324
2025-07-23 18:10:29,518 - INFO - Processing image: Untitled 14.png at position 92
2025-07-23 18:10:29,959 - INFO - Found existing file: Untitled 14.png (ID: 1y7eZzaAC12jicRv7tuooG63LX_t9srvk)
2025-07-23 18:10:29,959 - INFO - File Untitled 14.png already exists, updating...
2025-07-23 18:10:34,394 - INFO - Updated existing file: Untitled 14.png (ID: 1y7eZzaAC12jicRv7tuooG63LX_t9srvk)
2025-07-23 18:10:37,554 - INFO - Inserted image Untitled 14.png into document
2025-07-23 18:10:37,556 - INFO - Successfully inserted image: Untitled 14.png at index 92
2025-07-23 18:10:38,132 - INFO - Syncing note: Workspace.md
2025-07-23 18:10:38,558 - INFO - Found existing document: Workspace (ID: 1SMhJ7Ezply-zsNftFjOL7GlzxKb4uZljnOAXEpm12jg)
2025-07-23 18:10:38,558 - INFO - Document Workspace already exists, updating content...
2025-07-23 18:10:39,803 - INFO - Cleared content from document: 1SMhJ7Ezply-zsNftFjOL7GlzxKb4uZljnOAXEpm12jg
2025-07-23 18:10:39,804 - INFO - Using existing Google Doc: Workspace (ID: 1SMhJ7Ezply-zsNftFjOL7GlzxKb4uZljnOAXEpm12jg)
2025-07-23 18:10:39,804 - INFO - Applying 4 formatting requests...
2025-07-23 18:10:39,804 - INFO - Validated 4/4 requests
2025-07-23 18:10:39,804 - INFO - Validated 4/4 requests
2025-07-23 18:10:40,526 - INFO - Successfully applied formatting to document: Workspace
2025-07-23 18:10:40,526 - INFO - Successfully processed Google Doc: Workspace
2025-07-23 18:10:40,527 - INFO - Syncing note: Data Analyze.md
2025-07-23 18:10:40,972 - INFO - Found existing document: Data Analyze (ID: 1WLnzxFA1KFMFxK-ghHLVOnu6Llww8490AtAo0E2exNQ)
2025-07-23 18:10:40,972 - INFO - Document Data Analyze already exists, updating content...
2025-07-23 18:10:42,250 - INFO - Cleared content from document: 1WLnzxFA1KFMFxK-ghHLVOnu6Llww8490AtAo0E2exNQ
2025-07-23 18:10:42,250 - INFO - Using existing Google Doc: Data Analyze (ID: 1WLnzxFA1KFMFxK-ghHLVOnu6Llww8490AtAo0E2exNQ)
2025-07-23 18:10:42,250 - INFO - Applying 6 formatting requests...
2025-07-23 18:10:42,252 - INFO - Validated 6/6 requests
2025-07-23 18:10:42,252 - INFO - Validated 6/6 requests
2025-07-23 18:10:42,881 - INFO - Successfully applied formatting to document: Data Analyze
2025-07-23 18:10:42,881 - INFO - Successfully processed Google Doc: Data Analyze
2025-07-23 18:10:42,881 - INFO - Syncing note: Computer Science - Khoa học máy tính.md
2025-07-23 18:10:43,371 - INFO - Found existing document: Computer Science - Khoa học máy tính (ID: 1vM_j54GeRhnGnx3-LF2PHLMbrZ5yw6R_VzfjmTrh-Rg)
2025-07-23 18:10:43,372 - INFO - Document Computer Science - Khoa học máy tính already exists, updating content...
2025-07-23 18:10:44,674 - INFO - Cleared content from document: 1vM_j54GeRhnGnx3-LF2PHLMbrZ5yw6R_VzfjmTrh-Rg
2025-07-23 18:10:44,674 - INFO - Using existing Google Doc: Computer Science - Khoa học máy tính (ID: 1vM_j54GeRhnGnx3-LF2PHLMbrZ5yw6R_VzfjmTrh-Rg)
2025-07-23 18:10:44,674 - INFO - Applying 34 formatting requests...
2025-07-23 18:10:44,678 - INFO - Validated 34/34 requests
2025-07-23 18:10:44,678 - INFO - Validated 34/34 requests
2025-07-23 18:10:45,321 - INFO - Successfully applied formatting to document: Computer Science - Khoa học máy tính
2025-07-23 18:10:45,322 - INFO - Successfully processed Google Doc: Computer Science - Khoa học máy tính
2025-07-23 18:10:45,322 - INFO - Syncing note: English with LLM - Đảo ngữ.md
2025-07-23 18:10:45,764 - INFO - Found existing document: English with LLM - Đảo ngữ (ID: 1M69O2NJSSaS0jWR8i0wLUgtzKcG7PMUYPsFtB5XB4EY)
2025-07-23 18:10:45,764 - INFO - Document English with LLM - Đảo ngữ already exists, updating content...
2025-07-23 18:10:47,596 - INFO - Cleared content from document: 1M69O2NJSSaS0jWR8i0wLUgtzKcG7PMUYPsFtB5XB4EY
2025-07-23 18:10:47,596 - INFO - Using existing Google Doc: English with LLM - Đảo ngữ (ID: 1M69O2NJSSaS0jWR8i0wLUgtzKcG7PMUYPsFtB5XB4EY)
2025-07-23 18:10:47,596 - INFO - Applying 151 formatting requests...
2025-07-23 18:10:47,604 - INFO - Validated 151/151 requests
2025-07-23 18:10:47,604 - INFO - Validated 151/151 requests
2025-07-23 18:10:48,675 - INFO - Successfully applied formatting to document: English with LLM - Đảo ngữ
2025-07-23 18:10:48,675 - INFO - Successfully processed Google Doc: English with LLM - Đảo ngữ
2025-07-23 18:10:48,675 - INFO - Syncing note: PHP.md
2025-07-23 18:10:49,171 - INFO - Found existing document: PHP (ID: 1mmvzUVwcXpBS2pOUedFDVVOD8Wjwgmv3iJXVJV7I2l4)
2025-07-23 18:10:49,171 - INFO - Document PHP already exists, updating content...
2025-07-23 18:10:51,179 - INFO - Cleared content from document: 1mmvzUVwcXpBS2pOUedFDVVOD8Wjwgmv3iJXVJV7I2l4
2025-07-23 18:10:51,179 - INFO - Using existing Google Doc: PHP (ID: 1mmvzUVwcXpBS2pOUedFDVVOD8Wjwgmv3iJXVJV7I2l4)
2025-07-23 18:10:51,179 - INFO - Applying 545 formatting requests...
2025-07-23 18:10:51,217 - INFO - Validated 545/545 requests
2025-07-23 18:10:51,217 - INFO - Validated 545/545 requests
2025-07-23 18:10:52,819 - INFO - Successfully applied formatting to document: PHP
2025-07-23 18:10:52,819 - INFO - Successfully processed Google Doc: PHP
2025-07-23 18:10:52,819 - INFO - Syncing note: 6 Chiến lược Prompt Hiệu quả của OpenAI.md
2025-07-23 18:10:53,326 - INFO - Found existing document: 6 Chiến lược Prompt Hiệu quả của OpenAI (ID: 1gVXfKbvIG3rG1Uao-gitHfIMVJ-iQr6feDx-B6BsDvA)
2025-07-23 18:10:53,327 - INFO - Document 6 Chiến lược Prompt Hiệu quả của OpenAI already exists, updating content...
2025-07-23 18:10:55,488 - INFO - Cleared content from document: 1gVXfKbvIG3rG1Uao-gitHfIMVJ-iQr6feDx-B6BsDvA
2025-07-23 18:10:55,489 - INFO - Using existing Google Doc: 6 Chiến lược Prompt Hiệu quả của OpenAI (ID: 1gVXfKbvIG3rG1Uao-gitHfIMVJ-iQr6feDx-B6BsDvA)
2025-07-23 18:10:55,489 - INFO - Applying 519 formatting requests...
2025-07-23 18:10:55,544 - INFO - Validated 519/519 requests
2025-07-23 18:10:55,544 - INFO - Validated 519/519 requests
2025-07-23 18:10:57,491 - INFO - Successfully applied formatting to document: 6 Chiến lược Prompt Hiệu quả của OpenAI
2025-07-23 18:10:57,491 - INFO - Successfully processed Google Doc: 6 Chiến lược Prompt Hiệu quả của OpenAI
2025-07-23 18:10:57,491 - INFO - Syncing note: Diagrams - Vẽ sơ đồ.md
2025-07-23 18:10:57,492 - INFO - Found embedded image: Untitled 5.png
2025-07-23 18:10:57,942 - INFO - Found existing document: Diagrams - Vẽ sơ đồ (ID: 1FiO8V6fC-InpDwzJ-sh0QQqd0Pvq6UjQgd2A2BmcMAo)
2025-07-23 18:10:57,942 - INFO - Document Diagrams - Vẽ sơ đồ already exists, updating content...
2025-07-23 18:10:59,383 - INFO - Cleared content from document: 1FiO8V6fC-InpDwzJ-sh0QQqd0Pvq6UjQgd2A2BmcMAo
2025-07-23 18:10:59,384 - INFO - Using existing Google Doc: Diagrams - Vẽ sơ đồ (ID: 1FiO8V6fC-InpDwzJ-sh0QQqd0Pvq6UjQgd2A2BmcMAo)
2025-07-23 18:10:59,384 - INFO - Applying 13 formatting requests...
2025-07-23 18:10:59,384 - INFO - Validated 13/13 requests
2025-07-23 18:10:59,384 - INFO - Validated 13/13 requests
2025-07-23 18:11:00,020 - INFO - Successfully applied formatting to document: Diagrams - Vẽ sơ đồ
2025-07-23 18:11:00,020 - INFO - Successfully processed Google Doc: Diagrams - Vẽ sơ đồ
2025-07-23 18:11:00,020 - INFO - Processing image: Untitled 5.png at position 25
2025-07-23 18:11:00,449 - INFO - Found existing file: Untitled 5.png (ID: 1QGfdXc_9jGwiRH5cluWhTYYSAj2QVIgZ)
2025-07-23 18:11:00,449 - INFO - File Untitled 5.png already exists, updating...
2025-07-23 18:11:06,291 - INFO - Updated existing file: Untitled 5.png (ID: 1QGfdXc_9jGwiRH5cluWhTYYSAj2QVIgZ)
2025-07-23 18:11:09,223 - INFO - Inserted image Untitled 5.png into document
2025-07-23 18:11:09,224 - INFO - Successfully inserted image: Untitled 5.png at index 25
2025-07-23 18:11:09,764 - INFO - Syncing note: Tập thể dục.md
2025-07-23 18:11:09,766 - INFO - Found embedded image: 109120214_2828521967429081_3017506504666285487_n.jpg
2025-07-23 18:11:09,766 - INFO - Found embedded image: 109120824_2828521890762422_1242160168853690383_n.jpg
2025-07-23 18:11:09,766 - INFO - Found embedded image: 109564725_2828521907429087_2940439975379453911_n.jpg
2025-07-23 18:11:09,767 - INFO - Found embedded image: 109670649_2828521987429079_4321599374349967381_n.jpg
2025-07-23 18:11:09,767 - INFO - Found embedded image: 109696248_2828521930762418_277213041136568379_n.jpg
2025-07-23 18:11:09,767 - INFO - Found embedded image: 109898156_2828522014095743_8725420912218800228_n.jpg
2025-07-23 18:11:09,767 - INFO - Found embedded image: 112848464_2828521950762416_6675007294543947402_n.jpg
2025-07-23 18:11:10,230 - INFO - Found existing document: Tập thể dục (ID: 169EGjO_P7JZv9dMpcaQi1GO-TDPXPWclpiN5vCUPG1Q)
2025-07-23 18:11:10,230 - INFO - Document Tập thể dục already exists, updating content...
2025-07-23 18:11:12,137 - INFO - Cleared content from document: 169EGjO_P7JZv9dMpcaQi1GO-TDPXPWclpiN5vCUPG1Q
2025-07-23 18:11:12,138 - INFO - Using existing Google Doc: Tập thể dục (ID: 169EGjO_P7JZv9dMpcaQi1GO-TDPXPWclpiN5vCUPG1Q)
2025-07-23 18:11:12,138 - INFO - Applying 7 formatting requests...
2025-07-23 18:11:12,138 - INFO - Validated 7/7 requests
2025-07-23 18:11:12,138 - INFO - Validated 7/7 requests
2025-07-23 18:11:12,787 - INFO - Successfully applied formatting to document: Tập thể dục
2025-07-23 18:11:12,787 - INFO - Successfully processed Google Doc: Tập thể dục
2025-07-23 18:11:12,788 - INFO - Processing image: 112848464_2828521950762416_6675007294543947402_n.jpg at position 133
2025-07-23 18:11:13,192 - INFO - Found existing file: 112848464_2828521950762416_6675007294543947402_n.jpg (ID: 1YvliH2WTIGH-9egWB7Tn2ESF84ckXLLS)
2025-07-23 18:11:13,192 - INFO - File 112848464_2828521950762416_6675007294543947402_n.jpg already exists, updating...
2025-07-23 18:11:17,630 - INFO - Updated existing file: 112848464_2828521950762416_6675007294543947402_n.jpg (ID: 1YvliH2WTIGH-9egWB7Tn2ESF84ckXLLS)
2025-07-23 18:11:20,252 - INFO - Inserted image 112848464_2828521950762416_6675007294543947402_n.jpg into document
2025-07-23 18:11:20,253 - INFO - Successfully inserted image: 112848464_2828521950762416_6675007294543947402_n.jpg at index 133
2025-07-23 18:11:20,834 - INFO - Processing image: 109898156_2828522014095743_8725420912218800228_n.jpg at position 111
2025-07-23 18:11:21,305 - INFO - Found existing file: 109898156_2828522014095743_8725420912218800228_n.jpg (ID: 1vUcSsNKVNwP9T1vH9PWt-QloUqDYtygM)
2025-07-23 18:11:21,305 - INFO - File 109898156_2828522014095743_8725420912218800228_n.jpg already exists, updating...
2025-07-23 18:11:26,658 - INFO - Updated existing file: 109898156_2828522014095743_8725420912218800228_n.jpg (ID: 1vUcSsNKVNwP9T1vH9PWt-QloUqDYtygM)
2025-07-23 18:11:29,106 - INFO - Inserted image 109898156_2828522014095743_8725420912218800228_n.jpg into document
2025-07-23 18:11:29,106 - INFO - Successfully inserted image: 109898156_2828522014095743_8725420912218800228_n.jpg at index 111
2025-07-23 18:11:29,564 - INFO - Processing image: 109696248_2828521930762418_277213041136568379_n.jpg at position 89
2025-07-23 18:11:29,967 - INFO - Found existing file: 109696248_2828521930762418_277213041136568379_n.jpg (ID: 1qSay1VTSWxJJoKvCldvINKpDFs49fcRQ)
2025-07-23 18:11:29,967 - INFO - File 109696248_2828521930762418_277213041136568379_n.jpg already exists, updating...
2025-07-23 18:11:34,621 - INFO - Updated existing file: 109696248_2828521930762418_277213041136568379_n.jpg (ID: 1qSay1VTSWxJJoKvCldvINKpDFs49fcRQ)
2025-07-23 18:11:36,767 - INFO - Inserted image 109696248_2828521930762418_277213041136568379_n.jpg into document
2025-07-23 18:11:36,767 - INFO - Successfully inserted image: 109696248_2828521930762418_277213041136568379_n.jpg at index 89
2025-07-23 18:11:37,172 - INFO - Processing image: 109670649_2828521987429079_4321599374349967381_n.jpg at position 67
2025-07-23 18:11:37,574 - INFO - Found existing file: 109670649_2828521987429079_4321599374349967381_n.jpg (ID: 1qH7ETZqvKk0yapNnwb9BN1woPKug0HXs)
2025-07-23 18:11:37,574 - INFO - File 109670649_2828521987429079_4321599374349967381_n.jpg already exists, updating...
2025-07-23 18:11:41,878 - INFO - Updated existing file: 109670649_2828521987429079_4321599374349967381_n.jpg (ID: 1qH7ETZqvKk0yapNnwb9BN1woPKug0HXs)
2025-07-23 18:11:44,606 - INFO - Inserted image 109670649_2828521987429079_4321599374349967381_n.jpg into document
2025-07-23 18:11:44,606 - INFO - Successfully inserted image: 109670649_2828521987429079_4321599374349967381_n.jpg at index 67
2025-07-23 18:11:45,009 - INFO - Processing image: 109564725_2828521907429087_2940439975379453911_n.jpg at position 45
2025-07-23 18:11:45,412 - INFO - Found existing file: 109564725_2828521907429087_2940439975379453911_n.jpg (ID: 1V4VPF7qUp6T4UNENIhgVFVqnSZ_MjaRF)
2025-07-23 18:11:45,413 - INFO - File 109564725_2828521907429087_2940439975379453911_n.jpg already exists, updating...
2025-07-23 18:11:50,984 - INFO - Updated existing file: 109564725_2828521907429087_2940439975379453911_n.jpg (ID: 1V4VPF7qUp6T4UNENIhgVFVqnSZ_MjaRF)
2025-07-23 18:11:53,967 - INFO - Inserted image 109564725_2828521907429087_2940439975379453911_n.jpg into document
2025-07-23 18:11:53,967 - INFO - Successfully inserted image: 109564725_2828521907429087_2940439975379453911_n.jpg at index 45
2025-07-23 18:11:54,308 - INFO - Processing image: 109120824_2828521890762422_1242160168853690383_n.jpg at position 23
2025-07-23 18:11:54,839 - INFO - Found existing file: 109120824_2828521890762422_1242160168853690383_n.jpg (ID: 1Xxw1-agWVsfmMgyrT50ebalBs6ss6zZz)
2025-07-23 18:11:54,839 - INFO - File 109120824_2828521890762422_1242160168853690383_n.jpg already exists, updating...
2025-07-23 18:11:59,258 - INFO - Updated existing file: 109120824_2828521890762422_1242160168853690383_n.jpg (ID: 1Xxw1-agWVsfmMgyrT50ebalBs6ss6zZz)
2025-07-23 18:12:01,800 - INFO - Inserted image 109120824_2828521890762422_1242160168853690383_n.jpg into document
2025-07-23 18:12:01,800 - INFO - Successfully inserted image: 109120824_2828521890762422_1242160168853690383_n.jpg at index 23
2025-07-23 18:12:02,131 - INFO - Processing image: 109120214_2828521967429081_3017506504666285487_n.jpg at position 1
2025-07-23 18:12:02,519 - INFO - Found existing file: 109120214_2828521967429081_3017506504666285487_n.jpg (ID: 1EhkjRTz8ykQdxsr6xmE_2dKDqI3Cn6UF)
2025-07-23 18:12:02,519 - INFO - File 109120214_2828521967429081_3017506504666285487_n.jpg already exists, updating...
2025-07-23 18:12:06,718 - INFO - Updated existing file: 109120214_2828521967429081_3017506504666285487_n.jpg (ID: 1EhkjRTz8ykQdxsr6xmE_2dKDqI3Cn6UF)
2025-07-23 18:12:09,208 - INFO - Inserted image 109120214_2828521967429081_3017506504666285487_n.jpg into document
2025-07-23 18:12:09,209 - INFO - Successfully inserted image: 109120214_2828521967429081_3017506504666285487_n.jpg at index 1
2025-07-23 18:12:09,541 - INFO - Syncing note: Kubernetes - K8S.md
2025-07-23 18:12:10,119 - INFO - Found existing document: Kubernetes - K8S (ID: 147Mq5UmA6RYioZHwEups6KmPviy63IXJ6XDbCgaQCkE)
2025-07-23 18:12:10,120 - INFO - Document Kubernetes - K8S already exists, updating content...
2025-07-23 18:12:11,461 - INFO - Cleared content from document: 147Mq5UmA6RYioZHwEups6KmPviy63IXJ6XDbCgaQCkE
2025-07-23 18:12:11,461 - INFO - Using existing Google Doc: Kubernetes - K8S (ID: 147Mq5UmA6RYioZHwEups6KmPviy63IXJ6XDbCgaQCkE)
2025-07-23 18:12:11,461 - INFO - Applying 15 formatting requests...
2025-07-23 18:12:11,462 - INFO - Validated 15/15 requests
2025-07-23 18:12:11,462 - INFO - Validated 15/15 requests
2025-07-23 18:12:12,106 - INFO - Successfully applied formatting to document: Kubernetes - K8S
2025-07-23 18:12:12,106 - INFO - Successfully processed Google Doc: Kubernetes - K8S
2025-07-23 18:12:12,106 - INFO - Syncing note: Kho tài liệu sau 3 năm học IELTS của t - phần 1.md
2025-07-23 18:12:12,561 - INFO - Found existing document: Kho tài liệu sau 3 năm học IELTS của t - phần 1 (ID: 150xYMygGmwp2qRL17r9UTB0wzdQr6kdhaWCg-CIAsTo)
2025-07-23 18:12:12,561 - INFO - Document Kho tài liệu sau 3 năm học IELTS của t - phần 1 already exists, updating content...
2025-07-23 18:12:14,464 - INFO - Cleared content from document: 150xYMygGmwp2qRL17r9UTB0wzdQr6kdhaWCg-CIAsTo
2025-07-23 18:12:14,464 - INFO - Using existing Google Doc: Kho tài liệu sau 3 năm học IELTS của t - phần 1 (ID: 150xYMygGmwp2qRL17r9UTB0wzdQr6kdhaWCg-CIAsTo)
2025-07-23 18:12:14,464 - INFO - Applying 96 formatting requests...
2025-07-23 18:12:14,481 - INFO - Validated 96/96 requests
2025-07-23 18:12:14,482 - INFO - Validated 96/96 requests
2025-07-23 18:12:15,413 - INFO - Successfully applied formatting to document: Kho tài liệu sau 3 năm học IELTS của t - phần 1
2025-07-23 18:12:15,413 - INFO - Successfully processed Google Doc: Kho tài liệu sau 3 năm học IELTS của t - phần 1
2025-07-23 18:12:15,414 - INFO - Syncing note: Cấu trúc dữ liệu & giải thuật.md
2025-07-23 18:12:15,904 - INFO - Found existing document: Cấu trúc dữ liệu & giải thuật (ID: 1h_zZ86WV-K__051oOS_44aZ2GTDEbp7boJEL5Huvu2Y)
2025-07-23 18:12:15,904 - INFO - Document Cấu trúc dữ liệu & giải thuật already exists, updating content...
2025-07-23 18:12:17,372 - INFO - Cleared content from document: 1h_zZ86WV-K__051oOS_44aZ2GTDEbp7boJEL5Huvu2Y
2025-07-23 18:12:17,372 - INFO - Using existing Google Doc: Cấu trúc dữ liệu & giải thuật (ID: 1h_zZ86WV-K__051oOS_44aZ2GTDEbp7boJEL5Huvu2Y)
2025-07-23 18:12:17,372 - INFO - Applying 113 formatting requests...
2025-07-23 18:12:17,393 - INFO - Validated 113/113 requests
2025-07-23 18:12:17,394 - INFO - Validated 113/113 requests
2025-07-23 18:12:18,278 - INFO - Successfully applied formatting to document: Cấu trúc dữ liệu & giải thuật
2025-07-23 18:12:18,278 - INFO - Successfully processed Google Doc: Cấu trúc dữ liệu & giải thuật
2025-07-23 18:12:18,278 - INFO - Syncing note: Interview Senior Engineer.md
2025-07-23 18:12:18,739 - INFO - Found existing document: Interview Senior Engineer (ID: 1Lv1NA9RPUsNfFUcp5Yq6NrkgVNmaps_WO6xvdsaM9pg)
2025-07-23 18:12:18,740 - INFO - Document Interview Senior Engineer already exists, updating content...
2025-07-23 18:12:20,450 - INFO - Cleared content from document: 1Lv1NA9RPUsNfFUcp5Yq6NrkgVNmaps_WO6xvdsaM9pg
2025-07-23 18:12:20,451 - INFO - Using existing Google Doc: Interview Senior Engineer (ID: 1Lv1NA9RPUsNfFUcp5Yq6NrkgVNmaps_WO6xvdsaM9pg)
2025-07-23 18:12:20,451 - INFO - Applying 140 formatting requests...
2025-07-23 18:12:20,461 - INFO - Validated 140/140 requests
2025-07-23 18:12:20,461 - INFO - Validated 140/140 requests
2025-07-23 18:12:21,393 - INFO - Successfully applied formatting to document: Interview Senior Engineer
2025-07-23 18:12:21,393 - INFO - Successfully processed Google Doc: Interview Senior Engineer
2025-07-23 18:12:21,393 - INFO - Syncing note: Inceptionlabs.md
2025-07-23 18:12:21,810 - INFO - Found existing document: Inceptionlabs (ID: 1tbAC0dEF-00NxpWyYXlVjHmfObxg4Og5cZ12rVZAsBc)
2025-07-23 18:12:21,810 - INFO - Document Inceptionlabs already exists, updating content...
2025-07-23 18:12:23,258 - INFO - Cleared content from document: 1tbAC0dEF-00NxpWyYXlVjHmfObxg4Og5cZ12rVZAsBc
2025-07-23 18:12:23,258 - INFO - Using existing Google Doc: Inceptionlabs (ID: 1tbAC0dEF-00NxpWyYXlVjHmfObxg4Og5cZ12rVZAsBc)
2025-07-23 18:12:23,258 - INFO - Applying 87 formatting requests...
2025-07-23 18:12:23,261 - INFO - Validated 87/87 requests
2025-07-23 18:12:23,261 - INFO - Validated 87/87 requests
2025-07-23 18:12:23,972 - INFO - Successfully applied formatting to document: Inceptionlabs
2025-07-23 18:12:23,972 - INFO - Successfully processed Google Doc: Inceptionlabs
2025-07-23 18:12:23,972 - INFO - Syncing note: Linux.md
2025-07-23 18:12:23,973 - INFO - Found embedded image: Untitled 1 1.png
2025-07-23 18:12:24,538 - INFO - Found existing document: Linux (ID: 1k9vf8-_sXJdmZ0ETDdqfF8QBRbuKlCqcm_Q8FCS3onQ)
2025-07-23 18:12:24,538 - INFO - Document Linux already exists, updating content...
2025-07-23 18:12:25,799 - INFO - Cleared content from document: 1k9vf8-_sXJdmZ0ETDdqfF8QBRbuKlCqcm_Q8FCS3onQ
2025-07-23 18:12:25,799 - INFO - Using existing Google Doc: Linux (ID: 1k9vf8-_sXJdmZ0ETDdqfF8QBRbuKlCqcm_Q8FCS3onQ)
2025-07-23 18:12:25,799 - INFO - Applying 5 formatting requests...
2025-07-23 18:12:25,800 - INFO - Validated 5/5 requests
2025-07-23 18:12:25,800 - INFO - Validated 5/5 requests
2025-07-23 18:12:26,325 - INFO - Successfully applied formatting to document: Linux
2025-07-23 18:12:26,325 - INFO - Successfully processed Google Doc: Linux
2025-07-23 18:12:26,325 - INFO - Processing image: Untitled 1 1.png at position 14
2025-07-23 18:12:26,771 - INFO - Found existing file: Untitled 1 1.png (ID: 1oG_H4CkZe2YGxUk715-3v3MdCO1Ba3Yw)
2025-07-23 18:12:26,771 - INFO - File Untitled 1 1.png already exists, updating...
2025-07-23 18:12:31,577 - INFO - Updated existing file: Untitled 1 1.png (ID: 1oG_H4CkZe2YGxUk715-3v3MdCO1Ba3Yw)
2025-07-23 18:12:34,163 - INFO - Inserted image Untitled 1 1.png into document
2025-07-23 18:12:34,164 - INFO - Successfully inserted image: Untitled 1 1.png at index 14
2025-07-23 18:12:34,700 - INFO - Syncing note: Testing.md
2025-07-23 18:12:34,703 - INFO - Found embedded image: Untitled 12.png
2025-07-23 18:12:35,223 - INFO - Found existing document: Testing (ID: 1VqpW9-TCzlMWvUgeT-vfdM7ZSnPap72FSG7YXKYpSOs)
2025-07-23 18:12:35,224 - INFO - Document Testing already exists, updating content...
2025-07-23 18:12:37,241 - INFO - Cleared content from document: 1VqpW9-TCzlMWvUgeT-vfdM7ZSnPap72FSG7YXKYpSOs
2025-07-23 18:12:37,242 - INFO - Using existing Google Doc: Testing (ID: 1VqpW9-TCzlMWvUgeT-vfdM7ZSnPap72FSG7YXKYpSOs)
2025-07-23 18:12:37,242 - INFO - Applying 780 formatting requests...
2025-07-23 18:12:37,284 - INFO - Validated 780/780 requests
2025-07-23 18:12:37,284 - INFO - Validated 780/780 requests
2025-07-23 18:12:39,546 - INFO - Successfully applied formatting to document: Testing
2025-07-23 18:12:39,546 - INFO - Successfully processed Google Doc: Testing
2025-07-23 18:12:39,547 - INFO - Processing image: Untitled 12.png at position 841
2025-07-23 18:12:39,986 - INFO - Found existing file: Untitled 12.png (ID: 1LM7XLnIRP2b2z4zlnLeQRvJ2udGZso4X)
2025-07-23 18:12:39,986 - INFO - File Untitled 12.png already exists, updating...
2025-07-23 18:12:44,678 - INFO - Updated existing file: Untitled 12.png (ID: 1LM7XLnIRP2b2z4zlnLeQRvJ2udGZso4X)
2025-07-23 18:12:47,429 - INFO - Inserted image Untitled 12.png into document
2025-07-23 18:12:47,430 - INFO - Successfully inserted image: Untitled 12.png at index 841
2025-07-23 18:12:48,091 - INFO - Syncing note: Error handler.md
2025-07-23 18:12:48,535 - INFO - Found existing document: Error handler (ID: 1_MnERNZK3O52turL_vs6ukft78ztTQ8qJDgYwbgpbUA)
2025-07-23 18:12:48,535 - INFO - Document Error handler already exists, updating content...
2025-07-23 18:12:49,981 - INFO - Cleared content from document: 1_MnERNZK3O52turL_vs6ukft78ztTQ8qJDgYwbgpbUA
2025-07-23 18:12:49,981 - INFO - Using existing Google Doc: Error handler (ID: 1_MnERNZK3O52turL_vs6ukft78ztTQ8qJDgYwbgpbUA)
2025-07-23 18:12:49,981 - INFO - Applying 1 formatting requests...
2025-07-23 18:12:49,981 - INFO - Validated 1/1 requests
2025-07-23 18:12:49,981 - INFO - Validated 1/1 requests
2025-07-23 18:12:50,699 - INFO - Successfully applied formatting to document: Error handler
2025-07-23 18:12:50,699 - INFO - Successfully processed Google Doc: Error handler
2025-07-23 18:12:50,699 - INFO - Syncing note: English with LLM - Số ít số nhiều.md
2025-07-23 18:12:51,153 - INFO - Found existing document: English with LLM - Số ít số nhiều (ID: 1zZ2XwtxN-a3C9hyuxoaDjmSDRTIoOTslSNec6x2fbGk)
2025-07-23 18:12:51,153 - INFO - Document English with LLM - Số ít số nhiều already exists, updating content...
2025-07-23 18:12:52,550 - INFO - Cleared content from document: 1zZ2XwtxN-a3C9hyuxoaDjmSDRTIoOTslSNec6x2fbGk
2025-07-23 18:12:52,550 - INFO - Using existing Google Doc: English with LLM - Số ít số nhiều (ID: 1zZ2XwtxN-a3C9hyuxoaDjmSDRTIoOTslSNec6x2fbGk)
2025-07-23 18:12:52,550 - INFO - Applying 92 formatting requests...
2025-07-23 18:12:52,556 - INFO - Validated 92/92 requests
2025-07-23 18:12:52,556 - INFO - Validated 92/92 requests
2025-07-23 18:12:53,209 - INFO - Successfully applied formatting to document: English with LLM - Số ít số nhiều
2025-07-23 18:12:53,209 - INFO - Successfully processed Google Doc: English with LLM - Số ít số nhiều
2025-07-23 18:12:53,209 - INFO - Syncing note: Laravel.md
2025-07-23 18:12:53,211 - INFO - Found embedded image: Pasted image 20241008054441.png
2025-07-23 18:12:53,211 - INFO - Found embedded image: Pasted image 20241008054447.png
2025-07-23 18:12:53,211 - INFO - Found embedded image: Pasted image 20241008054452.png
2025-07-23 18:12:53,211 - INFO - Found embedded image: Pasted image 20241008054458.png
2025-07-23 18:12:53,212 - INFO - Found embedded image: Pasted image 20241008054704.png
2025-07-23 18:12:53,689 - INFO - Found existing document: Laravel (ID: 1t3knHrtmpOu8SBeHNnTJgjuXrhUgU01O8lTcDHCzoHs)
2025-07-23 18:12:53,689 - INFO - Document Laravel already exists, updating content...
2025-07-23 18:12:55,643 - INFO - Cleared content from document: 1t3knHrtmpOu8SBeHNnTJgjuXrhUgU01O8lTcDHCzoHs
2025-07-23 18:12:55,643 - INFO - Using existing Google Doc: Laravel (ID: 1t3knHrtmpOu8SBeHNnTJgjuXrhUgU01O8lTcDHCzoHs)
2025-07-23 18:12:55,643 - INFO - Applying 607 formatting requests...
2025-07-23 18:12:55,656 - INFO - Validated 607/607 requests
2025-07-23 18:12:55,656 - INFO - Validated 607/607 requests
2025-07-23 18:12:57,745 - INFO - Successfully applied formatting to document: Laravel
2025-07-23 18:12:57,746 - INFO - Successfully processed Google Doc: Laravel
2025-07-23 18:12:57,813 - INFO - Processing image: Pasted image 20241008054704.png at position 22644
2025-07-23 18:12:58,344 - INFO - Found existing file: Pasted image 20241008054704.png (ID: 181jX2Jk0ohztkUcdmZZBFlM4GQl4QR9c)
2025-07-23 18:12:58,344 - INFO - File Pasted image 20241008054704.png already exists, updating...
2025-07-23 18:13:02,700 - INFO - Updated existing file: Pasted image 20241008054704.png (ID: 181jX2Jk0ohztkUcdmZZBFlM4GQl4QR9c)
2025-07-23 18:13:05,956 - INFO - Inserted image Pasted image 20241008054704.png into document
2025-07-23 18:13:05,957 - INFO - Successfully inserted image: Pasted image 20241008054704.png at index 22644
2025-07-23 18:13:06,435 - INFO - Processing image: Pasted image 20241008054458.png at position 22270
2025-07-23 18:13:06,874 - INFO - Found existing file: Pasted image 20241008054458.png (ID: 1AY3S1vaKQjpHBO41rs2MvQvCplUqs_J_)
2025-07-23 18:13:06,875 - INFO - File Pasted image 20241008054458.png already exists, updating...
2025-07-23 18:13:11,593 - INFO - Updated existing file: Pasted image 20241008054458.png (ID: 1AY3S1vaKQjpHBO41rs2MvQvCplUqs_J_)
2025-07-23 18:13:14,192 - INFO - Inserted image Pasted image 20241008054458.png into document
2025-07-23 18:13:14,193 - INFO - Successfully inserted image: Pasted image 20241008054458.png at index 22270
2025-07-23 18:13:14,658 - INFO - Processing image: Pasted image 20241008054452.png at position 22025
2025-07-23 18:13:15,114 - INFO - Found existing file: Pasted image 20241008054452.png (ID: 1Cj1lHMjpDFwUqNe-bd9o-rOtRuFxbW_h)
2025-07-23 18:13:15,114 - INFO - File Pasted image 20241008054452.png already exists, updating...
2025-07-23 18:13:21,776 - INFO - Updated existing file: Pasted image 20241008054452.png (ID: 1Cj1lHMjpDFwUqNe-bd9o-rOtRuFxbW_h)
2025-07-23 18:13:24,429 - INFO - Inserted image Pasted image 20241008054452.png into document
2025-07-23 18:13:24,429 - INFO - Successfully inserted image: Pasted image 20241008054452.png at index 22025
2025-07-23 18:13:24,896 - INFO - Processing image: Pasted image 20241008054447.png at position 21856
2025-07-23 18:13:25,319 - INFO - Found existing file: Pasted image 20241008054447.png (ID: 1FDoSG47EzhMeJsFCptb_hXalEz15VeKc)
2025-07-23 18:13:25,319 - INFO - File Pasted image 20241008054447.png already exists, updating...
2025-07-23 18:13:30,408 - INFO - Updated existing file: Pasted image 20241008054447.png (ID: 1FDoSG47EzhMeJsFCptb_hXalEz15VeKc)
2025-07-23 18:13:33,203 - INFO - Inserted image Pasted image 20241008054447.png into document
2025-07-23 18:13:33,203 - INFO - Successfully inserted image: Pasted image 20241008054447.png at index 21856
2025-07-23 18:13:33,680 - INFO - Processing image: Pasted image 20241008054441.png at position 21659
2025-07-23 18:13:34,112 - INFO - Found existing file: Pasted image 20241008054441.png (ID: 1uNUrSqeFhu2qvOZN70VDNpxspFas_n4H)
2025-07-23 18:13:34,112 - INFO - File Pasted image 20241008054441.png already exists, updating...
2025-07-23 18:13:39,288 - INFO - Updated existing file: Pasted image 20241008054441.png (ID: 1uNUrSqeFhu2qvOZN70VDNpxspFas_n4H)
2025-07-23 18:13:42,485 - INFO - Inserted image Pasted image 20241008054441.png into document
2025-07-23 18:13:42,486 - INFO - Successfully inserted image: Pasted image 20241008054441.png at index 21659
2025-07-23 18:13:43,119 - INFO - Syncing note: Tự vệ.md
2025-07-23 18:13:43,570 - INFO - Found existing document: Tự vệ (ID: 1-8w9CuHFLqCcNhrCk3cJcUpO8JUml1bItSBASWDxiY8)
2025-07-23 18:13:43,570 - INFO - Document Tự vệ already exists, updating content...
2025-07-23 18:13:44,937 - INFO - Cleared content from document: 1-8w9CuHFLqCcNhrCk3cJcUpO8JUml1bItSBASWDxiY8
2025-07-23 18:13:44,937 - INFO - Using existing Google Doc: Tự vệ (ID: 1-8w9CuHFLqCcNhrCk3cJcUpO8JUml1bItSBASWDxiY8)
2025-07-23 18:13:44,937 - INFO - Applying 4 formatting requests...
2025-07-23 18:13:44,937 - INFO - Validated 4/4 requests
2025-07-23 18:13:44,937 - INFO - Validated 4/4 requests
2025-07-23 18:13:45,547 - INFO - Successfully applied formatting to document: Tự vệ
2025-07-23 18:13:45,548 - INFO - Successfully processed Google Doc: Tự vệ
2025-07-23 18:13:45,548 - INFO - Syncing note: Vietop.md
2025-07-23 18:13:45,964 - INFO - Found existing document: Vietop (ID: 1FdFJhNjnA7BQUfepd11WDxGSKWFNnKQaU6ct9tkMKMA)
2025-07-23 18:13:45,964 - INFO - Document Vietop already exists, updating content...
2025-07-23 18:13:47,328 - INFO - Cleared content from document: 1FdFJhNjnA7BQUfepd11WDxGSKWFNnKQaU6ct9tkMKMA
2025-07-23 18:13:47,329 - INFO - Using existing Google Doc: Vietop (ID: 1FdFJhNjnA7BQUfepd11WDxGSKWFNnKQaU6ct9tkMKMA)
2025-07-23 18:13:47,329 - INFO - Applying 9 formatting requests...
2025-07-23 18:13:47,329 - INFO - Validated 9/9 requests
2025-07-23 18:13:47,329 - INFO - Validated 9/9 requests
2025-07-23 18:13:47,926 - INFO - Successfully applied formatting to document: Vietop
2025-07-23 18:13:47,927 - INFO - Successfully processed Google Doc: Vietop
2025-07-23 18:13:47,928 - INFO - Syncing note: Quy trình làm việc.md
2025-07-23 18:13:48,371 - INFO - Found existing document: Quy trình làm việc (ID: 1PiEpzyYWY_iLDPjD9WzlwyJAZFDvdFB95gwTiqGaSwQ)
2025-07-23 18:13:48,372 - INFO - Document Quy trình làm việc already exists, updating content...
2025-07-23 18:13:49,585 - INFO - Cleared content from document: 1PiEpzyYWY_iLDPjD9WzlwyJAZFDvdFB95gwTiqGaSwQ
2025-07-23 18:13:49,585 - INFO - Using existing Google Doc: Quy trình làm việc (ID: 1PiEpzyYWY_iLDPjD9WzlwyJAZFDvdFB95gwTiqGaSwQ)
2025-07-23 18:13:49,586 - INFO - Applying 12 formatting requests...
2025-07-23 18:13:49,586 - INFO - Validated 12/12 requests
2025-07-23 18:13:49,586 - INFO - Validated 12/12 requests
2025-07-23 18:13:50,873 - INFO - Successfully applied formatting to document: Quy trình làm việc
2025-07-23 18:13:50,873 - INFO - Successfully processed Google Doc: Quy trình làm việc
2025-07-23 18:13:50,873 - INFO - Syncing note: Data structures & Algorithms.md
2025-07-23 18:13:50,876 - INFO - Found embedded image: 344761700_691730916042250_2784087986434747459_n.jpg
2025-07-23 18:13:50,877 - INFO - Found embedded image: Untitled 1.png
2025-07-23 18:13:50,877 - INFO - Found embedded image: Algorithm.png
2025-07-23 18:13:50,877 - INFO - Found embedded image: Pasted image 20240419132648.png
2025-07-23 18:13:51,374 - INFO - Found existing document: Data structures & Algorithms (ID: 1qmNusT8e6uZcD3OPVhsByJzlpWfueKpL2xo1HdVBZMQ)
2025-07-23 18:13:51,374 - INFO - Document Data structures & Algorithms already exists, updating content...
2025-07-23 18:13:53,267 - INFO - Cleared content from document: 1qmNusT8e6uZcD3OPVhsByJzlpWfueKpL2xo1HdVBZMQ
2025-07-23 18:13:53,268 - INFO - Using existing Google Doc: Data structures & Algorithms (ID: 1qmNusT8e6uZcD3OPVhsByJzlpWfueKpL2xo1HdVBZMQ)
2025-07-23 18:13:53,268 - INFO - Applying 209 formatting requests...
2025-07-23 18:13:53,285 - INFO - Validated 209/209 requests
2025-07-23 18:13:53,286 - INFO - Validated 209/209 requests
2025-07-23 18:13:54,236 - INFO - Successfully applied formatting to document: Data structures & Algorithms
2025-07-23 18:13:54,236 - INFO - Successfully processed Google Doc: Data structures & Algorithms
2025-07-23 18:13:54,261 - INFO - Processing image: Pasted image 20240419132648.png at position 6974
2025-07-23 18:13:54,666 - INFO - Found existing file: Pasted image 20240419132648.png (ID: 17QJ9_AR6DLUzY1N7sMQfIyLVtvYP-XZx)
2025-07-23 18:13:54,666 - INFO - File Pasted image 20240419132648.png already exists, updating...
2025-07-23 18:14:00,082 - INFO - Updated existing file: Pasted image 20240419132648.png (ID: 17QJ9_AR6DLUzY1N7sMQfIyLVtvYP-XZx)
2025-07-23 18:14:02,582 - INFO - Inserted image Pasted image 20240419132648.png into document
2025-07-23 18:14:02,582 - INFO - Successfully inserted image: Pasted image 20240419132648.png at index 6974
2025-07-23 18:14:02,984 - INFO - Processing image: Algorithm.png at position 6420
2025-07-23 18:14:03,403 - INFO - Found existing file: Algorithm.png (ID: 1f44pOGyeW6VaOeCvSFYLJQg5zsXz9nS3)
2025-07-23 18:14:03,403 - INFO - File Algorithm.png already exists, updating...
2025-07-23 18:14:07,279 - INFO - Updated existing file: Algorithm.png (ID: 1f44pOGyeW6VaOeCvSFYLJQg5zsXz9nS3)
2025-07-23 18:14:09,130 - INFO - Inserted image Algorithm.png into document
2025-07-23 18:14:09,130 - INFO - Successfully inserted image: Algorithm.png at index 6420
2025-07-23 18:14:09,508 - INFO - Processing image: Untitled 1.png at position 5333
2025-07-23 18:14:09,989 - INFO - Found existing file: Untitled 1.png (ID: 1bRJbw31gFtTO-KlJt0GdC9nDQ3Lko_ub)
2025-07-23 18:14:09,990 - INFO - File Untitled 1.png already exists, updating...
2025-07-23 18:14:13,832 - INFO - Updated existing file: Untitled 1.png (ID: 1bRJbw31gFtTO-KlJt0GdC9nDQ3Lko_ub)
2025-07-23 18:14:16,885 - INFO - Inserted image Untitled 1.png into document
2025-07-23 18:14:16,886 - INFO - Successfully inserted image: Untitled 1.png at index 5333
2025-07-23 18:14:17,266 - INFO - Processing image: 344761700_691730916042250_2784087986434747459_n.jpg at position 14
2025-07-23 18:14:18,194 - INFO - Found existing file: 344761700_691730916042250_2784087986434747459_n.jpg (ID: 1aDWBRveoIqYQRndZazQoh1R90poAlP03)
2025-07-23 18:14:18,194 - INFO - File 344761700_691730916042250_2784087986434747459_n.jpg already exists, updating...
2025-07-23 18:14:22,110 - INFO - Updated existing file: 344761700_691730916042250_2784087986434747459_n.jpg (ID: 1aDWBRveoIqYQRndZazQoh1R90poAlP03)
2025-07-23 18:14:24,296 - INFO - Inserted image 344761700_691730916042250_2784087986434747459_n.jpg into document
2025-07-23 18:14:24,297 - INFO - Successfully inserted image: 344761700_691730916042250_2784087986434747459_n.jpg at index 14
2025-07-23 18:14:24,676 - INFO - Syncing note: Inceptionlabs - Viclass.md
2025-07-23 18:14:25,148 - INFO - Found existing document: Inceptionlabs - Viclass (ID: 1tr6VqbjyS-rdFVlZStrjciLu7EaCO8U86LySe5iXyXU)
2025-07-23 18:14:25,148 - INFO - Document Inceptionlabs - Viclass already exists, updating content...
2025-07-23 18:14:26,451 - INFO - Cleared content from document: 1tr6VqbjyS-rdFVlZStrjciLu7EaCO8U86LySe5iXyXU
2025-07-23 18:14:26,451 - INFO - Using existing Google Doc: Inceptionlabs - Viclass (ID: 1tr6VqbjyS-rdFVlZStrjciLu7EaCO8U86LySe5iXyXU)
2025-07-23 18:14:26,451 - INFO - Applying 35 formatting requests...
2025-07-23 18:14:26,452 - INFO - Validated 35/35 requests
2025-07-23 18:14:26,452 - INFO - Validated 35/35 requests
2025-07-23 18:14:27,045 - INFO - Successfully applied formatting to document: Inceptionlabs - Viclass
2025-07-23 18:14:27,047 - INFO - Successfully processed Google Doc: Inceptionlabs - Viclass
2025-07-23 18:14:27,047 - INFO - Syncing note: Wordpress.md
2025-07-23 18:14:27,466 - INFO - Found existing document: Wordpress (ID: 1WBtsxjhvfs47JIq3LEIowKek6Ea9PGFoIC-Il0GH44o)
2025-07-23 18:14:27,466 - INFO - Document Wordpress already exists, updating content...
2025-07-23 18:14:28,872 - INFO - Cleared content from document: 1WBtsxjhvfs47JIq3LEIowKek6Ea9PGFoIC-Il0GH44o
2025-07-23 18:14:28,873 - INFO - Using existing Google Doc: Wordpress (ID: 1WBtsxjhvfs47JIq3LEIowKek6Ea9PGFoIC-Il0GH44o)
2025-07-23 18:14:28,873 - INFO - Applying 15 formatting requests...
2025-07-23 18:14:28,874 - INFO - Validated 15/15 requests
2025-07-23 18:14:28,874 - INFO - Validated 15/15 requests
2025-07-23 18:14:29,532 - INFO - Successfully applied formatting to document: Wordpress
2025-07-23 18:14:29,533 - INFO - Successfully processed Google Doc: Wordpress
2025-07-23 18:14:29,533 - INFO - Syncing note: Compiler.md
2025-07-23 18:14:29,963 - INFO - Found existing document: Compiler (ID: 1xGs6rKeXDyL0WtXfzynW4Yzz9Ukv6-phZKH5KDVqNTw)
2025-07-23 18:14:29,963 - INFO - Document Compiler already exists, updating content...
2025-07-23 18:14:31,174 - INFO - Cleared content from document: 1xGs6rKeXDyL0WtXfzynW4Yzz9Ukv6-phZKH5KDVqNTw
2025-07-23 18:14:31,174 - INFO - Using existing Google Doc: Compiler (ID: 1xGs6rKeXDyL0WtXfzynW4Yzz9Ukv6-phZKH5KDVqNTw)
2025-07-23 18:14:31,174 - INFO - Applying 3 formatting requests...
2025-07-23 18:14:31,174 - INFO - Validated 3/3 requests
2025-07-23 18:14:31,174 - INFO - Validated 3/3 requests
2025-07-23 18:14:31,874 - INFO - Successfully applied formatting to document: Compiler
2025-07-23 18:14:31,874 - INFO - Successfully processed Google Doc: Compiler
2025-07-23 18:14:31,874 - INFO - Syncing note: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview.md
2025-07-23 18:14:32,304 - INFO - Found existing document: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview (ID: 1_SygAZcUzxRla_rMmvkBNUCe6M-eXJvfzm4dmmzYdlM)
2025-07-23 18:14:32,304 - INFO - Document Tổng hợp các nguồn ôn luyện thuật toán & Coding interview already exists, updating content...
2025-07-23 18:14:33,579 - INFO - Cleared content from document: 1_SygAZcUzxRla_rMmvkBNUCe6M-eXJvfzm4dmmzYdlM
2025-07-23 18:14:33,579 - INFO - Using existing Google Doc: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview (ID: 1_SygAZcUzxRla_rMmvkBNUCe6M-eXJvfzm4dmmzYdlM)
2025-07-23 18:14:33,579 - INFO - Applying 50 formatting requests...
2025-07-23 18:14:33,583 - INFO - Validated 50/50 requests
2025-07-23 18:14:33,583 - INFO - Validated 50/50 requests
2025-07-23 18:14:34,199 - INFO - Successfully applied formatting to document: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview
2025-07-23 18:14:34,200 - INFO - Successfully processed Google Doc: Tổng hợp các nguồn ôn luyện thuật toán & Coding interview
2025-07-23 18:14:34,201 - INFO - Syncing note: Fashion - Tủ đồ - Quần áo.md
2025-07-23 18:14:34,629 - INFO - Found existing document: Fashion - Tủ đồ - Quần áo (ID: 1W7eZsLuDMSoQrm3PHiImtDHtRkYXCJhonj6I7YU7Htk)
2025-07-23 18:14:34,629 - INFO - Document Fashion - Tủ đồ - Quần áo already exists, updating content...
2025-07-23 18:14:35,746 - INFO - Cleared content from document: 1W7eZsLuDMSoQrm3PHiImtDHtRkYXCJhonj6I7YU7Htk
2025-07-23 18:14:35,746 - INFO - Using existing Google Doc: Fashion - Tủ đồ - Quần áo (ID: 1W7eZsLuDMSoQrm3PHiImtDHtRkYXCJhonj6I7YU7Htk)
2025-07-23 18:14:35,747 - INFO - Applying 20 formatting requests...
2025-07-23 18:14:35,747 - INFO - Validated 20/20 requests
2025-07-23 18:14:35,748 - INFO - Validated 20/20 requests
2025-07-23 18:14:36,598 - INFO - Successfully applied formatting to document: Fashion - Tủ đồ - Quần áo
2025-07-23 18:14:36,598 - INFO - Successfully processed Google Doc: Fashion - Tủ đồ - Quần áo
2025-07-23 18:14:36,598 - INFO - Syncing note: Angular.md
2025-07-23 18:14:37,647 - INFO - Found existing document: Angular (ID: 14V0wyPwfKJ94bgyT7Wt8khHmVQbHNnPa5I8fD0SMIfM)
2025-07-23 18:14:37,648 - INFO - Document Angular already exists, updating content...
2025-07-23 18:14:39,183 - INFO - Cleared content from document: 14V0wyPwfKJ94bgyT7Wt8khHmVQbHNnPa5I8fD0SMIfM
2025-07-23 18:14:39,184 - INFO - Using existing Google Doc: Angular (ID: 14V0wyPwfKJ94bgyT7Wt8khHmVQbHNnPa5I8fD0SMIfM)
2025-07-23 18:14:39,184 - INFO - Applying 34 formatting requests...
2025-07-23 18:14:39,185 - INFO - Validated 34/34 requests
2025-07-23 18:14:39,185 - INFO - Validated 34/34 requests
2025-07-23 18:14:39,981 - INFO - Successfully applied formatting to document: Angular
2025-07-23 18:14:39,981 - INFO - Successfully processed Google Doc: Angular
2025-07-23 18:14:39,982 - INFO - Syncing note: Nest.md
2025-07-23 18:14:40,394 - INFO - Found existing document: Nest (ID: 1YSuP4LkWehy42wHEiFIRLPvhRvVutjMqK3w1VCC6xf8)
2025-07-23 18:14:40,394 - INFO - Document Nest already exists, updating content...
2025-07-23 18:14:41,763 - INFO - Cleared content from document: 1YSuP4LkWehy42wHEiFIRLPvhRvVutjMqK3w1VCC6xf8
2025-07-23 18:14:41,763 - INFO - Using existing Google Doc: Nest (ID: 1YSuP4LkWehy42wHEiFIRLPvhRvVutjMqK3w1VCC6xf8)
2025-07-23 18:14:41,763 - INFO - Applying 5 formatting requests...
2025-07-23 18:14:41,763 - INFO - Validated 5/5 requests
2025-07-23 18:14:41,763 - INFO - Validated 5/5 requests
2025-07-23 18:14:42,504 - INFO - Successfully applied formatting to document: Nest
2025-07-23 18:14:42,505 - INFO - Successfully processed Google Doc: Nest
2025-07-23 18:14:42,505 - INFO - Syncing note: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu.md
2025-07-23 18:14:42,975 - INFO - Found existing document: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu (ID: 1sR-pgXl1RQAtUFTuNHcBz_sp5mIVagWMvLnvTHBen3I)
2025-07-23 18:14:42,975 - INFO - Document Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu already exists, updating content...
2025-07-23 18:14:44,275 - INFO - Cleared content from document: 1sR-pgXl1RQAtUFTuNHcBz_sp5mIVagWMvLnvTHBen3I
2025-07-23 18:14:44,277 - INFO - Using existing Google Doc: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu (ID: 1sR-pgXl1RQAtUFTuNHcBz_sp5mIVagWMvLnvTHBen3I)
2025-07-23 18:14:44,277 - INFO - Applying 9 formatting requests...
2025-07-23 18:14:44,283 - INFO - Validated 9/9 requests
2025-07-23 18:14:44,283 - INFO - Validated 9/9 requests
2025-07-23 18:14:45,144 - INFO - Successfully applied formatting to document: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu
2025-07-23 18:14:45,144 - INFO - Successfully processed Google Doc: Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu
2025-07-23 18:14:45,144 - INFO - Syncing note: English with LLM - Câu bị động.md
2025-07-23 18:14:45,614 - INFO - Found existing document: English with LLM - Câu bị động (ID: 12_sisehaPN1CYdofo_mN75umz8YOK84MwaV3QNqNZQs)
2025-07-23 18:14:45,614 - INFO - Document English with LLM - Câu bị động already exists, updating content...
2025-07-23 18:14:47,286 - INFO - Cleared content from document: 12_sisehaPN1CYdofo_mN75umz8YOK84MwaV3QNqNZQs
2025-07-23 18:14:47,287 - INFO - Using existing Google Doc: English with LLM - Câu bị động (ID: 12_sisehaPN1CYdofo_mN75umz8YOK84MwaV3QNqNZQs)
2025-07-23 18:14:47,287 - INFO - Applying 77 formatting requests...
2025-07-23 18:14:47,291 - INFO - Validated 77/77 requests
2025-07-23 18:14:47,291 - INFO - Validated 77/77 requests
2025-07-23 18:14:48,064 - INFO - Successfully applied formatting to document: English with LLM - Câu bị động
2025-07-23 18:14:48,064 - INFO - Successfully processed Google Doc: English with LLM - Câu bị động
2025-07-23 18:14:48,065 - INFO - Syncing note: Mục lục thuật toán.md
2025-07-23 18:14:48,507 - INFO - Found existing document: Mục lục thuật toán (ID: 1GWFq4UXJnMZc2uzD7c1qkktHt1qlIpGaOVvbnpL-6N0)
2025-07-23 18:14:48,507 - INFO - Document Mục lục thuật toán already exists, updating content...
2025-07-23 18:14:49,731 - INFO - Cleared content from document: 1GWFq4UXJnMZc2uzD7c1qkktHt1qlIpGaOVvbnpL-6N0
2025-07-23 18:14:49,732 - INFO - Using existing Google Doc: Mục lục thuật toán (ID: 1GWFq4UXJnMZc2uzD7c1qkktHt1qlIpGaOVvbnpL-6N0)
2025-07-23 18:14:49,732 - INFO - Applying 88 formatting requests...
2025-07-23 18:14:49,736 - INFO - Validated 88/88 requests
2025-07-23 18:14:49,737 - INFO - Validated 88/88 requests
2025-07-23 18:14:50,304 - INFO - Successfully applied formatting to document: Mục lục thuật toán
2025-07-23 18:14:50,305 - INFO - Successfully processed Google Doc: Mục lục thuật toán
2025-07-23 18:14:50,305 - INFO - Syncing note: VPC - Virtual Private Cloud - AZ - Availability Zone.md
2025-07-23 18:14:50,740 - INFO - Found existing document: VPC - Virtual Private Cloud - AZ - Availability Zone (ID: 1B-FcdLjGiBCgmBpLKHlEzX3gk7QrPaa_b0YxBpB6OT0)
2025-07-23 18:14:50,741 - INFO - Document VPC - Virtual Private Cloud - AZ - Availability Zone already exists, updating content...
2025-07-23 18:14:52,265 - INFO - Cleared content from document: 1B-FcdLjGiBCgmBpLKHlEzX3gk7QrPaa_b0YxBpB6OT0
2025-07-23 18:14:52,266 - INFO - Using existing Google Doc: VPC - Virtual Private Cloud - AZ - Availability Zone (ID: 1B-FcdLjGiBCgmBpLKHlEzX3gk7QrPaa_b0YxBpB6OT0)
2025-07-23 18:14:52,267 - INFO - Applying 159 formatting requests...
2025-07-23 18:14:52,278 - INFO - Validated 159/159 requests
2025-07-23 18:14:52,278 - INFO - Validated 159/159 requests
2025-07-23 18:14:52,958 - INFO - Successfully applied formatting to document: VPC - Virtual Private Cloud - AZ - Availability Zone
2025-07-23 18:14:52,958 - INFO - Successfully processed Google Doc: VPC - Virtual Private Cloud - AZ - Availability Zone
2025-07-23 18:14:52,959 - INFO - Syncing note: Flutter.md
2025-07-23 18:14:53,896 - INFO - Found existing document: Flutter (ID: 1xWx5bWhqSugMfEKVWSONzX96pTGLoYOb7HP-MF4D0sI)
2025-07-23 18:14:53,897 - INFO - Document Flutter already exists, updating content...
2025-07-23 18:14:55,199 - INFO - Cleared content from document: 1xWx5bWhqSugMfEKVWSONzX96pTGLoYOb7HP-MF4D0sI
2025-07-23 18:14:55,200 - INFO - Using existing Google Doc: Flutter (ID: 1xWx5bWhqSugMfEKVWSONzX96pTGLoYOb7HP-MF4D0sI)
2025-07-23 18:14:55,200 - INFO - Applying 4 formatting requests...
2025-07-23 18:14:55,200 - INFO - Validated 4/4 requests
2025-07-23 18:14:55,200 - INFO - Validated 4/4 requests
2025-07-23 18:14:55,908 - INFO - Successfully applied formatting to document: Flutter
2025-07-23 18:14:55,908 - INFO - Successfully processed Google Doc: Flutter
2025-07-23 18:14:55,908 - INFO - Syncing note: Some shit I need.md
2025-07-23 18:14:56,469 - INFO - Found existing document: Some shit I need (ID: 1JOQrIZvPLxsyRqI3sKXvkqaA-WBC1Lvii3LYKG1aKWg)
2025-07-23 18:14:56,469 - INFO - Document Some shit I need already exists, updating content...
2025-07-23 18:14:57,951 - INFO - Cleared content from document: 1JOQrIZvPLxsyRqI3sKXvkqaA-WBC1Lvii3LYKG1aKWg
2025-07-23 18:14:57,952 - INFO - Using existing Google Doc: Some shit I need (ID: 1JOQrIZvPLxsyRqI3sKXvkqaA-WBC1Lvii3LYKG1aKWg)
2025-07-23 18:14:57,952 - INFO - Applying 124 formatting requests...
2025-07-23 18:14:57,961 - INFO - Validated 124/124 requests
2025-07-23 18:14:57,961 - INFO - Validated 124/124 requests
2025-07-23 18:14:58,704 - INFO - Successfully applied formatting to document: Some shit I need
2025-07-23 18:14:58,704 - INFO - Successfully processed Google Doc: Some shit I need
2025-07-23 18:14:58,704 - INFO - Syncing note: Câu hỏi phỏng vấn.md
2025-07-23 18:14:59,241 - INFO - Found existing document: Câu hỏi phỏng vấn (ID: 1hWz7vMe0GRHimiKAQHAADINMr8dOXp9j_5Z114AW9Qw)
2025-07-23 18:14:59,241 - INFO - Document Câu hỏi phỏng vấn already exists, updating content...
2025-07-23 18:15:00,725 - INFO - Cleared content from document: 1hWz7vMe0GRHimiKAQHAADINMr8dOXp9j_5Z114AW9Qw
2025-07-23 18:15:00,726 - INFO - Using existing Google Doc: Câu hỏi phỏng vấn (ID: 1hWz7vMe0GRHimiKAQHAADINMr8dOXp9j_5Z114AW9Qw)
2025-07-23 18:15:00,726 - INFO - Applying 74 formatting requests...
2025-07-23 18:15:00,731 - INFO - Validated 74/74 requests
2025-07-23 18:15:00,732 - INFO - Validated 74/74 requests
2025-07-23 18:15:01,445 - INFO - Successfully applied formatting to document: Câu hỏi phỏng vấn
2025-07-23 18:15:01,445 - INFO - Successfully processed Google Doc: Câu hỏi phỏng vấn
2025-07-23 18:15:01,445 - INFO - Syncing note: Danh sách kiến thức chuẩn bị để phỏng vấn.md
2025-07-23 18:15:01,902 - INFO - Found existing document: Danh sách kiến thức chuẩn bị để phỏng vấn (ID: 16PJVGXIDl8_-_gN2U4Pqz5CzzDSkZhd7uQ2Ap0DQqlM)
2025-07-23 18:15:01,903 - INFO - Document Danh sách kiến thức chuẩn bị để phỏng vấn already exists, updating content...
2025-07-23 18:15:03,179 - INFO - Cleared content from document: 16PJVGXIDl8_-_gN2U4Pqz5CzzDSkZhd7uQ2Ap0DQqlM
2025-07-23 18:15:03,179 - INFO - Using existing Google Doc: Danh sách kiến thức chuẩn bị để phỏng vấn (ID: 16PJVGXIDl8_-_gN2U4Pqz5CzzDSkZhd7uQ2Ap0DQqlM)
2025-07-23 18:15:03,179 - INFO - Applying 25 formatting requests...
2025-07-23 18:15:03,179 - INFO - Validated 25/25 requests
2025-07-23 18:15:03,179 - INFO - Validated 25/25 requests
2025-07-23 18:15:03,788 - INFO - Successfully applied formatting to document: Danh sách kiến thức chuẩn bị để phỏng vấn
2025-07-23 18:15:03,788 - INFO - Successfully processed Google Doc: Danh sách kiến thức chuẩn bị để phỏng vấn
2025-07-23 18:15:03,788 - INFO - Syncing note: Product management.md
2025-07-23 18:15:04,245 - INFO - Found existing document: Product management (ID: 14-5Wx_YebZx8em1j-SzaJgy2pTv1URUvqB5kMpBEUus)
2025-07-23 18:15:04,246 - INFO - Document Product management already exists, updating content...
2025-07-23 18:15:05,559 - INFO - Cleared content from document: 14-5Wx_YebZx8em1j-SzaJgy2pTv1URUvqB5kMpBEUus
2025-07-23 18:15:05,559 - INFO - Using existing Google Doc: Product management (ID: 14-5Wx_YebZx8em1j-SzaJgy2pTv1URUvqB5kMpBEUus)
2025-07-23 18:15:05,559 - INFO - Applying 9 formatting requests...
2025-07-23 18:15:05,559 - INFO - Validated 9/9 requests
2025-07-23 18:15:05,559 - INFO - Validated 9/9 requests
2025-07-23 18:15:06,309 - INFO - Successfully applied formatting to document: Product management
2025-07-23 18:15:06,310 - INFO - Successfully processed Google Doc: Product management
2025-07-23 18:15:06,310 - INFO - Syncing note: Thanh toán chuyển khoản ngân hàng.md
2025-07-23 18:15:06,786 - INFO - Found existing document: Thanh toán chuyển khoản ngân hàng (ID: 1VP03gjdIjiNT9JUXQtE_P_QX11v-O8KaLfGry6X8hh4)
2025-07-23 18:15:06,786 - INFO - Document Thanh toán chuyển khoản ngân hàng already exists, updating content...
2025-07-23 18:15:08,068 - INFO - Cleared content from document: 1VP03gjdIjiNT9JUXQtE_P_QX11v-O8KaLfGry6X8hh4
2025-07-23 18:15:08,068 - INFO - Using existing Google Doc: Thanh toán chuyển khoản ngân hàng (ID: 1VP03gjdIjiNT9JUXQtE_P_QX11v-O8KaLfGry6X8hh4)
2025-07-23 18:15:08,068 - INFO - Applying 46 formatting requests...
2025-07-23 18:15:08,071 - INFO - Validated 46/46 requests
2025-07-23 18:15:08,071 - INFO - Validated 46/46 requests
2025-07-23 18:15:08,694 - INFO - Successfully applied formatting to document: Thanh toán chuyển khoản ngân hàng
2025-07-23 18:15:08,694 - INFO - Successfully processed Google Doc: Thanh toán chuyển khoản ngân hàng
2025-07-23 18:15:08,695 - INFO - Syncing note: MCP - Model Context Protocol.md
2025-07-23 18:15:09,236 - INFO - Found existing document: MCP - Model Context Protocol (ID: 1vln4VCgRAOkkmNE_2HFiAtdS5l39AEGYg9pOI2X0A3E)
2025-07-23 18:15:09,236 - INFO - Document MCP - Model Context Protocol already exists, updating content...
2025-07-23 18:15:10,463 - INFO - Cleared content from document: 1vln4VCgRAOkkmNE_2HFiAtdS5l39AEGYg9pOI2X0A3E
2025-07-23 18:15:10,463 - INFO - Using existing Google Doc: MCP - Model Context Protocol (ID: 1vln4VCgRAOkkmNE_2HFiAtdS5l39AEGYg9pOI2X0A3E)
2025-07-23 18:15:10,463 - INFO - Applying 50 formatting requests...
2025-07-23 18:15:10,464 - INFO - Validated 50/50 requests
2025-07-23 18:15:10,464 - INFO - Validated 50/50 requests
2025-07-23 18:15:11,269 - INFO - Successfully applied formatting to document: MCP - Model Context Protocol
2025-07-23 18:15:11,269 - INFO - Successfully processed Google Doc: MCP - Model Context Protocol
2025-07-23 18:15:11,269 - INFO - Syncing note: Java Microservices.md
2025-07-23 18:15:11,740 - INFO - Found existing document: Java Microservices (ID: 1Pf9dQJAK2BA1pl_qC75H9yCfB2bjDbUmrwmE_z1hLbo)
2025-07-23 18:15:11,741 - INFO - Document Java Microservices already exists, updating content...
2025-07-23 18:15:13,015 - INFO - Cleared content from document: 1Pf9dQJAK2BA1pl_qC75H9yCfB2bjDbUmrwmE_z1hLbo
2025-07-23 18:15:13,015 - INFO - Using existing Google Doc: Java Microservices (ID: 1Pf9dQJAK2BA1pl_qC75H9yCfB2bjDbUmrwmE_z1hLbo)
2025-07-23 18:15:13,015 - INFO - Applying 5 formatting requests...
2025-07-23 18:15:13,015 - INFO - Validated 5/5 requests
2025-07-23 18:15:13,015 - INFO - Validated 5/5 requests
2025-07-23 18:15:14,065 - INFO - Successfully applied formatting to document: Java Microservices
2025-07-23 18:15:14,065 - INFO - Successfully processed Google Doc: Java Microservices
2025-07-23 18:15:14,066 - INFO - Syncing note: Cách giải các bài thuật toán.md
2025-07-23 18:15:14,540 - INFO - Found existing document: Cách giải các bài thuật toán (ID: 12rvgX-PgFhTUa4VDPrd0tJXN3SzkCMRwwxgWEJVB_ns)
2025-07-23 18:15:14,541 - INFO - Document Cách giải các bài thuật toán already exists, updating content...
2025-07-23 18:15:15,850 - INFO - Cleared content from document: 12rvgX-PgFhTUa4VDPrd0tJXN3SzkCMRwwxgWEJVB_ns
2025-07-23 18:15:15,850 - INFO - Using existing Google Doc: Cách giải các bài thuật toán (ID: 12rvgX-PgFhTUa4VDPrd0tJXN3SzkCMRwwxgWEJVB_ns)
2025-07-23 18:15:15,850 - INFO - Applying 2 formatting requests...
2025-07-23 18:15:15,850 - INFO - Validated 2/2 requests
2025-07-23 18:15:15,850 - INFO - Validated 2/2 requests
2025-07-23 18:15:16,461 - INFO - Successfully applied formatting to document: Cách giải các bài thuật toán
2025-07-23 18:15:16,461 - INFO - Successfully processed Google Doc: Cách giải các bài thuật toán
2025-07-23 18:15:16,461 - INFO - Syncing note: Java.md
2025-07-23 18:15:17,019 - INFO - Found existing document: Java (ID: 1JlBhLTWvVpD3_rEbVJgm44GxPOAkB2HAg8sCqbUD1sM)
2025-07-23 18:15:17,019 - INFO - Document Java already exists, updating content...
2025-07-23 18:15:18,668 - INFO - Cleared content from document: 1JlBhLTWvVpD3_rEbVJgm44GxPOAkB2HAg8sCqbUD1sM
2025-07-23 18:15:18,668 - INFO - Using existing Google Doc: Java (ID: 1JlBhLTWvVpD3_rEbVJgm44GxPOAkB2HAg8sCqbUD1sM)
2025-07-23 18:15:18,668 - INFO - Applying 192 formatting requests...
2025-07-23 18:15:18,671 - INFO - Validated 192/192 requests
2025-07-23 18:15:18,671 - INFO - Validated 192/192 requests
2025-07-23 18:15:19,580 - INFO - Successfully applied formatting to document: Java
2025-07-23 18:15:19,580 - INFO - Successfully processed Google Doc: Java
2025-07-23 18:15:19,580 - INFO - Syncing note: English with LLM - Mạo từ.md
2025-07-23 18:15:20,294 - INFO - Found existing document: English with LLM - Mạo từ (ID: 1sfcrpBFTbAj5hg1yiYBSAfVW6iySGmrqEejRaUaOOqM)
2025-07-23 18:15:20,295 - INFO - Document English with LLM - Mạo từ already exists, updating content...
2025-07-23 18:15:21,851 - INFO - Cleared content from document: 1sfcrpBFTbAj5hg1yiYBSAfVW6iySGmrqEejRaUaOOqM
2025-07-23 18:15:21,853 - INFO - Using existing Google Doc: English with LLM - Mạo từ (ID: 1sfcrpBFTbAj5hg1yiYBSAfVW6iySGmrqEejRaUaOOqM)
2025-07-23 18:15:21,853 - INFO - Applying 114 formatting requests...
2025-07-23 18:15:21,860 - INFO - Validated 114/114 requests
2025-07-23 18:15:21,861 - INFO - Validated 114/114 requests
2025-07-23 18:15:22,594 - INFO - Successfully applied formatting to document: English with LLM - Mạo từ
2025-07-23 18:15:22,595 - INFO - Successfully processed Google Doc: English with LLM - Mạo từ
2025-07-23 18:15:22,595 - INFO - Syncing note: Logging.md
2025-07-23 18:15:23,034 - INFO - Found existing document: Logging (ID: 1ZGSSBYfIoe2FkHssbQEet_c-XnR7nxPDMQDIi8WXi20)
2025-07-23 18:15:23,034 - INFO - Document Logging already exists, updating content...
2025-07-23 18:15:24,385 - INFO - Cleared content from document: 1ZGSSBYfIoe2FkHssbQEet_c-XnR7nxPDMQDIi8WXi20
2025-07-23 18:15:24,385 - INFO - Using existing Google Doc: Logging (ID: 1ZGSSBYfIoe2FkHssbQEet_c-XnR7nxPDMQDIi8WXi20)
2025-07-23 18:15:24,385 - INFO - Applying 15 formatting requests...
2025-07-23 18:15:24,387 - INFO - Validated 15/15 requests
2025-07-23 18:15:24,387 - INFO - Validated 15/15 requests
2025-07-23 18:15:25,042 - INFO - Successfully applied formatting to document: Logging
2025-07-23 18:15:25,042 - INFO - Successfully processed Google Doc: Logging
2025-07-23 18:15:25,042 - INFO - Syncing note: Microservices.md
2025-07-23 18:15:25,508 - INFO - Found existing document: Microservices (ID: 1oAI1vQbgyIpX5meTwlVfYpjFHiyzgBK55ZAIoxbbHvE)
2025-07-23 18:15:25,508 - INFO - Document Microservices already exists, updating content...
2025-07-23 18:15:27,647 - INFO - Cleared content from document: 1oAI1vQbgyIpX5meTwlVfYpjFHiyzgBK55ZAIoxbbHvE
2025-07-23 18:15:27,648 - INFO - Using existing Google Doc: Microservices (ID: 1oAI1vQbgyIpX5meTwlVfYpjFHiyzgBK55ZAIoxbbHvE)
2025-07-23 18:15:27,648 - INFO - Applying 694 formatting requests...
2025-07-23 18:15:27,670 - INFO - Validated 694/694 requests
2025-07-23 18:15:27,670 - INFO - Validated 694/694 requests
2025-07-23 18:15:29,706 - INFO - Successfully applied formatting to document: Microservices
2025-07-23 18:15:29,706 - INFO - Successfully processed Google Doc: Microservices
2025-07-23 18:15:29,707 - INFO - Syncing note: IaaS.md
2025-07-23 18:15:30,164 - INFO - Found existing document: IaaS (ID: 1jh9lEkOUjZD1bgI-MlSAu-UHd34YBbVLKh4zdrOfbYA)
2025-07-23 18:15:30,164 - INFO - Document IaaS already exists, updating content...
2025-07-23 18:15:31,340 - INFO - Cleared content from document: 1jh9lEkOUjZD1bgI-MlSAu-UHd34YBbVLKh4zdrOfbYA
2025-07-23 18:15:31,340 - INFO - Using existing Google Doc: IaaS (ID: 1jh9lEkOUjZD1bgI-MlSAu-UHd34YBbVLKh4zdrOfbYA)
2025-07-23 18:15:31,340 - INFO - Applying 4 formatting requests...
2025-07-23 18:15:31,340 - INFO - Validated 4/4 requests
2025-07-23 18:15:31,341 - INFO - Validated 4/4 requests
2025-07-23 18:15:31,942 - INFO - Successfully applied formatting to document: IaaS
2025-07-23 18:15:31,943 - INFO - Successfully processed Google Doc: IaaS
2025-07-23 18:15:31,943 - INFO - Syncing note: Golang Scheduler.md
2025-07-23 18:15:32,370 - INFO - Found existing document: Golang Scheduler (ID: 1BrK3S-U4a7FxKCCf3HObBfXxQuONyUHJ67ITgyIwNuI)
2025-07-23 18:15:32,370 - INFO - Document Golang Scheduler already exists, updating content...
2025-07-23 18:15:33,741 - INFO - Cleared content from document: 1BrK3S-U4a7FxKCCf3HObBfXxQuONyUHJ67ITgyIwNuI
2025-07-23 18:15:33,742 - INFO - Using existing Google Doc: Golang Scheduler (ID: 1BrK3S-U4a7FxKCCf3HObBfXxQuONyUHJ67ITgyIwNuI)
2025-07-23 18:15:33,742 - INFO - Applying 8 formatting requests...
2025-07-23 18:15:33,743 - INFO - Validated 8/8 requests
2025-07-23 18:15:33,743 - INFO - Validated 8/8 requests
2025-07-23 18:15:34,327 - INFO - Successfully applied formatting to document: Golang Scheduler
2025-07-23 18:15:34,327 - INFO - Successfully processed Google Doc: Golang Scheduler
2025-07-23 18:15:34,327 - INFO - Syncing note: Solution sao lưu lịch sử chỉnh sửa.md
2025-07-23 18:15:34,328 - INFO - Found embedded image: Pasted image 20240706134702.png
2025-07-23 18:15:34,761 - INFO - Found existing document: Solution sao lưu lịch sử chỉnh sửa (ID: 11rldC4yPRy99zEnkwbU7ErglbcDAhSqdxdbEcT_qupg)
2025-07-23 18:15:34,761 - INFO - Document Solution sao lưu lịch sử chỉnh sửa already exists, updating content...
2025-07-23 18:15:36,494 - INFO - Cleared content from document: 11rldC4yPRy99zEnkwbU7ErglbcDAhSqdxdbEcT_qupg
2025-07-23 18:15:36,494 - INFO - Using existing Google Doc: Solution sao lưu lịch sử chỉnh sửa (ID: 11rldC4yPRy99zEnkwbU7ErglbcDAhSqdxdbEcT_qupg)
2025-07-23 18:15:36,495 - INFO - Applying 31 formatting requests...
2025-07-23 18:15:36,499 - INFO - Validated 31/31 requests
2025-07-23 18:15:36,499 - INFO - Validated 31/31 requests
2025-07-23 18:15:37,187 - INFO - Successfully applied formatting to document: Solution sao lưu lịch sử chỉnh sửa
2025-07-23 18:15:37,187 - INFO - Successfully processed Google Doc: Solution sao lưu lịch sử chỉnh sửa
2025-07-23 18:15:37,190 - INFO - Processing image: Pasted image 20240706134702.png at position 2427
2025-07-23 18:15:37,622 - INFO - Found existing file: Pasted image 20240706134702.png (ID: 1oB8fhJka3_tA455y-dKfmHqI5wBw0TT3)
2025-07-23 18:15:37,623 - INFO - File Pasted image 20240706134702.png already exists, updating...
2025-07-23 18:15:41,316 - INFO - Updated existing file: Pasted image 20240706134702.png (ID: 1oB8fhJka3_tA455y-dKfmHqI5wBw0TT3)
2025-07-23 18:15:43,772 - INFO - Inserted image Pasted image 20240706134702.png into document
2025-07-23 18:15:43,772 - INFO - Successfully inserted image: Pasted image 20240706134702.png at index 2427
2025-07-23 18:15:44,405 - INFO - Syncing note: Postgresql.md
2025-07-23 18:15:44,856 - INFO - Found existing document: Postgresql (ID: 136ZfZ5SDvq859xOF4TRHHMSavPss6zlxgUyQ3oMtIEs)
2025-07-23 18:15:44,856 - INFO - Document Postgresql already exists, updating content...
2025-07-23 18:15:46,219 - INFO - Cleared content from document: 136ZfZ5SDvq859xOF4TRHHMSavPss6zlxgUyQ3oMtIEs
2025-07-23 18:15:46,219 - INFO - Using existing Google Doc: Postgresql (ID: 136ZfZ5SDvq859xOF4TRHHMSavPss6zlxgUyQ3oMtIEs)
2025-07-23 18:15:46,219 - INFO - Applying 132 formatting requests...
2025-07-23 18:15:46,223 - INFO - Validated 132/132 requests
2025-07-23 18:15:46,223 - INFO - Validated 132/132 requests
2025-07-23 18:15:46,906 - INFO - Successfully applied formatting to document: Postgresql
2025-07-23 18:15:46,906 - INFO - Successfully processed Google Doc: Postgresql
2025-07-23 18:15:46,906 - INFO - Syncing note: Algorithms & Data Structures CheatSheet.md
2025-07-23 18:15:47,395 - INFO - Found existing document: Algorithms & Data Structures CheatSheet (ID: 1_oFKJKTF8e7A2FaNqy1-uM3hVAjcqOUlv5d_3OdV3lA)
2025-07-23 18:15:47,395 - INFO - Document Algorithms & Data Structures CheatSheet already exists, updating content...
2025-07-23 18:15:49,194 - INFO - Cleared content from document: 1_oFKJKTF8e7A2FaNqy1-uM3hVAjcqOUlv5d_3OdV3lA
2025-07-23 18:15:49,195 - INFO - Using existing Google Doc: Algorithms & Data Structures CheatSheet (ID: 1_oFKJKTF8e7A2FaNqy1-uM3hVAjcqOUlv5d_3OdV3lA)
2025-07-23 18:15:49,195 - INFO - Applying 416 formatting requests...
2025-07-23 18:15:49,224 - INFO - Validated 416/416 requests
2025-07-23 18:15:49,224 - INFO - Validated 416/416 requests
2025-07-23 18:15:51,046 - INFO - Successfully applied formatting to document: Algorithms & Data Structures CheatSheet
2025-07-23 18:15:51,047 - INFO - Successfully processed Google Doc: Algorithms & Data Structures CheatSheet
2025-07-23 18:15:51,047 - INFO - Syncing note: Node.js.md
2025-07-23 18:15:51,485 - INFO - Found existing document: Node.js (ID: 17jSk45dxEtwwas6zu_hCeQnMoukwF14CeZVvmrGRDAU)
2025-07-23 18:15:51,485 - INFO - Document Node.js already exists, updating content...
2025-07-23 18:15:52,865 - INFO - Cleared content from document: 17jSk45dxEtwwas6zu_hCeQnMoukwF14CeZVvmrGRDAU
2025-07-23 18:15:52,866 - INFO - Using existing Google Doc: Node.js (ID: 17jSk45dxEtwwas6zu_hCeQnMoukwF14CeZVvmrGRDAU)
2025-07-23 18:15:52,866 - INFO - Applying 71 formatting requests...
2025-07-23 18:15:52,869 - INFO - Validated 71/71 requests
2025-07-23 18:15:52,869 - INFO - Validated 71/71 requests
2025-07-23 18:15:53,549 - INFO - Successfully applied formatting to document: Node.js
2025-07-23 18:15:53,549 - INFO - Successfully processed Google Doc: Node.js
2025-07-23 18:15:53,549 - INFO - Syncing note: HTTP - HTTPS - TLS - SSL.md
2025-07-23 18:15:53,550 - INFO - Found embedded image: 340771529_617175149885300_3812928478176216761_n.jpg
2025-07-23 18:15:53,550 - INFO - Found embedded image: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg
2025-07-23 18:15:54,043 - INFO - Found existing document: HTTP - HTTPS - TLS - SSL (ID: 1nUWq5aPTI24nOzbC4AUn8i3eDN99NpKY3PlGVsS7aT0)
2025-07-23 18:15:54,043 - INFO - Document HTTP - HTTPS - TLS - SSL already exists, updating content...
2025-07-23 18:15:55,912 - INFO - Cleared content from document: 1nUWq5aPTI24nOzbC4AUn8i3eDN99NpKY3PlGVsS7aT0
2025-07-23 18:15:55,912 - INFO - Using existing Google Doc: HTTP - HTTPS - TLS - SSL (ID: 1nUWq5aPTI24nOzbC4AUn8i3eDN99NpKY3PlGVsS7aT0)
2025-07-23 18:15:55,912 - INFO - Applying 84 formatting requests...
2025-07-23 18:15:55,917 - INFO - Validated 84/84 requests
2025-07-23 18:15:55,918 - INFO - Validated 84/84 requests
2025-07-23 18:15:56,932 - INFO - Successfully applied formatting to document: HTTP - HTTPS - TLS - SSL
2025-07-23 18:15:56,932 - INFO - Successfully processed Google Doc: HTTP - HTTPS - TLS - SSL
2025-07-23 18:15:56,933 - INFO - Processing image: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg at position 504
2025-07-23 18:15:57,366 - INFO - Found existing file: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg (ID: 1bF8nYL2g76LVnV5eqf2MyxTdRpT2DqpC)
2025-07-23 18:15:57,367 - INFO - File 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg already exists, updating...
2025-07-23 18:16:01,494 - INFO - Updated existing file: 095fcc4d-9e86-44a4-9592-eb1285145a64_800x564.jpg (ID: 1bF8nYL2g76LVnV5eqf2MyxTdRpT2DqpC)
2025-07-23 18:32:53,215 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 18:32:53,217 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:32:53,218 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:32:53,219 - INFO - Google authentication successful
2025-07-23 18:32:54,613 - INFO - Found existing folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 18:32:55,087 - INFO - Found existing folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 18:32:55,091 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 18:32:55,092 - INFO - Syncing folder: Root (214 notes)
2025-07-23 18:32:55,092 - INFO - Syncing note: Kafka.md
2025-07-23 18:32:55,589 - INFO - Found existing document: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:32:55,589 - INFO - Document Kafka already exists, updating content...
2025-07-23 18:36:25,427 - INFO - Found embedded image: test_image1.png
2025-07-23 18:36:25,427 - INFO - Found embedded image: test_image2.jpg
2025-07-23 18:41:11,599 - INFO - Found embedded image: test_image1.png
2025-07-23 18:41:11,599 - INFO - Found embedded image: test_image2.jpg
2025-07-23 18:41:51,191 - INFO - Found embedded image: test_image1.png
2025-07-23 18:41:51,192 - INFO - Found embedded image: test_image2.jpg
2025-07-23 18:42:35,656 - INFO - Found embedded image: test_image1.png
2025-07-23 18:42:35,656 - INFO - Found embedded image: test_image2.jpg
2025-07-23 18:43:12,272 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 18:43:12,281 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:43:12,284 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:43:12,287 - INFO - Google authentication successful
2025-07-23 18:43:13,049 - INFO - Found existing folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 18:43:13,553 - INFO - Found existing folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 18:43:13,554 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 18:43:13,555 - INFO - Syncing folder: Root (214 notes)
2025-07-23 18:43:13,555 - INFO - Syncing note: Kafka.md
2025-07-23 18:43:14,096 - INFO - Found existing document: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:43:14,096 - INFO - Document Kafka already exists, updating content...
2025-07-23 18:43:15,738 - INFO - Cleared content from document: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0
2025-07-23 18:43:15,738 - INFO - Using existing Google Doc: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:43:15,738 - INFO - Applying 14 formatting requests...
2025-07-23 18:43:15,739 - INFO - Validated 14/14 requests
2025-07-23 18:43:15,739 - INFO - Validated 14/14 requests
2025-07-23 18:43:16,333 - INFO - Successfully applied formatting to document: Kafka
2025-07-23 18:43:16,333 - INFO - Successfully processed Google Doc: Kafka
2025-07-23 18:43:16,334 - INFO - Syncing note: Bài toán liệt kê.md
2025-07-23 18:43:16,830 - INFO - Found existing document: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 18:43:16,830 - INFO - Document Bài toán liệt kê already exists, updating content...
2025-07-23 18:43:18,731 - INFO - Cleared content from document: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4
2025-07-23 18:43:18,733 - INFO - Using existing Google Doc: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 18:43:18,733 - INFO - Applying 89 formatting requests...
2025-07-23 18:43:18,738 - INFO - Validated 89/89 requests
2025-07-23 18:43:18,738 - INFO - Validated 89/89 requests
2025-07-23 18:43:19,416 - INFO - Successfully applied formatting to document: Bài toán liệt kê
2025-07-23 18:43:19,417 - INFO - Successfully processed Google Doc: Bài toán liệt kê
2025-07-23 18:43:19,417 - INFO - Syncing note: Vue - Nuxt.md
2025-07-23 18:43:19,909 - INFO - Found existing document: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 18:43:19,910 - INFO - Document Vue - Nuxt already exists, updating content...
2025-07-23 18:43:21,824 - INFO - Cleared content from document: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8
2025-07-23 18:43:21,830 - INFO - Using existing Google Doc: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 18:43:21,830 - INFO - Applying 311 formatting requests...
2025-07-23 18:43:21,866 - INFO - Validated 311/311 requests
2025-07-23 18:43:21,866 - INFO - Validated 311/311 requests
2025-07-23 18:43:22,904 - INFO - Successfully applied formatting to document: Vue - Nuxt
2025-07-23 18:43:22,904 - INFO - Successfully processed Google Doc: Vue - Nuxt
2025-07-23 18:43:22,905 - INFO - Syncing note: Kudofoto.md
2025-07-23 18:43:23,398 - INFO - Found existing document: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 18:43:23,398 - INFO - Document Kudofoto already exists, updating content...
2025-07-23 18:43:24,754 - INFO - Cleared content from document: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs
2025-07-23 18:43:24,755 - INFO - Using existing Google Doc: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 18:43:24,755 - INFO - Applying 3 formatting requests...
2025-07-23 18:43:24,755 - INFO - Validated 3/3 requests
2025-07-23 18:43:24,755 - INFO - Validated 3/3 requests
2025-07-23 18:43:25,299 - INFO - Successfully applied formatting to document: Kudofoto
2025-07-23 18:43:25,299 - INFO - Successfully processed Google Doc: Kudofoto
2025-07-23 18:43:25,299 - INFO - Syncing note: Solutions & System Designs & Design Patterns.md
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 18:43:25,301 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Untitled 3.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:43:25,302 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 18:43:25,302 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:43:25,302 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 18:43:25,846 - INFO - Found existing document: Solutions & System Designs & Design Patterns (ID: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0)
2025-07-23 18:43:25,846 - INFO - Document Solutions & System Designs & Design Patterns already exists, updating content...
2025-07-23 18:43:29,664 - INFO - Cleared content from document: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0
2025-07-23 18:43:29,670 - INFO - Using existing Google Doc: Solutions & System Designs & Design Patterns (ID: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0)
2025-07-23 18:43:29,671 - INFO - Applying 1006 formatting requests...
2025-07-23 18:43:29,754 - INFO - Validated 1006/1006 requests
2025-07-23 18:43:29,754 - INFO - Validated 1006/1006 requests
2025-07-23 18:43:32,866 - INFO - Successfully applied formatting to document: Solutions & System Designs & Design Patterns
2025-07-23 18:43:32,866 - INFO - Successfully processed Google Doc: Solutions & System Designs & Design Patterns
2025-07-23 18:43:33,061 - WARNING - Could not find placeholder for image: Pasted image 20241012194316.png
2025-07-23 18:43:33,089 - WARNING - Could not find placeholder for image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:43:33,089 - WARNING - Could not find placeholder for image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:43:33,089 - WARNING - Could not find placeholder for image: Pasted image 20240927232457.png
2025-07-23 18:43:33,089 - WARNING - Could not find placeholder for image: Pasted image 20240903223244.png
2025-07-23 18:43:33,090 - WARNING - Could not find placeholder for image: Pasted image 20240903230303.png
2025-07-23 18:43:33,090 - WARNING - Could not find placeholder for image: Pasted image 20240903230309.png
2025-07-23 18:43:33,090 - WARNING - Could not find placeholder for image: Pasted image 20240425163824.png
2025-07-23 18:43:33,090 - WARNING - Could not find placeholder for image: Pasted image 20240425163928.png
2025-07-23 18:43:33,090 - WARNING - Could not find placeholder for image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:43:33,090 - WARNING - Could not find placeholder for image: Pasted image 20240904085651.png
2025-07-23 18:43:33,095 - INFO - Recreating document with placeholders removed...
2025-07-23 18:43:34,776 - INFO - Validated 1001/1001 requests
2025-07-23 18:43:35,704 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[14].insertText: Index 827 must be less than the end index of the referenced segment, 807.". Details: "Invalid requests[14].insertText: Index 827 must be less than the end index of the referenced segment, 807.">
2025-07-23 18:43:35,707 - INFO - Processing image: Untitled 3.png at position 12607
2025-07-23 18:43:36,172 - INFO - Found existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 18:43:36,172 - INFO - File Untitled 3.png already exists, updating...
2025-07-23 18:43:41,281 - INFO - Updated existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 18:43:44,196 - ERROR - Error inserting image Untitled 3.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 12607 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 12607 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:43:44,196 - ERROR - Failed to insert image: Untitled 3.png
2025-07-23 18:43:44,196 - INFO - Processing image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg at position 10319
2025-07-23 18:43:44,677 - INFO - Found existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 18:43:44,677 - INFO - File 390dd032e50c3364eec22e71a19b2113_MD5.jpg already exists, updating...
2025-07-23 18:43:49,637 - INFO - Updated existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 18:43:52,562 - ERROR - Error inserting image 390dd032e50c3364eec22e71a19b2113_MD5.jpg: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 10319 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 10319 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:43:52,562 - ERROR - Failed to insert image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:43:52,563 - INFO - Processing image: Pasted image 20240904085651.png at position 10184
2025-07-23 18:43:53,067 - INFO - Found existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 18:43:53,067 - INFO - File Pasted image 20240904085651.png already exists, updating...
2025-07-23 18:43:57,723 - INFO - Updated existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 18:44:00,276 - ERROR - Error inserting image Pasted image 20240904085651.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 10184 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 10184 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:00,276 - ERROR - Failed to insert image: Pasted image 20240904085651.png
2025-07-23 18:44:00,276 - INFO - Processing image: Pasted image 20240903223244.png at position 9815
2025-07-23 18:44:00,755 - INFO - Found existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 18:44:00,755 - INFO - File Pasted image 20240903223244.png already exists, updating...
2025-07-23 18:44:04,710 - INFO - Updated existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 18:44:07,365 - ERROR - Error inserting image Pasted image 20240903223244.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 9815 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 9815 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:07,365 - ERROR - Failed to insert image: Pasted image 20240903223244.png
2025-07-23 18:44:07,365 - INFO - Processing image: Pasted image 20240903230309.png at position 9078
2025-07-23 18:44:07,820 - INFO - Found existing file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 18:44:07,820 - INFO - File Pasted image 20240903230309.png already exists, updating...
2025-07-23 18:44:12,560 - INFO - Updated existing file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 18:44:15,153 - ERROR - Error inserting image Pasted image 20240903230309.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 9078 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 9078 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:15,154 - ERROR - Failed to insert image: Pasted image 20240903230309.png
2025-07-23 18:44:15,154 - INFO - Processing image: Pasted image 20240903230303.png at position 9056
2025-07-23 18:44:15,657 - INFO - Found existing file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 18:44:15,657 - INFO - File Pasted image 20240903230303.png already exists, updating...
2025-07-23 18:44:21,631 - INFO - Updated existing file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 18:44:24,705 - ERROR - Error inserting image Pasted image 20240903230303.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 9056 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 9056 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:24,706 - ERROR - Failed to insert image: Pasted image 20240903230303.png
2025-07-23 18:44:24,706 - INFO - Processing image: bddf3546-c720-4313-9046-36d8c4a97019.png at position 8690
2025-07-23 18:44:25,190 - INFO - Found existing file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 18:44:25,190 - INFO - File bddf3546-c720-4313-9046-36d8c4a97019.png already exists, updating...
2025-07-23 18:44:29,974 - INFO - Updated existing file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 18:44:32,959 - ERROR - Error inserting image bddf3546-c720-4313-9046-36d8c4a97019.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 8690 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 8690 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:32,960 - ERROR - Failed to insert image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:44:32,960 - INFO - Processing image: Pasted image 20240927232457.png at position 8144
2025-07-23 18:44:33,368 - INFO - Found existing file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 18:44:33,368 - INFO - File Pasted image 20240927232457.png already exists, updating...
2025-07-23 18:44:37,354 - INFO - Updated existing file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 18:44:39,595 - ERROR - Error inserting image Pasted image 20240927232457.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 8144 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 8144 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:39,595 - ERROR - Failed to insert image: Pasted image 20240927232457.png
2025-07-23 18:44:39,595 - INFO - Processing image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg at position 7460
2025-07-23 18:44:39,981 - INFO - Found existing file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 18:44:39,981 - INFO - File telegram-cloud-photo-size-5-6311899726957623527-y.jpg already exists, updating...
2025-07-23 18:44:45,604 - INFO - Updated existing file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 18:44:47,871 - ERROR - Error inserting image telegram-cloud-photo-size-5-6311899726957623527-y.jpg: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 7460 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 7460 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:47,872 - ERROR - Failed to insert image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:44:47,872 - INFO - Processing image: Pasted image 20240425163928.png at position 3048
2025-07-23 18:44:48,352 - INFO - Found existing file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 18:44:48,352 - INFO - File Pasted image 20240425163928.png already exists, updating...
2025-07-23 18:44:53,025 - INFO - Updated existing file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 18:44:55,538 - ERROR - Error inserting image Pasted image 20240425163928.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 3048 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 3048 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:44:55,538 - ERROR - Failed to insert image: Pasted image 20240425163928.png
2025-07-23 18:44:55,539 - INFO - Processing image: Pasted image 20240425163824.png at position 2586
2025-07-23 18:44:55,965 - INFO - Found existing file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 18:44:55,965 - INFO - File Pasted image 20240425163824.png already exists, updating...
2025-07-23 18:45:00,081 - INFO - Updated existing file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 18:45:02,632 - ERROR - Error inserting image Pasted image 20240425163824.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 2586 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 2586 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:45:02,632 - ERROR - Failed to insert image: Pasted image 20240425163824.png
2025-07-23 18:45:02,633 - INFO - Processing image: Pasted image 20241012194316.png at position 805
2025-07-23 18:45:03,199 - INFO - Found existing file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 18:45:03,199 - INFO - File Pasted image 20241012194316.png already exists, updating...
2025-07-23 18:45:08,152 - INFO - Updated existing file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 18:45:11,170 - ERROR - Error inserting image Pasted image 20241012194316.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 805 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 805 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:45:11,171 - ERROR - Failed to insert image: Pasted image 20241012194316.png
2025-07-23 18:45:11,172 - INFO - Syncing note: Domain knowledge.md
2025-07-23 18:45:11,621 - INFO - Found existing document: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 18:45:11,621 - INFO - Document Domain knowledge already exists, updating content...
2025-07-23 18:45:12,828 - INFO - Cleared content from document: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA
2025-07-23 18:45:12,828 - INFO - Using existing Google Doc: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 18:45:12,828 - INFO - Applying 7 formatting requests...
2025-07-23 18:45:12,828 - INFO - Validated 7/7 requests
2025-07-23 18:45:12,829 - INFO - Validated 7/7 requests
2025-07-23 18:45:13,806 - INFO - Successfully applied formatting to document: Domain knowledge
2025-07-23 18:45:13,806 - INFO - Successfully processed Google Doc: Domain knowledge
2025-07-23 18:45:13,806 - INFO - Syncing note: VPN - Proxy - Firewall.md
2025-07-23 18:45:14,275 - INFO - Found existing document: VPN - Proxy - Firewall (ID: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc)
2025-07-23 18:45:14,275 - INFO - Document VPN - Proxy - Firewall already exists, updating content...
2025-07-23 18:45:15,508 - INFO - Cleared content from document: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc
2025-07-23 18:45:15,509 - INFO - Using existing Google Doc: VPN - Proxy - Firewall (ID: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc)
2025-07-23 18:45:15,509 - INFO - Applying 21 formatting requests...
2025-07-23 18:45:15,510 - INFO - Validated 21/21 requests
2025-07-23 18:45:15,510 - INFO - Validated 21/21 requests
2025-07-23 18:45:16,168 - INFO - Successfully applied formatting to document: VPN - Proxy - Firewall
2025-07-23 18:45:16,168 - INFO - Successfully processed Google Doc: VPN - Proxy - Firewall
2025-07-23 18:45:16,168 - INFO - Syncing note: Cách làm sạch và bảo quản boots.md
2025-07-23 18:45:16,170 - INFO - Found embedded image: Untitled 16.png
2025-07-23 18:45:16,170 - INFO - Found embedded image: Untitled 1 9.png
2025-07-23 18:45:16,170 - INFO - Found embedded image: Untitled 2 6.png
2025-07-23 18:45:16,648 - INFO - Found existing document: Cách làm sạch và bảo quản boots (ID: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI)
2025-07-23 18:45:16,649 - INFO - Document Cách làm sạch và bảo quản boots already exists, updating content...
2025-07-23 18:45:18,034 - INFO - Cleared content from document: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI
2025-07-23 18:45:18,034 - INFO - Using existing Google Doc: Cách làm sạch và bảo quản boots (ID: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI)
2025-07-23 18:45:18,034 - INFO - Applying 24 formatting requests...
2025-07-23 18:45:18,039 - INFO - Validated 24/24 requests
2025-07-23 18:45:18,039 - INFO - Validated 24/24 requests
2025-07-23 18:45:18,860 - INFO - Successfully applied formatting to document: Cách làm sạch và bảo quản boots
2025-07-23 18:45:18,861 - INFO - Successfully processed Google Doc: Cách làm sạch và bảo quản boots
2025-07-23 18:45:18,863 - INFO - Recreating document with placeholders removed...
2025-07-23 18:45:19,548 - INFO - Validated 21/21 requests
2025-07-23 18:45:19,860 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI:batchUpdate?alt=json returned "Invalid requests[4].insertText: Index 567 must be less than the end index of the referenced segment, 546.". Details: "Invalid requests[4].insertText: Index 567 must be less than the end index of the referenced segment, 546.">
2025-07-23 18:45:19,860 - INFO - Processing image: Untitled 2 6.png at position 1303
2025-07-23 18:45:20,332 - INFO - Found existing file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 18:45:20,332 - INFO - File Untitled 2 6.png already exists, updating...
2025-07-23 18:45:24,120 - INFO - Updated existing file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 18:45:26,262 - ERROR - Error inserting image Untitled 2 6.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 1303 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 1303 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:45:26,262 - ERROR - Failed to insert image: Untitled 2 6.png
2025-07-23 18:45:26,262 - INFO - Processing image: Untitled 1 9.png at position 991
2025-07-23 18:45:26,723 - INFO - Found existing file: Untitled 1 9.png (ID: 1ERmvjwsTJgxP5FThseM_0k5fUnev1XBF)
2025-07-23 18:45:26,723 - INFO - File Untitled 1 9.png already exists, updating...
2025-07-23 18:45:30,719 - INFO - Updated existing file: Untitled 1 9.png (ID: 1ERmvjwsTJgxP5FThseM_0k5fUnev1XBF)
2025-07-23 18:45:32,643 - ERROR - Error inserting image Untitled 1 9.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 991 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 991 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:45:32,643 - ERROR - Failed to insert image: Untitled 1 9.png
2025-07-23 18:45:32,644 - INFO - Processing image: Untitled 16.png at position 545
2025-07-23 18:45:33,142 - INFO - Found existing file: Untitled 16.png (ID: 1CLRtvemBdWXXHAB5ANbyDbNHMjMQVYce)
2025-07-23 18:45:33,142 - INFO - File Untitled 16.png already exists, updating...
2025-07-23 18:45:37,837 - INFO - Updated existing file: Untitled 16.png (ID: 1CLRtvemBdWXXHAB5ANbyDbNHMjMQVYce)
2025-07-23 18:45:39,766 - ERROR - Error inserting image Untitled 16.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 545 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 545 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:45:39,766 - ERROR - Failed to insert image: Untitled 16.png
2025-07-23 18:45:39,767 - INFO - Syncing note: English.md
2025-07-23 18:45:39,768 - INFO - Found embedded image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png
2025-07-23 18:45:40,270 - INFO - Found existing document: English (ID: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ)
2025-07-23 18:45:40,270 - INFO - Document English already exists, updating content...
2025-07-23 18:45:41,491 - INFO - Cleared content from document: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ
2025-07-23 18:45:41,492 - INFO - Using existing Google Doc: English (ID: 1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ)
2025-07-23 18:45:41,492 - INFO - Applying 27 formatting requests...
2025-07-23 18:45:41,493 - INFO - Validated 27/27 requests
2025-07-23 18:45:41,493 - INFO - Validated 27/27 requests
2025-07-23 18:45:42,313 - INFO - Successfully applied formatting to document: English
2025-07-23 18:45:42,313 - INFO - Successfully processed Google Doc: English
2025-07-23 18:45:42,313 - INFO - Recreating document with placeholders removed...
2025-07-23 18:45:43,074 - INFO - Validated 27/27 requests
2025-07-23 18:45:43,447 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ:batchUpdate?alt=json returned "Invalid requests[7].insertText: Index 100 must be less than the end index of the referenced segment, 80.". Details: "Invalid requests[7].insertText: Index 100 must be less than the end index of the referenced segment, 80.">
2025-07-23 18:45:43,449 - INFO - Processing image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png at position 78
2025-07-23 18:46:08,484 - INFO - Found existing file: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png (ID: 1G_V_NGVX1-jY70yrOZr5Zf6uRhm6UTax)
2025-07-23 18:46:08,484 - INFO - File 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png already exists, updating...
2025-07-23 18:46:12,966 - INFO - Updated existing file: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png (ID: 1G_V_NGVX1-jY70yrOZr5Zf6uRhm6UTax)
2025-07-23 18:46:15,777 - ERROR - Error inserting image 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1Qc9r_WJsNzjhf0u7u6ZgRJX3ceTztZTrOAwKuDy4tMQ:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 78 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 78 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:46:15,778 - ERROR - Failed to insert image: 33f0d4c396b753ed8bac6ee96e7a621f_MD5.png
2025-07-23 18:46:15,778 - INFO - Syncing note: Kotlin.md
2025-07-23 18:46:16,232 - INFO - Found existing document: Kotlin (ID: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0)
2025-07-23 18:46:16,233 - INFO - Document Kotlin already exists, updating content...
2025-07-23 18:46:18,028 - INFO - Cleared content from document: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0
2025-07-23 18:46:18,029 - INFO - Using existing Google Doc: Kotlin (ID: 10YdOHGuB6Rub8_k-0M8b_UsoXzwj4k7zaerUb1avbX0)
2025-07-23 18:46:18,029 - INFO - Applying 95 formatting requests...
2025-07-23 18:46:18,037 - INFO - Validated 95/95 requests
2025-07-23 18:46:18,037 - INFO - Validated 95/95 requests
2025-07-23 18:46:18,657 - INFO - Successfully applied formatting to document: Kotlin
2025-07-23 18:46:18,658 - INFO - Successfully processed Google Doc: Kotlin
2025-07-23 18:46:18,658 - INFO - Syncing note: Golang.md
2025-07-23 18:46:18,659 - INFO - Found embedded image: Basic Golang Project.jpg
2025-07-23 18:46:19,212 - INFO - Found existing document: Golang (ID: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ)
2025-07-23 18:46:19,213 - INFO - Document Golang already exists, updating content...
2025-07-23 18:46:22,379 - INFO - Cleared content from document: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ
2025-07-23 18:46:22,389 - INFO - Using existing Google Doc: Golang (ID: 1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ)
2025-07-23 18:46:22,389 - INFO - Applying 534 formatting requests...
2025-07-23 18:46:22,414 - INFO - Validated 534/534 requests
2025-07-23 18:46:22,414 - INFO - Validated 534/534 requests
2025-07-23 18:46:24,063 - INFO - Successfully applied formatting to document: Golang
2025-07-23 18:46:24,063 - INFO - Successfully processed Google Doc: Golang
2025-07-23 18:46:24,066 - INFO - Recreating document with placeholders removed...
2025-07-23 18:46:25,106 - INFO - Validated 534/534 requests
2025-07-23 18:46:25,804 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ:batchUpdate?alt=json returned "Invalid requests[9].insertText: Index 164 must be less than the end index of the referenced segment, 144.". Details: "Invalid requests[9].insertText: Index 164 must be less than the end index of the referenced segment, 144.">
2025-07-23 18:46:25,807 - INFO - Processing image: Basic Golang Project.jpg at position 142
2025-07-23 18:46:26,287 - INFO - Found existing file: Basic Golang Project.jpg (ID: 15S4lbWCP74oN2upTxkF7HdfzPsUZV8BV)
2025-07-23 18:46:26,287 - INFO - File Basic Golang Project.jpg already exists, updating...
2025-07-23 18:46:30,448 - INFO - Updated existing file: Basic Golang Project.jpg (ID: 15S4lbWCP74oN2upTxkF7HdfzPsUZV8BV)
2025-07-23 18:46:33,232 - ERROR - Error inserting image Basic Golang Project.jpg: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1KYmV_TUuazmrcY8v-C2-eh0A3yLDeeqoYjQexUkPIlQ:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 142 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 142 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:46:33,233 - ERROR - Failed to insert image: Basic Golang Project.jpg
2025-07-23 18:46:33,235 - INFO - Syncing note: Fresher Java Interview.md
2025-07-23 18:46:33,748 - INFO - Found existing document: Fresher Java Interview (ID: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I)
2025-07-23 18:46:33,748 - INFO - Document Fresher Java Interview already exists, updating content...
2025-07-23 18:46:35,457 - INFO - Cleared content from document: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I
2025-07-23 18:46:35,459 - INFO - Using existing Google Doc: Fresher Java Interview (ID: 1fZDNUDnifAG04XkLX9b6B08NsjEnnd5e0nWSVLBkp1I)
2025-07-23 18:46:35,459 - INFO - Applying 143 formatting requests...
2025-07-23 18:46:35,474 - INFO - Validated 143/143 requests
2025-07-23 18:46:35,474 - INFO - Validated 143/143 requests
2025-07-23 18:46:36,274 - INFO - Successfully applied formatting to document: Fresher Java Interview
2025-07-23 18:46:36,274 - INFO - Successfully processed Google Doc: Fresher Java Interview
2025-07-23 18:46:36,275 - INFO - Syncing note: Blockchain.md
2025-07-23 18:46:36,764 - INFO - Found existing document: Blockchain (ID: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8)
2025-07-23 18:46:36,764 - INFO - Document Blockchain already exists, updating content...
2025-07-23 18:46:38,111 - INFO - Cleared content from document: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8
2025-07-23 18:46:38,111 - INFO - Using existing Google Doc: Blockchain (ID: 1_PhUtiMM40Shw1K2G7lJ0Q5Zr2yfE49EUn45BszzOB8)
2025-07-23 18:46:38,111 - INFO - Applying 5 formatting requests...
2025-07-23 18:46:38,111 - INFO - Validated 5/5 requests
2025-07-23 18:46:38,111 - INFO - Validated 5/5 requests
2025-07-23 18:46:38,967 - INFO - Successfully applied formatting to document: Blockchain
2025-07-23 18:46:38,968 - INFO - Successfully processed Google Doc: Blockchain
2025-07-23 18:46:38,968 - INFO - Syncing note: No-code - nocode - low-code - lowcode.md
2025-07-23 18:46:38,970 - INFO - Found embedded image: 1675871539203.png
2025-07-23 18:46:39,405 - INFO - Found existing document: No-code - nocode - low-code - lowcode (ID: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y)
2025-07-23 18:46:39,405 - INFO - Document No-code - nocode - low-code - lowcode already exists, updating content...
2025-07-23 18:46:40,650 - INFO - Cleared content from document: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y
2025-07-23 18:46:40,650 - INFO - Using existing Google Doc: No-code - nocode - low-code - lowcode (ID: 1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y)
2025-07-23 18:46:40,650 - INFO - Applying 8 formatting requests...
2025-07-23 18:46:40,651 - INFO - Validated 8/8 requests
2025-07-23 18:46:40,651 - INFO - Validated 8/8 requests
2025-07-23 18:46:41,298 - INFO - Successfully applied formatting to document: No-code - nocode - low-code - lowcode
2025-07-23 18:46:41,299 - INFO - Successfully processed Google Doc: No-code - nocode - low-code - lowcode
2025-07-23 18:46:41,299 - INFO - Recreating document with placeholders removed...
2025-07-23 18:46:42,143 - INFO - Validated 8/8 requests
2025-07-23 18:46:42,563 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y:batchUpdate?alt=json returned "Invalid requests[4].insertText: Index 71 must be less than the end index of the referenced segment, 51.". Details: "Invalid requests[4].insertText: Index 71 must be less than the end index of the referenced segment, 51.">
2025-07-23 18:46:42,564 - INFO - Processing image: 1675871539203.png at position 49
2025-07-23 18:46:43,047 - INFO - Found existing file: 1675871539203.png (ID: 1HrngqADYngMmqsbVgiL4wkU9DOLypCNv)
2025-07-23 18:46:43,047 - INFO - File 1675871539203.png already exists, updating...
2025-07-23 18:46:47,623 - INFO - Updated existing file: 1675871539203.png (ID: 1HrngqADYngMmqsbVgiL4wkU9DOLypCNv)
2025-07-23 18:46:49,423 - ERROR - Error inserting image 1675871539203.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1UD_PznJD8UNON-HN7a_wu1UDzhAuZk6eabHQoF9fl5Y:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.". Details: "Invalid requests[0].insertInlineImage: There was a problem retrieving the image. The provided image should be publicly accessible, within size limit, and in supported formats.">
2025-07-23 18:46:49,424 - ERROR - Failed to insert image: 1675871539203.png
2025-07-23 18:46:49,424 - INFO - Syncing note: React - Next.md
2025-07-23 18:46:49,935 - INFO - Found existing document: React - Next (ID: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8)
2025-07-23 18:46:49,935 - INFO - Document React - Next already exists, updating content...
2025-07-23 18:46:52,009 - INFO - Cleared content from document: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8
2025-07-23 18:46:52,010 - INFO - Using existing Google Doc: React - Next (ID: 1ySgDP0LGA9GS1ntkU0c51yjjDw9CsUQ0PO10X8YOIO8)
2025-07-23 18:46:52,010 - INFO - Applying 437 formatting requests...
2025-07-23 18:46:52,020 - INFO - Validated 437/437 requests
2025-07-23 18:46:52,021 - INFO - Validated 437/437 requests
2025-07-23 18:46:53,109 - INFO - Successfully applied formatting to document: React - Next
2025-07-23 18:46:53,109 - INFO - Successfully processed Google Doc: React - Next
2025-07-23 18:46:53,110 - INFO - Syncing note: SAP - Systems, Applications, and Products.md
2025-07-23 18:46:53,594 - INFO - Found existing document: SAP - Systems, Applications, and Products (ID: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU)
2025-07-23 18:46:53,594 - INFO - Document SAP - Systems, Applications, and Products already exists, updating content...
2025-07-23 18:46:54,827 - INFO - Cleared content from document: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU
2025-07-23 18:46:54,827 - INFO - Using existing Google Doc: SAP - Systems, Applications, and Products (ID: 1oBhJwy8hZFyKO8voXmztMxf0qzPLxzvBONi5n-CpFjU)
2025-07-23 18:46:54,828 - INFO - Applying 32 formatting requests...
2025-07-23 18:46:54,832 - INFO - Validated 32/32 requests
2025-07-23 18:46:54,833 - INFO - Validated 32/32 requests
2025-07-23 18:46:55,499 - INFO - Successfully applied formatting to document: SAP - Systems, Applications, and Products
2025-07-23 18:46:55,499 - INFO - Successfully processed Google Doc: SAP - Systems, Applications, and Products
2025-07-23 18:46:55,500 - INFO - Syncing note: Airblade - AB - Air blade.md
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502172424.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502173135.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502175000.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502175940.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502180730.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502193802.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502195106.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502195226.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240502203227.png
2025-07-23 18:46:55,501 - INFO - Found embedded image: Pasted image 20240505160601.png
2025-07-23 18:46:55,895 - INFO - Found existing document: Airblade - AB - Air blade (ID: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg)
2025-07-23 18:46:55,895 - INFO - Document Airblade - AB - Air blade already exists, updating content...
2025-07-23 18:46:57,172 - INFO - Cleared content from document: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg
2025-07-23 18:46:57,173 - INFO - Using existing Google Doc: Airblade - AB - Air blade (ID: 1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg)
2025-07-23 18:46:57,173 - INFO - Applying 22 formatting requests...
2025-07-23 18:46:57,173 - INFO - Validated 22/22 requests
2025-07-23 18:46:57,173 - INFO - Validated 22/22 requests
2025-07-23 18:46:57,802 - INFO - Successfully applied formatting to document: Airblade - AB - Air blade
2025-07-23 18:46:57,802 - INFO - Successfully processed Google Doc: Airblade - AB - Air blade
2025-07-23 18:46:57,803 - INFO - Recreating document with placeholders removed...
2025-07-23 18:46:58,609 - INFO - Validated 12/12 requests
2025-07-23 18:46:59,027 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[6].insertText: Index 293 must be less than the end index of the referenced segment, 74.". Details: "Invalid requests[6].insertText: Index 293 must be less than the end index of the referenced segment, 74.">
2025-07-23 18:46:59,028 - INFO - Processing image: Pasted image 20240505160601.png at position 271
2025-07-23 18:46:59,540 - INFO - Found existing file: Pasted image 20240505160601.png (ID: 19vHqQRDhIXQnafrFsSIdtF5v-8Tx0oMu)
2025-07-23 18:46:59,540 - INFO - File Pasted image 20240505160601.png already exists, updating...
2025-07-23 18:47:03,847 - INFO - Updated existing file: Pasted image 20240505160601.png (ID: 19vHqQRDhIXQnafrFsSIdtF5v-8Tx0oMu)
2025-07-23 18:47:06,353 - ERROR - Error inserting image Pasted image 20240505160601.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 271 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 271 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:06,353 - ERROR - Failed to insert image: Pasted image 20240505160601.png
2025-07-23 18:47:06,353 - INFO - Processing image: Pasted image 20240502203227.png at position 249
2025-07-23 18:47:06,808 - INFO - Found existing file: Pasted image 20240502203227.png (ID: 184yBiOBwiC1BEjPcUZUBkLAbxDRWF6Dx)
2025-07-23 18:47:06,809 - INFO - File Pasted image 20240502203227.png already exists, updating...
2025-07-23 18:47:11,226 - INFO - Updated existing file: Pasted image 20240502203227.png (ID: 184yBiOBwiC1BEjPcUZUBkLAbxDRWF6Dx)
2025-07-23 18:47:14,025 - ERROR - Error inserting image Pasted image 20240502203227.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 249 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 249 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:14,026 - ERROR - Failed to insert image: Pasted image 20240502203227.png
2025-07-23 18:47:14,026 - INFO - Processing image: Pasted image 20240502195226.png at position 227
2025-07-23 18:47:14,473 - INFO - Found existing file: Pasted image 20240502195226.png (ID: 1AfS3bqZ8UZ2tAw3CtDnazyHrVJ8rDUCO)
2025-07-23 18:47:14,473 - INFO - File Pasted image 20240502195226.png already exists, updating...
2025-07-23 18:47:19,080 - INFO - Updated existing file: Pasted image 20240502195226.png (ID: 1AfS3bqZ8UZ2tAw3CtDnazyHrVJ8rDUCO)
2025-07-23 18:47:21,229 - ERROR - Error inserting image Pasted image 20240502195226.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 227 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 227 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:21,229 - ERROR - Failed to insert image: Pasted image 20240502195226.png
2025-07-23 18:47:21,229 - INFO - Processing image: Pasted image 20240502195106.png at position 205
2025-07-23 18:47:21,637 - INFO - Found existing file: Pasted image 20240502195106.png (ID: 1wCOr0N3Ccg733qGpvFOga0s4QxJFIvNd)
2025-07-23 18:47:21,638 - INFO - File Pasted image 20240502195106.png already exists, updating...
2025-07-23 18:47:25,918 - INFO - Updated existing file: Pasted image 20240502195106.png (ID: 1wCOr0N3Ccg733qGpvFOga0s4QxJFIvNd)
2025-07-23 18:47:28,769 - ERROR - Error inserting image Pasted image 20240502195106.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 205 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 205 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:28,769 - ERROR - Failed to insert image: Pasted image 20240502195106.png
2025-07-23 18:47:28,769 - INFO - Processing image: Pasted image 20240502193802.png at position 183
2025-07-23 18:47:29,213 - INFO - Found existing file: Pasted image 20240502193802.png (ID: 1vHljhifrKfzEfVhZGhLPMG8I83-h1fOq)
2025-07-23 18:47:29,213 - INFO - File Pasted image 20240502193802.png already exists, updating...
2025-07-23 18:47:33,803 - INFO - Updated existing file: Pasted image 20240502193802.png (ID: 1vHljhifrKfzEfVhZGhLPMG8I83-h1fOq)
2025-07-23 18:47:35,863 - ERROR - Error inserting image Pasted image 20240502193802.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 183 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 183 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:35,863 - ERROR - Failed to insert image: Pasted image 20240502193802.png
2025-07-23 18:47:35,863 - INFO - Processing image: Pasted image 20240502180730.png at position 161
2025-07-23 18:47:36,399 - INFO - Found existing file: Pasted image 20240502180730.png (ID: 1480xkeA-YEMkHs-xceHyzWl8tt6NsGmR)
2025-07-23 18:47:36,399 - INFO - File Pasted image 20240502180730.png already exists, updating...
2025-07-23 18:47:40,640 - INFO - Updated existing file: Pasted image 20240502180730.png (ID: 1480xkeA-YEMkHs-xceHyzWl8tt6NsGmR)
2025-07-23 18:47:42,997 - ERROR - Error inserting image Pasted image 20240502180730.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 161 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 161 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:42,997 - ERROR - Failed to insert image: Pasted image 20240502180730.png
2025-07-23 18:47:42,997 - INFO - Processing image: Pasted image 20240502175940.png at position 139
2025-07-23 18:47:43,436 - INFO - Found existing file: Pasted image 20240502175940.png (ID: 1Z6jpvse7ChAJiT6-lK2oHXJY_QQ3Ok1b)
2025-07-23 18:47:43,436 - INFO - File Pasted image 20240502175940.png already exists, updating...
2025-07-23 18:47:49,892 - INFO - Updated existing file: Pasted image 20240502175940.png (ID: 1Z6jpvse7ChAJiT6-lK2oHXJY_QQ3Ok1b)
2025-07-23 18:47:52,218 - ERROR - Error inserting image Pasted image 20240502175940.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 139 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 139 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:47:52,219 - ERROR - Failed to insert image: Pasted image 20240502175940.png
2025-07-23 18:47:52,219 - INFO - Processing image: Pasted image 20240502175000.png at position 117
2025-07-23 18:47:52,653 - INFO - Found existing file: Pasted image 20240502175000.png (ID: 1-h1sSlqTb8oNdK8mi_wevHLnvPfyVNrM)
2025-07-23 18:47:52,653 - INFO - File Pasted image 20240502175000.png already exists, updating...
2025-07-23 18:47:58,458 - INFO - Updated existing file: Pasted image 20240502175000.png (ID: 1-h1sSlqTb8oNdK8mi_wevHLnvPfyVNrM)
2025-07-23 18:48:00,846 - ERROR - Error inserting image Pasted image 20240502175000.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 117 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 117 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:00,846 - ERROR - Failed to insert image: Pasted image 20240502175000.png
2025-07-23 18:48:00,846 - INFO - Processing image: Pasted image 20240502173135.png at position 95
2025-07-23 18:48:01,289 - INFO - Found existing file: Pasted image 20240502173135.png (ID: 1Hu600W9W1IMUmk0jXwPouutZDnPIUBTl)
2025-07-23 18:48:01,289 - INFO - File Pasted image 20240502173135.png already exists, updating...
2025-07-23 18:48:05,597 - INFO - Updated existing file: Pasted image 20240502173135.png (ID: 1Hu600W9W1IMUmk0jXwPouutZDnPIUBTl)
2025-07-23 18:48:07,900 - ERROR - Error inserting image Pasted image 20240502173135.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 95 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 95 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:07,900 - ERROR - Failed to insert image: Pasted image 20240502173135.png
2025-07-23 18:48:07,900 - INFO - Processing image: Pasted image 20240502172424.png at position 73
2025-07-23 18:48:08,380 - INFO - Found existing file: Pasted image 20240502172424.png (ID: 16S3g6HOx_gMZ9Icj_CC-dF5Y2XAvndLU)
2025-07-23 18:48:08,380 - INFO - File Pasted image 20240502172424.png already exists, updating...
2025-07-23 18:48:12,602 - INFO - Updated existing file: Pasted image 20240502172424.png (ID: 16S3g6HOx_gMZ9Icj_CC-dF5Y2XAvndLU)
2025-07-23 18:48:15,177 - ERROR - Error inserting image Pasted image 20240502172424.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1SFdg7Xig0vt1j1O3maJLqrwp2e7iEuNj5ARKo43G8yg:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 73 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 73 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:15,177 - ERROR - Failed to insert image: Pasted image 20240502172424.png
2025-07-23 18:48:15,178 - INFO - Syncing note: Post score algorithm - Trending algorithm.md
2025-07-23 18:48:15,179 - INFO - Found embedded image: Pasted image 20231014200829.png
2025-07-23 18:48:15,179 - INFO - Found embedded image: Pasted image 20231014200850.png
2025-07-23 18:48:15,652 - INFO - Found existing document: Post score algorithm - Trending algorithm (ID: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8)
2025-07-23 18:48:15,652 - INFO - Document Post score algorithm - Trending algorithm already exists, updating content...
2025-07-23 18:48:16,967 - INFO - Cleared content from document: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8
2025-07-23 18:48:16,967 - INFO - Using existing Google Doc: Post score algorithm - Trending algorithm (ID: 1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8)
2025-07-23 18:48:16,967 - INFO - Applying 11 formatting requests...
2025-07-23 18:48:16,967 - INFO - Validated 11/11 requests
2025-07-23 18:48:16,967 - INFO - Validated 11/11 requests
2025-07-23 18:48:17,669 - INFO - Successfully applied formatting to document: Post score algorithm - Trending algorithm
2025-07-23 18:48:17,669 - INFO - Successfully processed Google Doc: Post score algorithm - Trending algorithm
2025-07-23 18:48:17,669 - INFO - Recreating document with placeholders removed...
2025-07-23 18:48:18,509 - INFO - Validated 9/9 requests
2025-07-23 18:48:18,897 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8:batchUpdate?alt=json returned "Invalid requests[5].insertText: Index 90 must be less than the end index of the referenced segment, 47.". Details: "Invalid requests[5].insertText: Index 90 must be less than the end index of the referenced segment, 47.">
2025-07-23 18:48:18,897 - INFO - Processing image: Pasted image 20231014200850.png at position 68
2025-07-23 18:48:19,363 - INFO - Found existing file: Pasted image 20231014200850.png (ID: 1Meqv2ihZPf2jTVvKS1ucSA1fF8wuYa2J)
2025-07-23 18:48:19,363 - INFO - File Pasted image 20231014200850.png already exists, updating...
2025-07-23 18:48:24,012 - INFO - Updated existing file: Pasted image 20231014200850.png (ID: 1Meqv2ihZPf2jTVvKS1ucSA1fF8wuYa2J)
2025-07-23 18:48:25,675 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 18:48:25,678 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:48:25,680 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:48:25,682 - INFO - Google authentication successful
2025-07-23 18:48:26,445 - INFO - Found existing folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 18:48:26,924 - ERROR - Error inserting image Pasted image 20231014200850.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 68 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 68 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:26,924 - ERROR - Failed to insert image: Pasted image 20231014200850.png
2025-07-23 18:48:26,924 - INFO - Processing image: Pasted image 20231014200829.png at position 46
2025-07-23 18:48:27,191 - INFO - Found existing folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 18:48:27,194 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 18:48:27,194 - INFO - Syncing folder: Root (214 notes)
2025-07-23 18:48:27,194 - INFO - Syncing note: Kafka.md
2025-07-23 18:48:27,337 - INFO - Found existing file: Pasted image 20231014200829.png (ID: 1djVOj-7KJ6Jo3e_MWii2dh6clUdOio0s)
2025-07-23 18:48:27,337 - INFO - File Pasted image 20231014200829.png already exists, updating...
2025-07-23 18:48:27,664 - INFO - Found existing document: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:48:27,664 - INFO - Document Kafka already exists, updating content...
2025-07-23 18:48:29,207 - INFO - Cleared content from document: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0
2025-07-23 18:48:29,208 - INFO - Using existing Google Doc: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:48:29,208 - INFO - Applying 14 formatting requests...
2025-07-23 18:48:29,208 - INFO - Validated 14/14 requests
2025-07-23 18:48:29,208 - INFO - Validated 14/14 requests
2025-07-23 18:48:29,768 - INFO - Successfully applied formatting to document: Kafka
2025-07-23 18:48:29,768 - INFO - Successfully processed Google Doc: Kafka
2025-07-23 18:48:29,769 - INFO - Syncing note: Bài toán liệt kê.md
2025-07-23 18:48:30,205 - INFO - Found existing document: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 18:48:30,205 - INFO - Document Bài toán liệt kê already exists, updating content...
2025-07-23 18:48:31,642 - INFO - Cleared content from document: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4
2025-07-23 18:48:31,643 - INFO - Using existing Google Doc: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 18:48:31,643 - INFO - Applying 89 formatting requests...
2025-07-23 18:48:31,645 - INFO - Validated 89/89 requests
2025-07-23 18:48:31,645 - INFO - Validated 89/89 requests
2025-07-23 18:48:31,795 - INFO - Updated existing file: Pasted image 20231014200829.png (ID: 1djVOj-7KJ6Jo3e_MWii2dh6clUdOio0s)
2025-07-23 18:48:32,532 - INFO - Successfully applied formatting to document: Bài toán liệt kê
2025-07-23 18:48:32,532 - INFO - Successfully processed Google Doc: Bài toán liệt kê
2025-07-23 18:48:32,533 - INFO - Syncing note: Vue - Nuxt.md
2025-07-23 18:48:33,026 - INFO - Found existing document: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 18:48:33,027 - INFO - Document Vue - Nuxt already exists, updating content...
2025-07-23 18:48:34,339 - INFO - Cleared content from document: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8
2025-07-23 18:48:34,340 - INFO - Using existing Google Doc: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 18:48:34,340 - INFO - Applying 311 formatting requests...
2025-07-23 18:48:34,341 - ERROR - Error inserting image Pasted image 20231014200829.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1zgjhuv7c9cE894QbtTCCjJ6IcLmhzwlEwD64gNotcH8:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 46 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 46 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:34,341 - ERROR - Failed to insert image: Pasted image 20231014200829.png
2025-07-23 18:48:34,341 - INFO - Syncing note: Windows Tips.md
2025-07-23 18:48:34,350 - INFO - Validated 311/311 requests
2025-07-23 18:48:34,351 - INFO - Validated 311/311 requests
2025-07-23 18:48:34,804 - INFO - Found existing document: Windows Tips (ID: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU)
2025-07-23 18:48:34,804 - INFO - Document Windows Tips already exists, updating content...
2025-07-23 18:48:35,082 - INFO - Successfully applied formatting to document: Vue - Nuxt
2025-07-23 18:48:35,082 - INFO - Successfully processed Google Doc: Vue - Nuxt
2025-07-23 18:48:35,082 - INFO - Syncing note: Kudofoto.md
2025-07-23 18:48:35,489 - INFO - Found existing document: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 18:48:35,489 - INFO - Document Kudofoto already exists, updating content...
2025-07-23 18:48:35,780 - INFO - Cleared content from document: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU
2025-07-23 18:48:35,780 - INFO - Using existing Google Doc: Windows Tips (ID: 1cJCZPhQ4il_5zGPGbEuh8Yzq3UajQXkwQn4dkmwkrqU)
2025-07-23 18:48:35,780 - INFO - Applying 50 formatting requests...
2025-07-23 18:48:35,787 - INFO - Validated 50/50 requests
2025-07-23 18:48:35,787 - INFO - Validated 50/50 requests
2025-07-23 18:48:36,424 - INFO - Successfully applied formatting to document: Windows Tips
2025-07-23 18:48:36,424 - INFO - Successfully processed Google Doc: Windows Tips
2025-07-23 18:48:36,424 - INFO - Syncing note: Du lịch Huế.md
2025-07-23 18:48:36,660 - INFO - Cleared content from document: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs
2025-07-23 18:48:36,660 - INFO - Using existing Google Doc: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 18:48:36,660 - INFO - Applying 3 formatting requests...
2025-07-23 18:48:36,661 - INFO - Validated 3/3 requests
2025-07-23 18:48:36,661 - INFO - Validated 3/3 requests
2025-07-23 18:48:36,916 - INFO - Found existing document: Du lịch Huế (ID: 1tsw_aeq_8hk4ZRgMwIcJvT6BEM2Zz85tgER8Jk0Uip8)
2025-07-23 18:48:36,916 - INFO - Document Du lịch Huế already exists, updating content...
2025-07-23 18:48:37,172 - INFO - Successfully applied formatting to document: Kudofoto
2025-07-23 18:48:37,172 - INFO - Successfully processed Google Doc: Kudofoto
2025-07-23 18:48:37,172 - INFO - Syncing note: Solutions & System Designs & Design Patterns.md
2025-07-23 18:48:37,174 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 18:48:37,174 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 18:48:37,175 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:48:37,175 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 18:48:37,176 - INFO - Found embedded image: Untitled 3.png
2025-07-23 18:48:37,176 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:48:37,176 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:48:37,176 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 18:48:37,176 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 18:48:37,176 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 18:48:37,176 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 18:48:37,177 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 18:48:37,177 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 18:48:37,177 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:48:37,177 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 18:48:37,721 - INFO - Found existing document: Solutions & System Designs & Design Patterns (ID: 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0)
2025-07-23 18:48:37,721 - INFO - Document Solutions & System Designs & Design Patterns already exists, updating content...
2025-07-23 18:48:38,509 - INFO - Cleared content from document: 1tsw_aeq_8hk4ZRgMwIcJvT6BEM2Zz85tgER8Jk0Uip8
2025-07-23 18:48:38,510 - INFO - Using existing Google Doc: Du lịch Huế (ID: 1tsw_aeq_8hk4ZRgMwIcJvT6BEM2Zz85tgER8Jk0Uip8)
2025-07-23 18:48:38,510 - INFO - Applying 174 formatting requests...
2025-07-23 18:48:38,515 - INFO - Validated 174/174 requests
2025-07-23 18:48:38,515 - INFO - Validated 174/174 requests
2025-07-23 18:48:38,916 - ERROR - Error clearing document content 1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1TO0d9bwFlsB8ugHjS5j7oztbAmJ-XR8JJo-6xW4TmV0:batchUpdate?alt=json returned "Invalid requests[0].deleteContentRange: The range should not be empty.". Details: "Invalid requests[0].deleteContentRange: The range should not be empty.">
2025-07-23 18:48:38,919 - WARNING - Failed to update Solutions & System Designs & Design Patterns, creating new document...
2025-07-23 18:48:39,319 - INFO - Successfully applied formatting to document: Du lịch Huế
2025-07-23 18:48:39,319 - INFO - Successfully processed Google Doc: Du lịch Huế
2025-07-23 18:48:39,320 - INFO - Syncing note: Research websites.md
2025-07-23 18:48:39,808 - INFO - Found existing document: Research websites (ID: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58)
2025-07-23 18:48:39,808 - INFO - Document Research websites already exists, updating content...
2025-07-23 18:48:40,183 - INFO - Created new Google Doc: Solutions & System Designs & Design Patterns (ID: 1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM)
2025-07-23 18:48:41,277 - INFO - Cleared content from document: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58
2025-07-23 18:48:41,277 - INFO - Using existing Google Doc: Research websites (ID: 1bEf_3LjMuf4Zth83CKalQ1ehNurHF634NJMywA5hb58)
2025-07-23 18:48:41,277 - INFO - Applying 20 formatting requests...
2025-07-23 18:48:41,279 - INFO - Validated 20/20 requests
2025-07-23 18:48:41,279 - INFO - Validated 20/20 requests
2025-07-23 18:48:41,933 - INFO - Successfully applied formatting to document: Research websites
2025-07-23 18:48:41,933 - INFO - Successfully processed Google Doc: Research websites
2025-07-23 18:48:41,933 - INFO - Syncing note: Terminal UI - TUI.md
2025-07-23 18:48:42,467 - INFO - Found existing document: Terminal UI - TUI (ID: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4)
2025-07-23 18:48:42,467 - INFO - Document Terminal UI - TUI already exists, updating content...
2025-07-23 18:48:42,581 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:48:42,581 - INFO - Applying 1006 formatting requests...
2025-07-23 18:48:42,627 - INFO - Validated 1006/1006 requests
2025-07-23 18:48:42,627 - INFO - Validated 1006/1006 requests
2025-07-23 18:48:43,953 - INFO - Cleared content from document: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4
2025-07-23 18:48:43,953 - INFO - Using existing Google Doc: Terminal UI - TUI (ID: 1GSkX8bgTQPSZ71HdGOSyeeAhWAKxf4WZGvFFe09hVd4)
2025-07-23 18:48:43,954 - INFO - Applying 38 formatting requests...
2025-07-23 18:48:43,955 - INFO - Validated 38/38 requests
2025-07-23 18:48:43,955 - INFO - Validated 38/38 requests
2025-07-23 18:48:44,595 - INFO - Successfully applied formatting to document: Terminal UI - TUI
2025-07-23 18:48:44,595 - INFO - Successfully processed Google Doc: Terminal UI - TUI
2025-07-23 18:48:44,595 - INFO - Syncing note: Các loại quần nên có trong tủ đồ.md
2025-07-23 18:48:44,596 - INFO - Found embedded image: Untitled 15.png
2025-07-23 18:48:44,596 - INFO - Found embedded image: Untitled 1 8.png
2025-07-23 18:48:44,596 - INFO - Found embedded image: Untitled 2 5.png
2025-07-23 18:48:45,131 - INFO - Found existing document: Các loại quần nên có trong tủ đồ (ID: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU)
2025-07-23 18:48:45,132 - INFO - Document Các loại quần nên có trong tủ đồ already exists, updating content...
2025-07-23 18:48:45,572 - INFO - Successfully applied formatting to document: Solutions & System Designs & Design Patterns
2025-07-23 18:48:45,572 - INFO - Successfully processed Google Doc: Solutions & System Designs & Design Patterns
2025-07-23 18:48:45,649 - WARNING - Could not find placeholder for image: Pasted image 20241012194316.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240927232457.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240903223244.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240903230303.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240903230309.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240425163824.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240425163928.png
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:48:45,659 - WARNING - Could not find placeholder for image: Pasted image 20240904085651.png
2025-07-23 18:48:45,660 - INFO - Recreating document with placeholders removed...
2025-07-23 18:48:46,405 - INFO - Cleared content from document: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU
2025-07-23 18:48:46,406 - INFO - Using existing Google Doc: Các loại quần nên có trong tủ đồ (ID: 16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU)
2025-07-23 18:48:46,406 - INFO - Applying 13 formatting requests...
2025-07-23 18:48:46,407 - INFO - Validated 13/13 requests
2025-07-23 18:48:46,408 - INFO - Validated 13/13 requests
2025-07-23 18:48:46,810 - INFO - Validated 1001/1001 requests
2025-07-23 18:48:46,937 - INFO - Successfully applied formatting to document: Các loại quần nên có trong tủ đồ
2025-07-23 18:48:46,937 - INFO - Successfully processed Google Doc: Các loại quần nên có trong tủ đồ
2025-07-23 18:48:46,940 - INFO - Recreating document with placeholders removed...
2025-07-23 18:48:47,689 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM:batchUpdate?alt=json returned "Invalid requests[15].updateParagraphStyle: Index 860 must be less than the end index of the referenced segment, 842.". Details: "Invalid requests[15].updateParagraphStyle: Index 860 must be less than the end index of the referenced segment, 842.">
2025-07-23 18:48:47,690 - INFO - Processing image: Untitled 3.png at position 12607
2025-07-23 18:48:47,790 - INFO - Validated 10/10 requests
2025-07-23 18:48:48,136 - INFO - Found existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 18:48:48,137 - INFO - File Untitled 3.png already exists, updating...
2025-07-23 18:48:48,202 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU:batchUpdate?alt=json returned "Invalid requests[4].insertText: Index 437 must be less than the end index of the referenced segment, 416.". Details: "Invalid requests[4].insertText: Index 437 must be less than the end index of the referenced segment, 416.">
2025-07-23 18:48:48,202 - INFO - Processing image: Untitled 2 5.png at position 904
2025-07-23 18:48:48,650 - INFO - Found existing file: Untitled 2 5.png (ID: 1XEPzWgDsqiLVu8qaI-QythxFslGZqTQR)
2025-07-23 18:48:48,650 - INFO - File Untitled 2 5.png already exists, updating...
2025-07-23 18:48:52,511 - INFO - Updated existing file: Untitled 2 5.png (ID: 1XEPzWgDsqiLVu8qaI-QythxFslGZqTQR)
2025-07-23 18:48:52,754 - INFO - Updated existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 18:48:55,172 - ERROR - Error inserting image Untitled 2 5.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 904 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 904 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:55,172 - ERROR - Failed to insert image: Untitled 2 5.png
2025-07-23 18:48:55,172 - INFO - Processing image: Untitled 1 8.png at position 691
2025-07-23 18:48:55,271 - ERROR - Error inserting image Untitled 3.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 12607 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 12607 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:48:55,271 - ERROR - Failed to insert image: Untitled 3.png
2025-07-23 18:48:55,271 - INFO - Processing image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg at position 10319
2025-07-23 18:48:55,621 - INFO - Found existing file: Untitled 1 8.png (ID: 1xQL65i5prT573u1YuZrBi83cV7CZrA_v)
2025-07-23 18:48:55,621 - INFO - File Untitled 1 8.png already exists, updating...
2025-07-23 18:48:55,725 - INFO - Found existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 18:48:55,725 - INFO - File 390dd032e50c3364eec22e71a19b2113_MD5.jpg already exists, updating...
2025-07-23 18:48:59,799 - INFO - Updated existing file: Untitled 1 8.png (ID: 1xQL65i5prT573u1YuZrBi83cV7CZrA_v)
2025-07-23 18:49:00,175 - INFO - Updated existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 18:49:02,374 - ERROR - Error inserting image 390dd032e50c3364eec22e71a19b2113_MD5.jpg: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 10319 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 10319 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:49:02,374 - ERROR - Failed to insert image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:49:02,374 - INFO - Processing image: Pasted image 20240904085651.png at position 10184
2025-07-23 18:49:02,404 - ERROR - Error inserting image Untitled 1 8.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 691 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 691 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:49:02,404 - ERROR - Failed to insert image: Untitled 1 8.png
2025-07-23 18:49:02,404 - INFO - Processing image: Untitled 15.png at position 415
2025-07-23 18:49:02,773 - INFO - Found existing file: Untitled 15.png (ID: 1Y7No72BnqKOp9_fHTGcnfwXMXw7MkFxu)
2025-07-23 18:49:02,774 - INFO - File Untitled 15.png already exists, updating...
2025-07-23 18:49:02,797 - INFO - Found existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 18:49:02,797 - INFO - File Pasted image 20240904085651.png already exists, updating...
2025-07-23 18:49:06,777 - INFO - Updated existing file: Untitled 15.png (ID: 1Y7No72BnqKOp9_fHTGcnfwXMXw7MkFxu)
2025-07-23 18:49:06,944 - INFO - Updated existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 18:49:09,270 - ERROR - Error inserting image Untitled 15.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/16o_Qp0KunGsHDPBXEC8IGXTCZkwl5HwO-ZVeYuKNPcU:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 415 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 415 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:49:09,273 - ERROR - Failed to insert image: Untitled 15.png
2025-07-23 18:49:09,293 - INFO - Syncing note: Management.md
2025-07-23 18:49:10,034 - INFO - Found existing document: Management (ID: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg)
2025-07-23 18:49:10,035 - INFO - Document Management already exists, updating content...
2025-07-23 18:49:11,518 - INFO - Cleared content from document: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg
2025-07-23 18:49:11,519 - INFO - Using existing Google Doc: Management (ID: 1TjvPRlAt6KdXilAtl1mxWyD1i0VNQcjLblShX_0Jfqg)
2025-07-23 18:49:11,519 - INFO - Applying 19 formatting requests...
2025-07-23 18:49:11,520 - INFO - Validated 19/19 requests
2025-07-23 18:49:11,521 - INFO - Validated 19/19 requests
2025-07-23 18:49:12,341 - INFO - Successfully applied formatting to document: Management
2025-07-23 18:49:12,342 - INFO - Successfully processed Google Doc: Management
2025-07-23 18:49:12,342 - INFO - Syncing note: Monitor server.md
2025-07-23 18:49:12,848 - INFO - Found existing document: Monitor server (ID: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w)
2025-07-23 18:49:12,849 - INFO - Document Monitor server already exists, updating content...
2025-07-23 18:49:14,125 - INFO - Cleared content from document: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w
2025-07-23 18:49:14,125 - INFO - Using existing Google Doc: Monitor server (ID: 18QAkrY_8inTaG-ST2ex44R3RH0oyvDjNZO2lmXeCR0w)
2025-07-23 18:49:14,125 - INFO - Applying 6 formatting requests...
2025-07-23 18:49:14,125 - INFO - Validated 6/6 requests
2025-07-23 18:49:14,125 - INFO - Validated 6/6 requests
2025-07-23 18:49:14,835 - INFO - Successfully applied formatting to document: Monitor server
2025-07-23 18:49:14,835 - INFO - Successfully processed Google Doc: Monitor server
2025-07-23 18:49:14,835 - INFO - Syncing note: English with LLM.md
2025-07-23 18:49:15,292 - INFO - Found existing document: English with LLM (ID: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk)
2025-07-23 18:49:15,292 - INFO - Document English with LLM already exists, updating content...
2025-07-23 18:49:16,675 - INFO - Cleared content from document: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk
2025-07-23 18:49:16,676 - INFO - Using existing Google Doc: English with LLM (ID: 11sE14ivV3qs4448wFpb2jaCglZQo2yB6wOYsEIS7vVk)
2025-07-23 18:49:16,676 - INFO - Applying 71 formatting requests...
2025-07-23 18:49:16,683 - INFO - Validated 71/71 requests
2025-07-23 18:49:16,685 - INFO - Validated 71/71 requests
2025-07-23 18:49:17,474 - INFO - Successfully applied formatting to document: English with LLM
2025-07-23 18:49:17,474 - INFO - Successfully processed Google Doc: English with LLM
2025-07-23 18:49:17,480 - INFO - Syncing note: Network.md
2025-07-23 18:49:17,959 - INFO - Found existing document: Network (ID: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0)
2025-07-23 18:49:17,959 - INFO - Document Network already exists, updating content...
2025-07-23 18:49:19,490 - INFO - Cleared content from document: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0
2025-07-23 18:49:19,490 - INFO - Using existing Google Doc: Network (ID: 1cVxBxhtezGm1pWg-J-rhO5tzscXGwYb3V5zB-44daK0)
2025-07-23 18:49:19,490 - INFO - Applying 21 formatting requests...
2025-07-23 18:49:19,491 - INFO - Validated 21/21 requests
2025-07-23 18:49:19,491 - INFO - Validated 21/21 requests
2025-07-23 18:49:20,343 - INFO - Successfully applied formatting to document: Network
2025-07-23 18:49:20,344 - INFO - Successfully processed Google Doc: Network
2025-07-23 18:49:20,344 - INFO - Syncing note: Python.md
2025-07-23 18:49:20,812 - INFO - Found existing document: Python (ID: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg)
2025-07-23 18:49:20,812 - INFO - Document Python already exists, updating content...
2025-07-23 18:49:22,362 - INFO - Cleared content from document: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg
2025-07-23 18:49:22,362 - INFO - Using existing Google Doc: Python (ID: 1lnfHHel5g-wtPw_sqyKWe35IyfgmLprjNP4-fkJDHsg)
2025-07-23 18:49:22,362 - INFO - Applying 22 formatting requests...
2025-07-23 18:49:22,363 - INFO - Validated 22/22 requests
2025-07-23 18:49:22,363 - INFO - Validated 22/22 requests
2025-07-23 18:49:23,188 - INFO - Successfully applied formatting to document: Python
2025-07-23 18:49:23,188 - INFO - Successfully processed Google Doc: Python
2025-07-23 18:49:23,188 - INFO - Syncing note: Tạo video bằng các tool AI.md
2025-07-23 18:49:23,346 - ERROR - Error inserting image Pasted image 20240904085651.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 10184 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 10184 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:49:23,347 - ERROR - Failed to insert image: Pasted image 20240904085651.png
2025-07-23 18:49:23,347 - INFO - Processing image: Pasted image 20240903223244.png at position 9815
2025-07-23 18:49:23,682 - INFO - Found existing document: Tạo video bằng các tool AI (ID: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY)
2025-07-23 18:49:23,682 - INFO - Document Tạo video bằng các tool AI already exists, updating content...
2025-07-23 18:49:23,790 - INFO - Found existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 18:49:23,790 - INFO - File Pasted image 20240903223244.png already exists, updating...
2025-07-23 18:49:25,154 - INFO - Cleared content from document: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY
2025-07-23 18:49:25,156 - INFO - Using existing Google Doc: Tạo video bằng các tool AI (ID: 1Q4-Lt7dp8C_SdiPV5BEkgHPrqrL4AoqLFCjjSsTAjSY)
2025-07-23 18:49:25,156 - INFO - Applying 35 formatting requests...
2025-07-23 18:49:25,211 - INFO - Validated 35/35 requests
2025-07-23 18:49:25,211 - INFO - Validated 35/35 requests
2025-07-23 18:49:26,305 - INFO - Successfully applied formatting to document: Tạo video bằng các tool AI
2025-07-23 18:49:26,306 - INFO - Successfully processed Google Doc: Tạo video bằng các tool AI
2025-07-23 18:49:26,388 - INFO - Syncing note: English with LLM - Irregular verbs.md
2025-07-23 18:49:27,124 - INFO - Found existing document: English with LLM - Irregular verbs (ID: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno)
2025-07-23 18:49:27,124 - INFO - Document English with LLM - Irregular verbs already exists, updating content...
2025-07-23 18:49:28,175 - INFO - Updated existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 18:49:29,014 - INFO - Cleared content from document: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno
2025-07-23 18:49:29,014 - INFO - Using existing Google Doc: English with LLM - Irregular verbs (ID: 196VDsUjgp6sZIsKxs-WR_6kl4Is_Zb3WJYrPr160gno)
2025-07-23 18:49:29,014 - INFO - Applying 306 formatting requests...
2025-07-23 18:49:29,195 - INFO - Validated 306/306 requests
2025-07-23 18:49:29,195 - INFO - Validated 306/306 requests
2025-07-23 18:49:30,296 - ERROR - Error inserting image Pasted image 20240903223244.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 9815 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 9815 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:49:30,296 - ERROR - Failed to insert image: Pasted image 20240903223244.png
2025-07-23 18:49:30,296 - INFO - Processing image: Pasted image 20240903230309.png at position 9078
2025-07-23 18:49:30,419 - INFO - Successfully applied formatting to document: English with LLM - Irregular verbs
2025-07-23 18:49:30,419 - INFO - Successfully processed Google Doc: English with LLM - Irregular verbs
2025-07-23 18:49:30,420 - INFO - Syncing note: Work.md
2025-07-23 18:49:30,915 - INFO - Found existing document: Work (ID: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU)
2025-07-23 18:49:30,915 - INFO - Document Work already exists, updating content...
2025-07-23 18:49:32,155 - INFO - Cleared content from document: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU
2025-07-23 18:49:32,156 - INFO - Using existing Google Doc: Work (ID: 1saDbL2T3xy0EAUzpmIsohg51IBO1NudsLWUjetUeVyU)
2025-07-23 18:49:32,156 - INFO - Applying 21 formatting requests...
2025-07-23 18:49:32,158 - INFO - Validated 21/21 requests
2025-07-23 18:49:32,158 - INFO - Validated 21/21 requests
2025-07-23 18:49:32,928 - INFO - Successfully applied formatting to document: Work
2025-07-23 18:49:32,928 - INFO - Successfully processed Google Doc: Work
2025-07-23 18:49:32,929 - INFO - Syncing note: Chuyến Du Lịch Công Ty Inception Labs 2025.md
2025-07-23 18:49:33,541 - INFO - Found existing document: Chuyến Du Lịch Công Ty Inception Labs 2025 (ID: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s)
2025-07-23 18:49:33,541 - INFO - Document Chuyến Du Lịch Công Ty Inception Labs 2025 already exists, updating content...
2025-07-23 18:49:35,276 - INFO - Cleared content from document: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s
2025-07-23 18:49:35,276 - INFO - Using existing Google Doc: Chuyến Du Lịch Công Ty Inception Labs 2025 (ID: 1TMTticn-h1N5BTclZhkQ4PoEdzn8XGlT87Bz6T80m6s)
2025-07-23 18:49:35,276 - INFO - Applying 182 formatting requests...
2025-07-23 18:49:35,286 - INFO - Validated 182/182 requests
2025-07-23 18:49:35,286 - INFO - Validated 182/182 requests
2025-07-23 18:49:36,160 - INFO - Successfully applied formatting to document: Chuyến Du Lịch Công Ty Inception Labs 2025
2025-07-23 18:49:36,161 - INFO - Successfully processed Google Doc: Chuyến Du Lịch Công Ty Inception Labs 2025
2025-07-23 18:49:36,161 - INFO - Syncing note: Dagger & Koin.md
2025-07-23 18:49:36,654 - INFO - Found existing document: Dagger & Koin (ID: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s)
2025-07-23 18:49:36,654 - INFO - Document Dagger & Koin already exists, updating content...
2025-07-23 18:49:37,916 - INFO - Cleared content from document: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s
2025-07-23 18:49:37,917 - INFO - Using existing Google Doc: Dagger & Koin (ID: 1WU-SXX8_ZYJapUhL4NfFSyb1R7UaqSLu5Q4VidMJc8s)
2025-07-23 18:49:37,917 - INFO - Applying 17 formatting requests...
2025-07-23 18:49:37,918 - INFO - Validated 17/17 requests
2025-07-23 18:49:37,918 - INFO - Validated 17/17 requests
2025-07-23 18:49:38,700 - INFO - Successfully applied formatting to document: Dagger & Koin
2025-07-23 18:49:38,700 - INFO - Successfully processed Google Doc: Dagger & Koin
2025-07-23 18:49:38,701 - INFO - Syncing note: SolidJS.md
2025-07-23 18:49:39,137 - INFO - Found existing document: SolidJS (ID: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0)
2025-07-23 18:49:39,137 - INFO - Document SolidJS already exists, updating content...
2025-07-23 18:49:41,783 - INFO - Cleared content from document: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0
2025-07-23 18:49:41,784 - INFO - Using existing Google Doc: SolidJS (ID: 1UMMmHAB7OoKqKAB7APlzexinKnQMz-T6YYB2KpzdjK0)
2025-07-23 18:49:41,784 - INFO - Applying 3 formatting requests...
2025-07-23 18:49:41,784 - INFO - Validated 3/3 requests
2025-07-23 18:49:41,785 - INFO - Validated 3/3 requests
2025-07-23 18:49:42,680 - INFO - Successfully applied formatting to document: SolidJS
2025-07-23 18:49:42,681 - INFO - Successfully processed Google Doc: SolidJS
2025-07-23 18:49:42,681 - INFO - Syncing note: Fresher Back-end Interview.md
2025-07-23 18:49:43,131 - INFO - Found existing document: Fresher Back-end Interview (ID: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU)
2025-07-23 18:49:43,132 - INFO - Document Fresher Back-end Interview already exists, updating content...
2025-07-23 18:49:44,734 - INFO - Cleared content from document: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU
2025-07-23 18:49:44,735 - INFO - Using existing Google Doc: Fresher Back-end Interview (ID: 1nIJsJZ4F5UjH8Z3fOyWmtruAxvjXo6p1Rpsa0d3ZzDU)
2025-07-23 18:49:44,735 - INFO - Applying 37 formatting requests...
2025-07-23 18:49:44,740 - INFO - Validated 37/37 requests
2025-07-23 18:49:44,741 - INFO - Validated 37/37 requests
2025-07-23 18:49:45,730 - INFO - Successfully applied formatting to document: Fresher Back-end Interview
2025-07-23 18:49:45,730 - INFO - Successfully processed Google Doc: Fresher Back-end Interview
2025-07-23 18:49:45,731 - INFO - Syncing note: CSS.md
2025-07-23 18:49:46,236 - INFO - Found existing document: CSS (ID: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4)
2025-07-23 18:49:46,236 - INFO - Document CSS already exists, updating content...
2025-07-23 18:49:47,932 - INFO - Cleared content from document: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4
2025-07-23 18:49:47,933 - INFO - Using existing Google Doc: CSS (ID: 1hAMbm0rWWZtih05lQWiuCbChZfNQfBFV_sjMyjN0lP4)
2025-07-23 18:49:47,933 - INFO - Applying 86 formatting requests...
2025-07-23 18:49:47,938 - INFO - Validated 86/86 requests
2025-07-23 18:49:47,938 - INFO - Validated 86/86 requests
2025-07-23 18:49:48,661 - INFO - Successfully applied formatting to document: CSS
2025-07-23 18:49:48,661 - INFO - Successfully processed Google Doc: CSS
2025-07-23 18:49:48,661 - INFO - Syncing note: 25 phương pháp giúp bạn ngừng overthinking.md
2025-07-23 18:49:49,141 - INFO - Found existing document: 25 phương pháp giúp bạn ngừng overthinking (ID: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY)
2025-07-23 18:49:49,141 - INFO - Document 25 phương pháp giúp bạn ngừng overthinking already exists, updating content...
2025-07-23 18:49:51,589 - INFO - Cleared content from document: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY
2025-07-23 18:49:51,590 - INFO - Using existing Google Doc: 25 phương pháp giúp bạn ngừng overthinking (ID: 1jKmq3wsPQ5vq52eRMZiJ08bquAfweSEf_KgXoZblXUY)
2025-07-23 18:49:51,590 - INFO - Applying 57 formatting requests...
2025-07-23 18:49:51,600 - INFO - Validated 57/57 requests
2025-07-23 18:49:51,600 - INFO - Validated 57/57 requests
2025-07-23 18:49:52,464 - INFO - Successfully applied formatting to document: 25 phương pháp giúp bạn ngừng overthinking
2025-07-23 18:49:52,465 - INFO - Successfully processed Google Doc: 25 phương pháp giúp bạn ngừng overthinking
2025-07-23 18:49:52,465 - INFO - Syncing note: Working.md
2025-07-23 18:49:52,930 - INFO - Found existing document: Working (ID: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk)
2025-07-23 18:49:52,930 - INFO - Document Working already exists, updating content...
2025-07-23 18:49:54,051 - INFO - Cleared content from document: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk
2025-07-23 18:49:54,052 - INFO - Using existing Google Doc: Working (ID: 1kXijkaDqwqY5s6En5EhGhuIBdM_k_W3LFIgH6ymr9Lk)
2025-07-23 18:49:54,052 - INFO - Applying 153 formatting requests...
2025-07-23 18:49:54,055 - INFO - Validated 153/153 requests
2025-07-23 18:49:54,055 - INFO - Validated 153/153 requests
2025-07-23 18:49:55,135 - INFO - Successfully applied formatting to document: Working
2025-07-23 18:49:55,135 - INFO - Successfully processed Google Doc: Working
2025-07-23 18:49:55,135 - INFO - Syncing note: Thống kê tủ đồ hiện tại.md
2025-07-23 18:49:55,843 - INFO - Found existing document: Thống kê tủ đồ hiện tại (ID: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0)
2025-07-23 18:49:55,843 - INFO - Document Thống kê tủ đồ hiện tại already exists, updating content...
2025-07-23 18:49:57,084 - INFO - Cleared content from document: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0
2025-07-23 18:49:57,084 - INFO - Using existing Google Doc: Thống kê tủ đồ hiện tại (ID: 15PFC-hz9OhmbDLdDYdK9Yjt0dldu72d_v_gDPLIMhp0)
2025-07-23 18:49:57,084 - INFO - Applying 19 formatting requests...
2025-07-23 18:49:57,085 - INFO - Validated 19/19 requests
2025-07-23 18:49:57,086 - INFO - Validated 19/19 requests
2025-07-23 18:49:57,573 - INFO - Successfully applied formatting to document: Thống kê tủ đồ hiện tại
2025-07-23 18:49:57,575 - INFO - Successfully processed Google Doc: Thống kê tủ đồ hiện tại
2025-07-23 18:49:57,575 - INFO - Syncing note: Note lại từ Huyền Chip.md
2025-07-23 18:49:58,059 - INFO - Found existing document: Note lại từ Huyền Chip (ID: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c)
2025-07-23 18:49:58,060 - INFO - Document Note lại từ Huyền Chip already exists, updating content...
2025-07-23 18:49:59,200 - INFO - Cleared content from document: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c
2025-07-23 18:49:59,200 - INFO - Using existing Google Doc: Note lại từ Huyền Chip (ID: 11mD1mpZot8WIfmRyJ5Rprlc9NfVVflomv42hEMiC34c)
2025-07-23 18:49:59,200 - INFO - Applying 35 formatting requests...
2025-07-23 18:49:59,206 - INFO - Validated 35/35 requests
2025-07-23 18:49:59,206 - INFO - Validated 35/35 requests
2025-07-23 18:49:59,950 - INFO - Successfully applied formatting to document: Note lại từ Huyền Chip
2025-07-23 18:49:59,950 - INFO - Successfully processed Google Doc: Note lại từ Huyền Chip
2025-07-23 18:49:59,950 - INFO - Syncing note: Công cụ học tiếng Anh.md
2025-07-23 18:50:00,419 - INFO - Found existing document: Công cụ học tiếng Anh (ID: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE)
2025-07-23 18:50:00,419 - INFO - Document Công cụ học tiếng Anh already exists, updating content...
2025-07-23 18:50:01,783 - INFO - Cleared content from document: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE
2025-07-23 18:50:01,783 - INFO - Using existing Google Doc: Công cụ học tiếng Anh (ID: 1c2GLz8NhZOc4OnrY61DkCPZ3Bq3ewKglaP91_4hiCeE)
2025-07-23 18:50:01,783 - INFO - Applying 38 formatting requests...
2025-07-23 18:50:01,785 - INFO - Validated 38/38 requests
2025-07-23 18:50:01,785 - INFO - Validated 38/38 requests
2025-07-23 18:50:02,627 - INFO - Successfully applied formatting to document: Công cụ học tiếng Anh
2025-07-23 18:50:02,627 - INFO - Successfully processed Google Doc: Công cụ học tiếng Anh
2025-07-23 18:50:02,634 - INFO - Syncing note: Software Engineer Roadmap 2025 - The Complete Guide.md
2025-07-23 18:50:03,604 - INFO - Found existing document: Software Engineer Roadmap 2025 - The Complete Guide (ID: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE)
2025-07-23 18:50:03,604 - INFO - Document Software Engineer Roadmap 2025 - The Complete Guide already exists, updating content...
2025-07-23 18:50:05,252 - INFO - Cleared content from document: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE
2025-07-23 18:50:05,252 - INFO - Using existing Google Doc: Software Engineer Roadmap 2025 - The Complete Guide (ID: 1hqThsX0aF2vu2_9urqFed8WJ62Yx0Sow_X0dVR26ncE)
2025-07-23 18:50:05,252 - INFO - Applying 148 formatting requests...
2025-07-23 18:50:05,290 - INFO - Validated 148/148 requests
2025-07-23 18:50:05,290 - INFO - Validated 148/148 requests
2025-07-23 18:50:06,387 - INFO - Successfully applied formatting to document: Software Engineer Roadmap 2025 - The Complete Guide
2025-07-23 18:50:06,388 - INFO - Successfully processed Google Doc: Software Engineer Roadmap 2025 - The Complete Guide
2025-07-23 18:50:06,390 - INFO - Syncing note: English with LLM - Mệnh đề quan hệ.md
2025-07-23 18:50:07,075 - INFO - Found existing document: English with LLM - Mệnh đề quan hệ (ID: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8)
2025-07-23 18:50:07,075 - INFO - Document English with LLM - Mệnh đề quan hệ already exists, updating content...
2025-07-23 18:50:08,583 - INFO - Cleared content from document: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8
2025-07-23 18:50:08,584 - INFO - Using existing Google Doc: English with LLM - Mệnh đề quan hệ (ID: 1HRPjPJw_U7FAEfkauJGkHvFVgwgTymW2Jvjcqnz2HT8)
2025-07-23 18:50:08,585 - INFO - Applying 50 formatting requests...
2025-07-23 18:50:08,590 - INFO - Validated 50/50 requests
2025-07-23 18:50:08,590 - INFO - Validated 50/50 requests
2025-07-23 18:50:09,337 - INFO - Successfully applied formatting to document: English with LLM - Mệnh đề quan hệ
2025-07-23 18:50:09,339 - INFO - Successfully processed Google Doc: English with LLM - Mệnh đề quan hệ
2025-07-23 18:50:09,339 - INFO - Syncing note: Development documentations.md
2025-07-23 18:50:09,771 - INFO - Found existing document: Development documentations (ID: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k)
2025-07-23 18:50:09,771 - INFO - Document Development documentations already exists, updating content...
2025-07-23 18:50:11,184 - INFO - Cleared content from document: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k
2025-07-23 18:50:11,185 - INFO - Using existing Google Doc: Development documentations (ID: 1iuVuCo3SHgz8UuN3QiStxRUPo_yH6m5Qc_AIYycLX3k)
2025-07-23 18:50:11,185 - INFO - Applying 16 formatting requests...
2025-07-23 18:50:11,185 - INFO - Validated 16/16 requests
2025-07-23 18:50:11,186 - INFO - Validated 16/16 requests
2025-07-23 18:50:11,853 - INFO - Successfully applied formatting to document: Development documentations
2025-07-23 18:50:11,853 - INFO - Successfully processed Google Doc: Development documentations
2025-07-23 18:50:11,853 - INFO - Syncing note: Freelance.md
2025-07-23 18:50:12,346 - INFO - Found existing document: Freelance (ID: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg)
2025-07-23 18:50:12,346 - INFO - Document Freelance already exists, updating content...
2025-07-23 18:50:13,704 - INFO - Cleared content from document: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg
2025-07-23 18:50:13,706 - INFO - Using existing Google Doc: Freelance (ID: 1i0YG2ey7N8mVkIjHtHe3oInV5J0ZppDrAk-JH8lbZVg)
2025-07-23 18:50:13,706 - INFO - Applying 31 formatting requests...
2025-07-23 18:50:13,708 - INFO - Validated 31/31 requests
2025-07-23 18:50:13,708 - INFO - Validated 31/31 requests
2025-07-23 18:50:14,501 - INFO - Successfully applied formatting to document: Freelance
2025-07-23 18:50:14,501 - INFO - Successfully processed Google Doc: Freelance
2025-07-23 18:50:14,502 - INFO - Syncing note: Phượt.md
2025-07-23 18:50:15,031 - INFO - Found existing document: Phượt (ID: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE)
2025-07-23 18:50:15,031 - INFO - Document Phượt already exists, updating content...
2025-07-23 18:50:16,533 - INFO - Cleared content from document: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE
2025-07-23 18:50:16,534 - INFO - Using existing Google Doc: Phượt (ID: 1rwhOUR0ti4paqPsdHc6xZ1XEfi6p-6eVuF2Lzssg-YE)
2025-07-23 18:50:16,534 - INFO - Applying 8 formatting requests...
2025-07-23 18:50:16,534 - INFO - Validated 8/8 requests
2025-07-23 18:50:16,534 - INFO - Validated 8/8 requests
2025-07-23 18:50:17,255 - INFO - Successfully applied formatting to document: Phượt
2025-07-23 18:50:17,255 - INFO - Successfully processed Google Doc: Phượt
2025-07-23 18:50:17,255 - INFO - Syncing note: Luận bàn về async.md
2025-07-23 18:50:17,708 - INFO - Found existing document: Luận bàn về async (ID: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y)
2025-07-23 18:50:17,708 - INFO - Document Luận bàn về async already exists, updating content...
2025-07-23 18:50:19,218 - INFO - Cleared content from document: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y
2025-07-23 18:50:19,219 - INFO - Using existing Google Doc: Luận bàn về async (ID: 1cED9AIq4sRLUAq9glev7XTrOTkmVhXc5OLp01sc3E_Y)
2025-07-23 18:50:19,219 - INFO - Applying 16 formatting requests...
2025-07-23 18:50:19,220 - INFO - Validated 16/16 requests
2025-07-23 18:50:19,220 - INFO - Validated 16/16 requests
2025-07-23 18:50:19,798 - INFO - Successfully applied formatting to document: Luận bàn về async
2025-07-23 18:50:19,798 - INFO - Successfully processed Google Doc: Luận bàn về async
2025-07-23 18:50:19,798 - INFO - Syncing note: Clean Code notes.md
2025-07-23 18:50:20,315 - INFO - Found existing document: Clean Code notes (ID: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis)
2025-07-23 18:50:20,316 - INFO - Document Clean Code notes already exists, updating content...
2025-07-23 18:50:21,691 - INFO - Cleared content from document: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis
2025-07-23 18:50:21,692 - INFO - Using existing Google Doc: Clean Code notes (ID: 1HuYLce74EdhM0zxs6hwkc_YcknFhbF3OwX1P41Ygdis)
2025-07-23 18:50:21,692 - INFO - Applying 165 formatting requests...
2025-07-23 18:50:21,694 - INFO - Validated 165/165 requests
2025-07-23 18:50:21,694 - INFO - Validated 165/165 requests
2025-07-23 18:50:22,377 - INFO - Successfully applied formatting to document: Clean Code notes
2025-07-23 18:50:22,377 - INFO - Successfully processed Google Doc: Clean Code notes
2025-07-23 18:50:22,378 - INFO - Syncing note: LLM promt engineering.md
2025-07-23 18:50:22,894 - INFO - Found existing document: LLM promt engineering (ID: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc)
2025-07-23 18:50:22,895 - INFO - Document LLM promt engineering already exists, updating content...
2025-07-23 18:50:24,402 - INFO - Cleared content from document: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc
2025-07-23 18:50:24,403 - INFO - Using existing Google Doc: LLM promt engineering (ID: 1X4vpT7asYV6LcTz4kVnSN2jvatR9f8Djp_sIqLJsvdc)
2025-07-23 18:50:24,403 - INFO - Applying 129 formatting requests...
2025-07-23 18:50:24,406 - INFO - Validated 129/129 requests
2025-07-23 18:50:24,406 - INFO - Validated 129/129 requests
2025-07-23 18:50:25,319 - INFO - Successfully applied formatting to document: LLM promt engineering
2025-07-23 18:50:25,320 - INFO - Successfully processed Google Doc: LLM promt engineering
2025-07-23 18:50:25,320 - INFO - Syncing note: Fullstack - Full-stack.md
2025-07-23 18:50:25,827 - INFO - Found existing document: Fullstack - Full-stack (ID: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM)
2025-07-23 18:50:25,828 - INFO - Document Fullstack - Full-stack already exists, updating content...
2025-07-23 18:50:27,271 - INFO - Cleared content from document: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM
2025-07-23 18:50:27,272 - INFO - Using existing Google Doc: Fullstack - Full-stack (ID: 1-cfEffa7-Gtw0XaWSANuxNModd_r3zMsucXQKBZlQFM)
2025-07-23 18:50:27,272 - INFO - Applying 17 formatting requests...
2025-07-23 18:50:27,273 - INFO - Validated 17/17 requests
2025-07-23 18:50:27,273 - INFO - Validated 17/17 requests
2025-07-23 18:50:27,785 - INFO - Successfully applied formatting to document: Fullstack - Full-stack
2025-07-23 18:50:27,785 - INFO - Successfully processed Google Doc: Fullstack - Full-stack
2025-07-23 18:50:27,785 - INFO - Syncing note: Life.md
2025-07-23 18:50:28,261 - INFO - Found existing document: Life (ID: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs)
2025-07-23 18:50:28,261 - INFO - Document Life already exists, updating content...
2025-07-23 18:50:29,510 - INFO - Cleared content from document: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs
2025-07-23 18:50:29,510 - INFO - Using existing Google Doc: Life (ID: 10U1ufwoI6C_mVZcmweNGNcgtetvNj-b4NzhUfey9lGs)
2025-07-23 18:50:29,510 - INFO - Applying 10 formatting requests...
2025-07-23 18:50:29,514 - INFO - Validated 10/10 requests
2025-07-23 18:50:29,514 - INFO - Validated 10/10 requests
2025-07-23 18:50:30,147 - INFO - Successfully applied formatting to document: Life
2025-07-23 18:50:30,147 - INFO - Successfully processed Google Doc: Life
2025-07-23 18:50:30,147 - INFO - Syncing note: Business.md
2025-07-23 18:50:30,625 - INFO - Found existing document: Business (ID: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg)
2025-07-23 18:50:30,625 - INFO - Document Business already exists, updating content...
2025-07-23 18:50:32,009 - INFO - Cleared content from document: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg
2025-07-23 18:50:32,009 - INFO - Using existing Google Doc: Business (ID: 1sAcpcGwWWzC0CLpESrrvR56gwID31be2cvEPn1H2AZg)
2025-07-23 18:50:32,009 - INFO - Applying 5 formatting requests...
2025-07-23 18:50:32,009 - INFO - Validated 5/5 requests
2025-07-23 18:50:32,009 - INFO - Validated 5/5 requests
2025-07-23 18:50:32,839 - INFO - Successfully applied formatting to document: Business
2025-07-23 18:50:32,840 - INFO - Successfully processed Google Doc: Business
2025-07-23 18:50:32,840 - INFO - Syncing note: ARBO.md
2025-07-23 18:50:32,841 - INFO - Found embedded image: Untitled 2 7.png
2025-07-23 18:50:32,842 - INFO - Found embedded image: Untitled 3 4.png
2025-07-23 18:50:32,842 - INFO - Found embedded image: Untitled 4 2.png
2025-07-23 18:50:33,297 - INFO - Found existing document: ARBO (ID: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8)
2025-07-23 18:50:33,297 - INFO - Document ARBO already exists, updating content...
2025-07-23 18:50:34,575 - INFO - Cleared content from document: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8
2025-07-23 18:50:34,575 - INFO - Using existing Google Doc: ARBO (ID: 1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8)
2025-07-23 18:50:34,576 - INFO - Applying 44 formatting requests...
2025-07-23 18:50:34,577 - INFO - Validated 44/44 requests
2025-07-23 18:50:34,577 - INFO - Validated 44/44 requests
2025-07-23 18:50:35,161 - INFO - Successfully applied formatting to document: ARBO
2025-07-23 18:50:35,161 - INFO - Successfully processed Google Doc: ARBO
2025-07-23 18:50:35,162 - INFO - Recreating document with placeholders removed...
2025-07-23 18:50:36,014 - INFO - Validated 41/41 requests
2025-07-23 18:50:36,431 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8:batchUpdate?alt=json returned "Invalid requests[3].insertText: Index 117 must be less than the end index of the referenced segment, 52.". Details: "Invalid requests[3].insertText: Index 117 must be less than the end index of the referenced segment, 52.">
2025-07-23 18:50:36,432 - INFO - Processing image: Untitled 4 2.png at position 95
2025-07-23 18:50:36,890 - INFO - Found existing file: Untitled 4 2.png (ID: 1SSmhf8SDT3EyNTDDbn7FH9wzvkg2wrkF)
2025-07-23 18:50:36,891 - INFO - File Untitled 4 2.png already exists, updating...
2025-07-23 18:50:41,228 - INFO - Updated existing file: Untitled 4 2.png (ID: 1SSmhf8SDT3EyNTDDbn7FH9wzvkg2wrkF)
2025-07-23 18:50:43,886 - ERROR - Error inserting image Untitled 4 2.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 95 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 95 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:50:43,887 - ERROR - Failed to insert image: Untitled 4 2.png
2025-07-23 18:50:43,887 - INFO - Processing image: Untitled 3 4.png at position 73
2025-07-23 18:50:44,327 - INFO - Found existing file: Untitled 3 4.png (ID: 1IxtfsRZI_UulU08TwIsVjBwjTNGOb1L9)
2025-07-23 18:50:44,327 - INFO - File Untitled 3 4.png already exists, updating...
2025-07-23 18:50:49,431 - INFO - Updated existing file: Untitled 3 4.png (ID: 1IxtfsRZI_UulU08TwIsVjBwjTNGOb1L9)
2025-07-23 18:50:52,189 - ERROR - Error inserting image Untitled 3 4.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 73 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 73 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:50:52,189 - ERROR - Failed to insert image: Untitled 3 4.png
2025-07-23 18:50:52,190 - INFO - Processing image: Untitled 2 7.png at position 51
2025-07-23 18:50:52,613 - INFO - Found existing file: Untitled 2 7.png (ID: 1xe_8fd-SjQFVYSIR92eLX03RFCJjKE0H)
2025-07-23 18:50:52,613 - INFO - File Untitled 2 7.png already exists, updating...
2025-07-23 18:50:56,846 - INFO - Updated existing file: Untitled 2 7.png (ID: 1xe_8fd-SjQFVYSIR92eLX03RFCJjKE0H)
2025-07-23 18:50:59,454 - ERROR - Error inserting image Untitled 2 7.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1peGdm3tkhZbgWEYdsdM06I6dA01dzZlDWhS9kGofmj8:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 51 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 51 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:50:59,454 - ERROR - Failed to insert image: Untitled 2 7.png
2025-07-23 18:50:59,454 - INFO - Syncing note: Note câu hỏi phỏng vấn Laravel.md
2025-07-23 18:50:59,455 - INFO - Found embedded image: Screenshot 2023-05-27 144607 1.png
2025-07-23 18:50:59,982 - INFO - Found existing document: Note câu hỏi phỏng vấn Laravel (ID: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A)
2025-07-23 18:50:59,982 - INFO - Document Note câu hỏi phỏng vấn Laravel already exists, updating content...
2025-07-23 18:51:01,195 - INFO - Cleared content from document: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A
2025-07-23 18:51:01,196 - INFO - Using existing Google Doc: Note câu hỏi phỏng vấn Laravel (ID: 1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A)
2025-07-23 18:51:01,196 - INFO - Applying 12 formatting requests...
2025-07-23 18:51:01,196 - INFO - Validated 12/12 requests
2025-07-23 18:51:01,196 - INFO - Validated 12/12 requests
2025-07-23 18:51:01,778 - INFO - Successfully applied formatting to document: Note câu hỏi phỏng vấn Laravel
2025-07-23 18:51:01,778 - INFO - Successfully processed Google Doc: Note câu hỏi phỏng vấn Laravel
2025-07-23 18:51:01,779 - INFO - Recreating document with placeholders removed...
2025-07-23 18:51:02,596 - INFO - Validated 11/11 requests
2025-07-23 18:51:03,000 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1e_Ak4q9s2MxplPlaoGbYGt7y4Zzzw5g6b8nZUzCDn5A:batchUpdate?alt=json returned "Invalid requests[0].insertText: Index 23 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertText: Index 23 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:51:03,000 - INFO - Processing image: Screenshot 2023-05-27 144607 1.png at position 1
2025-07-23 18:51:03,478 - INFO - Found existing file: Screenshot 2023-05-27 144607 1.png (ID: 1fNqpl_OCBRSYkibWH9TXSXDYnneODe4R)
2025-07-23 18:51:03,478 - INFO - File Screenshot 2023-05-27 144607 1.png already exists, updating...
2025-07-23 18:51:07,758 - INFO - Updated existing file: Screenshot 2023-05-27 144607 1.png (ID: 1fNqpl_OCBRSYkibWH9TXSXDYnneODe4R)
2025-07-23 18:51:10,162 - INFO - Inserted image Screenshot 2023-05-27 144607 1.png into document
2025-07-23 18:51:10,163 - INFO - Successfully inserted image: Screenshot 2023-05-27 144607 1.png at index 1
2025-07-23 18:51:10,163 - INFO - Syncing note: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md
2025-07-23 18:51:10,683 - INFO - Found existing document: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow (ID: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw)
2025-07-23 18:51:10,683 - INFO - Document viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow already exists, updating content...
2025-07-23 18:51:12,607 - INFO - Cleared content from document: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw
2025-07-23 18:51:12,608 - INFO - Using existing Google Doc: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow (ID: 1AEOQ3WP2ZZ6aTEm-CFp_XRh-e3wT1FvTsx8bJga7Hhw)
2025-07-23 18:51:12,608 - INFO - Applying 203 formatting requests...
2025-07-23 18:51:12,613 - INFO - Validated 203/203 requests
2025-07-23 18:51:12,613 - INFO - Validated 203/203 requests
2025-07-23 18:51:13,362 - INFO - Successfully applied formatting to document: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow
2025-07-23 18:51:13,362 - INFO - Successfully processed Google Doc: viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow
2025-07-23 18:51:13,362 - INFO - Syncing note: Machine learning - Deep Learning - AI - ML - DL.md
2025-07-23 18:51:13,938 - INFO - Found existing document: Machine learning - Deep Learning - AI - ML - DL (ID: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y)
2025-07-23 18:51:13,938 - INFO - Document Machine learning - Deep Learning - AI - ML - DL already exists, updating content...
2025-07-23 18:51:15,681 - INFO - Cleared content from document: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y
2025-07-23 18:51:15,681 - INFO - Using existing Google Doc: Machine learning - Deep Learning - AI - ML - DL (ID: 1zeW3XyySPpZ_RQXfAVP_yqxd4fcjyq4AoAIyOfXf75Y)
2025-07-23 18:51:15,681 - INFO - Applying 388 formatting requests...
2025-07-23 18:51:15,697 - INFO - Validated 388/388 requests
2025-07-23 18:51:15,698 - INFO - Validated 388/388 requests
2025-07-23 18:51:16,697 - INFO - Successfully applied formatting to document: Machine learning - Deep Learning - AI - ML - DL
2025-07-23 18:51:16,698 - INFO - Successfully processed Google Doc: Machine learning - Deep Learning - AI - ML - DL
2025-07-23 18:51:16,698 - INFO - Syncing note: Note ebook Thuật toán của thầy Lê Minh Hoàng.md
2025-07-23 18:51:17,696 - INFO - Found existing document: Note ebook Thuật toán của thầy Lê Minh Hoàng (ID: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM)
2025-07-23 18:51:17,696 - INFO - Document Note ebook Thuật toán của thầy Lê Minh Hoàng already exists, updating content...
2025-07-23 18:51:18,820 - INFO - Starting Obsidian to Google Docs/Drive sync...
2025-07-23 18:51:18,822 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:51:18,824 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-07-23 18:51:18,825 - INFO - Google authentication successful
2025-07-23 18:51:18,900 - INFO - Cleared content from document: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM
2025-07-23 18:51:18,900 - INFO - Using existing Google Doc: Note ebook Thuật toán của thầy Lê Minh Hoàng (ID: 1B_0BPHn5hiw4AT_ME6Ti07JbqC7jIMkRlsD6ufaJlHM)
2025-07-23 18:51:18,900 - INFO - Applying 6 formatting requests...
2025-07-23 18:51:18,900 - INFO - Validated 6/6 requests
2025-07-23 18:51:18,900 - INFO - Validated 6/6 requests
2025-07-23 18:51:19,573 - INFO - Found existing folder: Obsidian Sync (ID: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze)
2025-07-23 18:51:19,614 - INFO - Successfully applied formatting to document: Note ebook Thuật toán của thầy Lê Minh Hoàng
2025-07-23 18:51:19,615 - INFO - Successfully processed Google Doc: Note ebook Thuật toán của thầy Lê Minh Hoàng
2025-07-23 18:51:19,615 - INFO - Syncing note: English with LLM - Câu nhấn mạnh.md
2025-07-23 18:51:20,034 - INFO - Found existing folder: Media (ID: 14YGgZka9M8qAgLNqd9nDkB5BffMK8Cqp)
2025-07-23 18:51:20,036 - INFO - Found 218 markdown files in Obsidian vault
2025-07-23 18:51:20,036 - INFO - Syncing folder: Root (214 notes)
2025-07-23 18:51:20,036 - INFO - Syncing note: Kafka.md
2025-07-23 18:51:20,052 - INFO - Found existing document: English with LLM - Câu nhấn mạnh (ID: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg)
2025-07-23 18:51:20,052 - INFO - Document English with LLM - Câu nhấn mạnh already exists, updating content...
2025-07-23 18:51:20,557 - INFO - Found existing document: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:51:20,557 - INFO - Document Kafka already exists, updating content...
2025-07-23 18:51:21,561 - INFO - Cleared content from document: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg
2025-07-23 18:51:21,561 - INFO - Using existing Google Doc: English with LLM - Câu nhấn mạnh (ID: 1DttWidv1R-s0S4WCTD1Q34rJoyE2SOMiDVqLPwvoIyg)
2025-07-23 18:51:21,561 - INFO - Applying 45 formatting requests...
2025-07-23 18:51:21,564 - INFO - Validated 45/45 requests
2025-07-23 18:51:21,565 - INFO - Validated 45/45 requests
2025-07-23 18:51:21,740 - INFO - Cleared content from document: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0
2025-07-23 18:51:21,741 - INFO - Using existing Google Doc: Kafka (ID: 1jlXD0Popu9BskX3wGnka3k8OhbdU_2qFUKJPJN62Z_0)
2025-07-23 18:51:21,741 - INFO - Applying 14 formatting requests...
2025-07-23 18:51:21,741 - INFO - Validated 14/14 requests
2025-07-23 18:51:21,741 - INFO - Validated 14/14 requests
2025-07-23 18:51:22,178 - INFO - Successfully applied formatting to document: Kafka
2025-07-23 18:51:22,178 - INFO - Successfully processed Google Doc: Kafka
2025-07-23 18:51:22,178 - INFO - Syncing note: Bài toán liệt kê.md
2025-07-23 18:51:22,194 - INFO - Successfully applied formatting to document: English with LLM - Câu nhấn mạnh
2025-07-23 18:51:22,194 - INFO - Successfully processed Google Doc: English with LLM - Câu nhấn mạnh
2025-07-23 18:51:22,194 - INFO - Syncing note: Nginx.md
2025-07-23 18:51:22,637 - INFO - Found existing document: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 18:51:22,637 - INFO - Document Bài toán liệt kê already exists, updating content...
2025-07-23 18:51:22,663 - INFO - Found existing document: Nginx (ID: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso)
2025-07-23 18:51:22,663 - INFO - Document Nginx already exists, updating content...
2025-07-23 18:51:23,620 - INFO - Cleared content from document: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4
2025-07-23 18:51:23,621 - INFO - Using existing Google Doc: Bài toán liệt kê (ID: 1h0pJe2O87C21NcKYTtbXNG8YeqCVUpJQb2LjfXQrZw4)
2025-07-23 18:51:23,621 - INFO - Applying 89 formatting requests...
2025-07-23 18:51:23,624 - INFO - Validated 89/89 requests
2025-07-23 18:51:23,624 - INFO - Validated 89/89 requests
2025-07-23 18:51:24,169 - INFO - Cleared content from document: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso
2025-07-23 18:51:24,169 - INFO - Using existing Google Doc: Nginx (ID: 1WBbdWZ5ERcj9vobhFHwR5xGS7iGZCMW2sNsp7v_VRso)
2025-07-23 18:51:24,170 - INFO - Applying 14 formatting requests...
2025-07-23 18:51:24,170 - INFO - Validated 14/14 requests
2025-07-23 18:51:24,170 - INFO - Validated 14/14 requests
2025-07-23 18:51:24,486 - INFO - Successfully applied formatting to document: Bài toán liệt kê
2025-07-23 18:51:24,490 - INFO - Successfully processed Google Doc: Bài toán liệt kê
2025-07-23 18:51:24,499 - INFO - Syncing note: Vue - Nuxt.md
2025-07-23 18:51:24,818 - INFO - Successfully applied formatting to document: Nginx
2025-07-23 18:51:24,818 - INFO - Successfully processed Google Doc: Nginx
2025-07-23 18:51:24,819 - INFO - Syncing note: Chỗ mua đồ.md
2025-07-23 18:51:25,038 - INFO - Found existing document: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 18:51:25,038 - INFO - Document Vue - Nuxt already exists, updating content...
2025-07-23 18:51:25,270 - INFO - Found existing document: Chỗ mua đồ (ID: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8)
2025-07-23 18:51:25,270 - INFO - Document Chỗ mua đồ already exists, updating content...
2025-07-23 18:51:26,257 - INFO - Cleared content from document: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8
2025-07-23 18:51:26,258 - INFO - Using existing Google Doc: Vue - Nuxt (ID: 1A-cgkX31JEnrow3E7v_gM69rgp2d9RLGabFVlZVqFr8)
2025-07-23 18:51:26,258 - INFO - Applying 311 formatting requests...
2025-07-23 18:51:26,264 - INFO - Validated 311/311 requests
2025-07-23 18:51:26,265 - INFO - Validated 311/311 requests
2025-07-23 18:51:26,480 - INFO - Cleared content from document: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8
2025-07-23 18:51:26,480 - INFO - Using existing Google Doc: Chỗ mua đồ (ID: 10QfLEh9t38hQ9GKWhNfh_DCwuEus-n9WIxSel7tgmY8)
2025-07-23 18:51:26,480 - INFO - Applying 22 formatting requests...
2025-07-23 18:51:26,480 - INFO - Validated 22/22 requests
2025-07-23 18:51:26,480 - INFO - Validated 22/22 requests
2025-07-23 18:51:27,078 - INFO - Successfully applied formatting to document: Vue - Nuxt
2025-07-23 18:51:27,078 - INFO - Successfully processed Google Doc: Vue - Nuxt
2025-07-23 18:51:27,079 - INFO - Syncing note: Kudofoto.md
2025-07-23 18:51:27,384 - INFO - Successfully applied formatting to document: Chỗ mua đồ
2025-07-23 18:51:27,385 - INFO - Successfully processed Google Doc: Chỗ mua đồ
2025-07-23 18:51:27,385 - INFO - Syncing note: AI support for coding.md
2025-07-23 18:51:27,522 - INFO - Found existing document: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 18:51:27,522 - INFO - Document Kudofoto already exists, updating content...
2025-07-23 18:51:27,971 - INFO - Found existing document: AI support for coding (ID: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q)
2025-07-23 18:51:27,971 - INFO - Document AI support for coding already exists, updating content...
2025-07-23 18:51:28,388 - INFO - Cleared content from document: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs
2025-07-23 18:51:28,389 - INFO - Using existing Google Doc: Kudofoto (ID: 1eEnD53HPwzOf3_xUvhGc2z7BAj0vdjN0_ExpO1xkeYs)
2025-07-23 18:51:28,389 - INFO - Applying 3 formatting requests...
2025-07-23 18:51:28,389 - INFO - Validated 3/3 requests
2025-07-23 18:51:28,389 - INFO - Validated 3/3 requests
2025-07-23 18:51:28,820 - INFO - Successfully applied formatting to document: Kudofoto
2025-07-23 18:51:28,820 - INFO - Successfully processed Google Doc: Kudofoto
2025-07-23 18:51:28,820 - INFO - Syncing note: Solutions & System Designs & Design Patterns.md
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:51:28,821 - INFO - Found embedded image: Pasted image 20241012194316.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: Untitled 3.png
2025-07-23 18:51:28,821 - INFO - Found embedded image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:51:28,821 - INFO - Found embedded image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240927232457.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240903223244.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240903230303.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240903230309.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240425163824.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240425163928.png
2025-07-23 18:51:28,822 - INFO - Found embedded image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:51:28,822 - INFO - Found embedded image: Pasted image 20240904085651.png
2025-07-23 18:51:29,267 - INFO - Cleared content from document: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q
2025-07-23 18:51:29,267 - INFO - Using existing Google Doc: AI support for coding (ID: 1dIJaVIUVBpseT7dx8S7_RRwF_1H6P4QVRhfI5Qhcd7Q)
2025-07-23 18:51:29,267 - INFO - Applying 101 formatting requests...
2025-07-23 18:51:29,272 - INFO - Validated 101/101 requests
2025-07-23 18:51:29,273 - INFO - Validated 101/101 requests
2025-07-23 18:51:29,360 - INFO - Found existing document: Solutions & System Designs & Design Patterns (ID: 1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM)
2025-07-23 18:51:29,360 - INFO - Document Solutions & System Designs & Design Patterns already exists, updating content...
2025-07-23 18:51:29,901 - INFO - Successfully applied formatting to document: AI support for coding
2025-07-23 18:51:29,901 - INFO - Successfully processed Google Doc: AI support for coding
2025-07-23 18:51:29,902 - INFO - Syncing note: Cách đặt câu hỏi cho ChatGPT.md
2025-07-23 18:51:30,189 - ERROR - Error clearing document content 1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1M7qWHMJb3M8qlhhKd5H-ElF8v_ZIujmeWwbPTtaQaFM:batchUpdate?alt=json returned "Invalid requests[0].deleteContentRange: The range should not be empty.". Details: "Invalid requests[0].deleteContentRange: The range should not be empty.">
2025-07-23 18:51:30,189 - WARNING - Failed to update Solutions & System Designs & Design Patterns, creating new document...
2025-07-23 18:51:30,364 - INFO - Found existing document: Cách đặt câu hỏi cho ChatGPT (ID: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q)
2025-07-23 18:51:30,367 - INFO - Document Cách đặt câu hỏi cho ChatGPT already exists, updating content...
2025-07-23 18:51:31,334 - INFO - Created new Google Doc: Solutions & System Designs & Design Patterns (ID: 1EKocTwdPHN7mTeJLJ2EmCGLTwo59FSLMi_1fcBadEh4)
2025-07-23 18:51:31,721 - INFO - Cleared content from document: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q
2025-07-23 18:51:31,721 - INFO - Using existing Google Doc: Cách đặt câu hỏi cho ChatGPT (ID: 1rP9LemAo2nvIkDEcuAhYJaD_z1sV7kY9Ow9gnLFCc5Q)
2025-07-23 18:51:31,722 - INFO - Applying 49 formatting requests...
2025-07-23 18:51:31,740 - INFO - Validated 49/49 requests
2025-07-23 18:51:31,741 - INFO - Validated 49/49 requests
2025-07-23 18:51:32,361 - INFO - Successfully applied formatting to document: Cách đặt câu hỏi cho ChatGPT
2025-07-23 18:51:32,362 - INFO - Successfully processed Google Doc: Cách đặt câu hỏi cho ChatGPT
2025-07-23 18:51:32,362 - INFO - Syncing note: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC.md
2025-07-23 18:51:32,827 - INFO - Found existing document: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC (ID: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI)
2025-07-23 18:51:32,828 - INFO - Document English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC already exists, updating content...
2025-07-23 18:51:33,477 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:51:33,478 - INFO - Applying 1006 formatting requests...
2025-07-23 18:51:33,524 - INFO - Validated 1006/1006 requests
2025-07-23 18:51:33,524 - INFO - Validated 1006/1006 requests
2025-07-23 18:51:34,409 - INFO - Cleared content from document: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI
2025-07-23 18:51:34,409 - INFO - Using existing Google Doc: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC (ID: 1UE6czEghOZGcqHhKM0LRR31_c8OwUmMmfePBoKCnwbI)
2025-07-23 18:51:34,409 - INFO - Applying 414 formatting requests...
2025-07-23 18:51:34,420 - INFO - Validated 414/414 requests
2025-07-23 18:51:34,420 - INFO - Validated 414/414 requests
2025-07-23 18:51:35,314 - INFO - Successfully applied formatting to document: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC
2025-07-23 18:51:35,315 - INFO - Successfully processed Google Doc: English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC
2025-07-23 18:51:35,315 - INFO - Syncing note: Râm Generation.md
2025-07-23 18:51:35,763 - INFO - Found existing document: Râm Generation (ID: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8)
2025-07-23 18:51:35,763 - INFO - Document Râm Generation already exists, updating content...
2025-07-23 18:51:37,028 - INFO - Cleared content from document: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8
2025-07-23 18:51:37,028 - INFO - Using existing Google Doc: Râm Generation (ID: 1peqAjdW8UFqJyzYH9qNDC55JKYpye2aRtAM09iHnWP8)
2025-07-23 18:51:37,028 - INFO - Applying 24 formatting requests...
2025-07-23 18:51:37,029 - INFO - Validated 24/24 requests
2025-07-23 18:51:37,029 - INFO - Validated 24/24 requests
2025-07-23 18:51:37,602 - INFO - Successfully applied formatting to document: Solutions & System Designs & Design Patterns
2025-07-23 18:51:37,603 - INFO - Successfully processed Google Doc: Solutions & System Designs & Design Patterns
2025-07-23 18:51:37,646 - INFO - Successfully applied formatting to document: Râm Generation
2025-07-23 18:51:37,646 - INFO - Successfully processed Google Doc: Râm Generation
2025-07-23 18:51:37,646 - INFO - Syncing note: Ollama.md
2025-07-23 18:51:37,664 - WARNING - Could not find placeholder for image: Pasted image 20241012194316.png
2025-07-23 18:51:37,673 - WARNING - Could not find placeholder for image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: bddf3546-c720-4313-9046-36d8c4a97019.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240927232457.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240903223244.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240903230303.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240903230309.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240425163824.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240425163928.png
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg
2025-07-23 18:51:37,674 - WARNING - Could not find placeholder for image: Pasted image 20240904085651.png
2025-07-23 18:51:37,675 - INFO - Recreating document with placeholders removed...
2025-07-23 18:51:38,030 - INFO - Found existing document: Ollama (ID: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8)
2025-07-23 18:51:38,030 - INFO - Document Ollama already exists, updating content...
2025-07-23 18:51:39,209 - INFO - Cleared content from document: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8
2025-07-23 18:51:39,209 - INFO - Using existing Google Doc: Ollama (ID: 1r8Hgvo0k_U6wtDcK-x2OYn98hylJvv4tU0h3uJw-Ii8)
2025-07-23 18:51:39,209 - INFO - Applying 8 formatting requests...
2025-07-23 18:51:39,210 - INFO - Validated 8/8 requests
2025-07-23 18:51:39,210 - INFO - Validated 8/8 requests
2025-07-23 18:51:39,716 - INFO - Validated 1001/1001 requests
2025-07-23 18:51:40,066 - INFO - Successfully applied formatting to document: Ollama
2025-07-23 18:51:40,067 - INFO - Successfully processed Google Doc: Ollama
2025-07-23 18:51:40,067 - INFO - Syncing note: English with LLM - Từ nối.md
2025-07-23 18:51:40,507 - INFO - Found existing document: English with LLM - Từ nối (ID: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw)
2025-07-23 18:51:40,507 - INFO - Document English with LLM - Từ nối already exists, updating content...
2025-07-23 18:51:41,963 - INFO - Cleared content from document: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw
2025-07-23 18:51:41,964 - INFO - Using existing Google Doc: English with LLM - Từ nối (ID: 1qdD6Z0MJftKSiG4UnATn0cJeYYL8UoWEmeUbe9YA2Xw)
2025-07-23 18:51:41,964 - INFO - Applying 52 formatting requests...
2025-07-23 18:51:41,966 - INFO - Validated 52/52 requests
2025-07-23 18:51:41,966 - INFO - Validated 52/52 requests
2025-07-23 18:51:42,637 - INFO - Successfully applied formatting to document: English with LLM - Từ nối
2025-07-23 18:51:42,637 - INFO - Successfully processed Google Doc: English with LLM - Từ nối
2025-07-23 18:51:42,637 - INFO - Syncing note: Sống Platform.md
2025-07-23 18:51:42,876 - INFO - Updated document content with 1001 requests
2025-07-23 18:51:42,877 - INFO - Processing image: Untitled 3.png at position 12607
2025-07-23 18:51:43,087 - INFO - Found existing document: Sống Platform (ID: 1HSh2u3SnqpVWCXWcwqrDLTc---iEj8B4j3kzo9WBh4w)
2025-07-23 18:51:43,087 - INFO - Document Sống Platform already exists, updating content...
2025-07-23 18:51:43,319 - INFO - Found existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 18:51:43,320 - INFO - File Untitled 3.png already exists, updating...
2025-07-23 18:51:44,408 - INFO - Cleared content from document: 1HSh2u3SnqpVWCXWcwqrDLTc---iEj8B4j3kzo9WBh4w
2025-07-23 18:51:44,409 - INFO - Using existing Google Doc: Sống Platform (ID: 1HSh2u3SnqpVWCXWcwqrDLTc---iEj8B4j3kzo9WBh4w)
2025-07-23 18:51:44,409 - INFO - Applying 34 formatting requests...
2025-07-23 18:51:44,409 - INFO - Validated 34/34 requests
2025-07-23 18:51:44,409 - INFO - Validated 34/34 requests
2025-07-23 18:51:44,929 - INFO - Successfully applied formatting to document: Sống Platform
2025-07-23 18:51:44,929 - INFO - Successfully processed Google Doc: Sống Platform
2025-07-23 18:51:44,929 - INFO - Syncing note: Top 10 câu hỏi phỏng vấn System Design và Microservices.md
2025-07-23 18:51:44,931 - INFO - Found embedded image: a03077b24546810e9aabd32f2afe2608_MD5.webp
2025-07-23 18:51:45,397 - INFO - Found existing document: Top 10 câu hỏi phỏng vấn System Design và Microservices (ID: 12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4)
2025-07-23 18:51:45,397 - INFO - Document Top 10 câu hỏi phỏng vấn System Design và Microservices already exists, updating content...
2025-07-23 18:51:46,779 - INFO - Cleared content from document: 12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4
2025-07-23 18:51:46,779 - INFO - Using existing Google Doc: Top 10 câu hỏi phỏng vấn System Design và Microservices (ID: 12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4)
2025-07-23 18:51:46,779 - INFO - Applying 50 formatting requests...
2025-07-23 18:51:46,784 - INFO - Validated 50/50 requests
2025-07-23 18:51:46,784 - INFO - Validated 50/50 requests
2025-07-23 18:51:47,270 - ERROR - Error applying formatting to Top 10 câu hỏi phỏng vấn System Design và Microservices: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Invalid requests[40].insertText: The insertion index cannot be within a grapheme cluster.". Details: "Invalid requests[40].insertText: The insertion index cannot be within a grapheme cluster.">
2025-07-23 18:51:47,271 - ERROR - Problematic requests: [{'insertText': {'location': {'index': 1}, 'text': 'Nhà tuyển dụng thường yêu cầu ứng viên ở vị trí Senior Software Engineer (Backend) cần giải quyết được một số vấn đề của hệ thống Microservices và khả năng thiết kế hệ thống. Dưới đây là một số câu hỏi và gợi ý cách trả lời để bạn tham khảo về 2 chủ đề trên.\n'}}, {'insertText': {'location': {'index': 260}, 'text': 'Lưu ý: những câu hỏi và gợi ý dưới đây chỉ mang tính tham khảo. Bạn nên tự xây dựng câu trả lời hoàn chỉnh của riêng mình.\n'}}, {'insertText': {'location': {'index': 383}, 'text': '0.1. System Design:\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 383, 'endIndex': 402}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 403}, 'text': '0.1.1. Bạn trình bày giúp mình về định lý CAP?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 403, 'endIndex': 449}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 450}, 'text': 'Gợi ý:\nBạn có thể tham khảo\xa0video này - https://www.youtube.com/watch?v=BHqjEjzAicA\nNgoài ra, bạn nên tìm hiểu thêm sự khác nhau sự Consistency trong định lý CAP và Consistency trong tính chất ACID.\n'}}, {'insertText': {'location': {'index': 649}, 'text': '0.1.2. Một bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khiến cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 649, 'endIndex': 779}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 780}, 'text': 'Gợi ý:\nĐầu tiên, bạn nên làm rõ ý nghĩa của dữ liệu trong bảng và độ lớn của bảng để đưa ra những giải pháp phù hợp. Và nên trình bày theo trình tự từ đơn giản đến phức tạp.\n'}}, {'insertText': {'location': {'index': 954}, 'text': '0.1.3. Thiết kế hệ thống TinyURL?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 954, 'endIndex': 987}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 988}, 'text': 'Gợi ý:\nĐây là một câu hỏi rất phổ biến vì nó có tính phân hoá cao. Độ phức tạp của hệ thống phụ thuộc vào yêu cầu của nhà tuyển dụng. Bạn nên làm rõ những yêu cầu và chú ý một số vấn đề sau:\n'}}, {'insertText': {'location': {'index': 1179}, 'text': 'LIST_BLOCK_0\n[IMAGE_PLACEHOLDER_0]\n\xa0Vẹt xám châu Phi được cho rằng là loài chim thông minh nhất thế giới. Chúng có những khả năng đặc biệt như bước chước giọng người, giải toán và có khả năng nhận thức. Đây là Alex (1976 - 2007) được biết đến là chú vẹt xám châu Phi nổi tiếng nhất trong lịch sử. Alex sở hữu hơn 100 âm thanh của các đồ vật và hành động khác nhau như tiếng gà gáy, tiếng lợn kêu, âm thanh của phi thuyền, tiếng thở dài, … Ngoài ra, Alex có thể sắp xếp đồ vật, có thể đếm đến 6, …\n'}}, {'insertText': {'location': {'index': 1676}, 'text': '0.2. Microservice:\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1676, 'endIndex': 1694}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1695}, 'text': '0.2.1. Bạn hãy so sánh ưu nhược điểm của monolithic và microservices?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1695, 'endIndex': 1764}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1765}, 'text': 'Gợi ý: Để câu trả lời thuyết phục hơn, bạn có thể nêu ra những yếu tố ảnh hưởng tới từng ưu nhược điểm.\n'}}, {'insertText': {'location': {'index': 1869}, 'text': '0.2.2. Tại sao bạn lại chia service như này?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 1869, 'endIndex': 1913}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 1914}, 'text': 'Gợi ý: Đây là một câu hỏi khó. Nhiều bạn đề cập tới Domain-Driven Design nhưng hãy cẩn thận khi đề cập tới Domain-Driven Design. Bạn cần nắm rõ nghiệp vụ và cách model business bạn sử dụng là gì.\n'}}, {'insertText': {'location': {'index': 2110}, 'text': '0.2.3. Order được xử lý lần lượt qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2110, 'endIndex': 2290}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2291}, 'text': 'Gợi ý: Bạn có thể sử dụng message queue có khả năng persist được message.\n'}}, {'insertText': {'location': {'index': 2365}, 'text': '0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2365, 'endIndex': 2507}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 2508}, 'text': 'Gợi ý: Bạn cần dựa vào yêu cầu về tính nhất quán và tính đúng đắn của dữ liệu. Từ đó để đưa ra giải pháp như thêm một adapter layer hoặc đồng bộ dữ liệu từ B sang A. Lưu ý để đảm bảo tính đúng đắn của dữ liệu so với tài liệu, logic không thể dựa hoàn toàn vào tài liệu, ta cần kiểm tra dữ liệu thực tế trên môi trường prod và nonprod.\n'}}, {'insertText': {'location': {'index': 2843}, 'text': '0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order được điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. Làm sao để hệ thống trả lại response cho frontend?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 2843, 'endIndex': 3127}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3128}, 'text': 'Gợi ý: Có nhiều cách đáp ứng được yêu cầu trên. Bạn nên đánh giá về mặt hiệu năng và khả năng mở rộng của từng giải pháp.\n'}}, {'insertText': {'location': {'index': 3250}, 'text': '0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3250, 'endIndex': 3410}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3411}, 'text': 'Gợi ý: Đầu tiên bạn nên review lại thiết kế luồng, đặc biệt chú ý tới điểm giao tiếp với các third party nếu có. Sau đó cần nêu ra 1 quy trình sử dụng các công cụ monitor và distributed tracing để xác định được issue làm ở đâu. Cuối cùng đưa ra những giải pháp khắc phục như tối ưu code, query và cấp thêm tài nguyên cho các service, …\n'}}, {'insertText': {'location': {'index': 3747}, 'text': '0.3. Khác:\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3747, 'endIndex': 3757}, 'paragraphStyle': {'namedStyleType': 'HEADING_2'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3758}, 'text': '0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?\n'}}, {'updateParagraphStyle': {'range': {'startIndex': 3758, 'endIndex': 3834}, 'paragraphStyle': {'namedStyleType': 'HEADING_3'}, 'fields': 'namedStyleType'}}, {'insertText': {'location': {'index': 3835}, 'text': 'Gợi ý: Đây là một câu hỏi rất hay gặp thế nên bạn cần chuẩn bị kỹ. Mỗi người có kinh nghiệm, trải nghiệm khác nhau và gặp những vấn đề khác nhau. Bạn nên chọn ra và chuẩn bị kỹ 3 vấn đề khó nhất bạn gặp phải. Thêm nữa, cần đảm bảo rằng độ phức tạp của 3 vấn đề tương xứng với level hiện của bạn hoặc level đang hướng tới.\n'}}, {'insertText': {'location': {'index': 4157}, 'text': 'Nếu mọi người có câu trả lời hoặc câu hỏi khác thì comment ở dưới giúp mình nha 👇\nHẹn mọi người ở phần 2 với những câu hỏi về những chủ đề khác 👋\n'}}, {'insertText': {'location': {'index': 4303}, 'text': 'Nếu bạn thấy hay thì cho mình xin 1 upvote 🔼 và share nhé.\nCám ơn mọi người rất nhiều 🙏\n'}}, {'insertText': {'location': {'index': 4391}, 'text': '📚️ Ronin Engineer:\xa0https://ronin-engineer.github.io/register-post\n🏢 System Design VN:\xa0https://fb.com/groups/systemdesign.vn\n'}}, {'insertText': {'location': {'index': 4514}, 'text': 'Độ dài của short URL\n'}}, {'createParagraphBullets': {'range': {'startIndex': 4514, 'endIndex': 4534}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 4535}, 'text': 'Các yêu cầu về check trùng. Ví dụ khi shorten 1 URL nhiều lần, có cần trả về cùng 1 short URL không?\n'}}, {'createParagraphBullets': {'range': {'startIndex': 4535, 'endIndex': 4635}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 4636}, 'text': 'Bạn sử dụng DB nào?\n'}}, {'createParagraphBullets': {'range': {'startIndex': 4636, 'endIndex': 4655}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}, {'insertText': {'location': {'index': 4656}, 'text': 'Bạn xử lý đụng độ (collision) như nào?\n'}}, {'createParagraphBullets': {'range': {'startIndex': 4656, 'endIndex': 4694}, 'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'}}]
2025-07-23 18:51:47,271 - INFO - Attempting fallback approach for grapheme cluster error...
2025-07-23 18:51:47,271 - INFO - Applying 50 requests individually as fallback...
2025-07-23 18:51:48,161 - INFO - Updated existing file: Untitled 3.png (ID: 1HXQWztYeZIkQ5C8epKrBKw0GLiV8yMY6)
2025-07-23 18:51:50,842 - INFO - Inserted image Untitled 3.png into document
2025-07-23 18:51:50,842 - INFO - Successfully inserted image: Untitled 3.png at index 12607
2025-07-23 18:51:50,842 - INFO - Processing image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg at position 10319
2025-07-23 18:51:51,353 - INFO - Found existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 18:51:51,354 - INFO - File 390dd032e50c3364eec22e71a19b2113_MD5.jpg already exists, updating...
2025-07-23 18:51:51,984 - WARNING - Skipping request 9 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:52,389 - WARNING - Skipping request 10 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:52,790 - WARNING - Skipping request 11 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:53,156 - WARNING - Skipping request 12 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:53,574 - WARNING - Skipping request 13 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:53,851 - WARNING - Skipping request 14 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/561286222532'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:54,135 - WARNING - Skipping request 15 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:54,433 - WARNING - Skipping request 16 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:54,717 - WARNING - Skipping request 17 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:55,003 - WARNING - Skipping request 18 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:55,291 - WARNING - Skipping request 19 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:55,576 - WARNING - Skipping request 20 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:55,856 - WARNING - Skipping request 21 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:56,135 - WARNING - Skipping request 22 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_unit': '1/min/{project}/{user}'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:56,351 - INFO - Updated existing file: 390dd032e50c3364eec22e71a19b2113_MD5.jpg (ID: 17ZXdEoJqzIowcQh72SFOYfLFZ0RkVemS)
2025-07-23 18:51:56,421 - WARNING - Skipping request 23 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:56,704 - WARNING - Skipping request 24 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:57,074 - WARNING - Skipping request 25 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:57,351 - WARNING - Skipping request 26 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:57,632 - WARNING - Skipping request 27 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:58,021 - WARNING - Skipping request 28 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:58,314 - WARNING - Skipping request 29 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'WriteRequestsPerMinutePerUser', 'service': 'docs.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:58,380 - INFO - Inserted image 390dd032e50c3364eec22e71a19b2113_MD5.jpg into document
2025-07-23 18:51:58,381 - INFO - Successfully inserted image: 390dd032e50c3364eec22e71a19b2113_MD5.jpg at index 10319
2025-07-23 18:51:58,381 - INFO - Processing image: Pasted image 20240904085651.png at position 10184
2025-07-23 18:51:58,590 - WARNING - Skipping request 30 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:58,822 - INFO - Found existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 18:51:58,822 - INFO - File Pasted image 20240904085651.png already exists, updating...
2025-07-23 18:51:58,876 - WARNING - Skipping request 31 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:59,194 - WARNING - Skipping request 32 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:59,545 - WARNING - Skipping request 33 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:51:59,829 - WARNING - Skipping request 34 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:00,105 - WARNING - Skipping request 35 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:00,476 - WARNING - Skipping request 36 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_limit_value': '60', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:00,760 - WARNING - Skipping request 37 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:01,040 - WARNING - Skipping request 38 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:01,319 - WARNING - Skipping request 39 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:01,593 - WARNING - Skipping request 40 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:01,876 - WARNING - Skipping request 41 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:02,159 - WARNING - Skipping request 42 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:02,447 - WARNING - Skipping request 43 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:02,738 - WARNING - Skipping request 44 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'docs.googleapis.com/write_requests', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:03,025 - WARNING - Skipping request 45 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:03,334 - WARNING - Skipping request 46 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:03,369 - INFO - Updated existing file: Pasted image 20240904085651.png (ID: 1XOrWMUA1g37goqDr_GMdgPUsdb3Vn_5T)
2025-07-23 18:52:03,628 - WARNING - Skipping request 47 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:03,908 - WARNING - Skipping request 48 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:04,208 - WARNING - Skipping request 49 due to error: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:04,208 - INFO - Fallback completed: 9/50 requests applied
2025-07-23 18:52:04,211 - INFO - Recreating document with placeholders removed...
2025-07-23 18:52:04,828 - ERROR - Error updating document content: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'consumer': 'projects/561286222532', 'service': 'docs.googleapis.com', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:04,828 - INFO - Processing image: a03077b24546810e9aabd32f2afe2608_MD5.webp at position 1192
2025-07-23 18:52:05,320 - INFO - Found existing file: a03077b24546810e9aabd32f2afe2608_MD5.webp (ID: 1iXfTISL2ztsHCJfWwjJcs1kOrdi3qYWy)
2025-07-23 18:52:05,320 - INFO - File a03077b24546810e9aabd32f2afe2608_MD5.webp already exists, updating...
2025-07-23 18:52:05,704 - INFO - Inserted image Pasted image 20240904085651.png into document
2025-07-23 18:52:05,704 - INFO - Successfully inserted image: Pasted image 20240904085651.png at index 10184
2025-07-23 18:52:05,704 - INFO - Processing image: Pasted image 20240903223244.png at position 9815
2025-07-23 18:52:06,252 - INFO - Found existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 18:52:06,252 - INFO - File Pasted image 20240903223244.png already exists, updating...
2025-07-23 18:52:10,135 - INFO - Updated existing file: a03077b24546810e9aabd32f2afe2608_MD5.webp (ID: 1iXfTISL2ztsHCJfWwjJcs1kOrdi3qYWy)
2025-07-23 18:52:10,441 - ERROR - Error inserting image a03077b24546810e9aabd32f2afe2608_MD5.webp: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/12YxGinEEfwWqr2kKAPhzbvvydu6oLgi1i1BIXErRCs4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'consumer': 'projects/561286222532', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests', 'quota_location': 'global', 'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:10,442 - ERROR - Failed to insert image: a03077b24546810e9aabd32f2afe2608_MD5.webp
2025-07-23 18:52:10,442 - INFO - Syncing note: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai).md
2025-07-23 18:52:10,716 - INFO - Updated existing file: Pasted image 20240903223244.png (ID: 1HuVW4eqf5HhA59WlrYh5ZibH_ld-Teqj)
2025-07-23 18:52:11,009 - INFO - Found existing document: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) (ID: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA)
2025-07-23 18:52:11,010 - INFO - Document Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) already exists, updating content...
2025-07-23 18:52:11,397 - ERROR - Error inserting image Pasted image 20240903223244.png: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1EKocTwdPHN7mTeJLJ2EmCGLTwo59FSLMi_1fcBadEh4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'docs.googleapis.com', 'quota_unit': '1/min/{project}/{user}', 'quota_limit_value': '60', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_location': 'global', 'consumer': 'projects/561286222532', 'quota_metric': 'docs.googleapis.com/write_requests'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:11,398 - ERROR - Failed to insert image: Pasted image 20240903223244.png
2025-07-23 18:52:11,398 - INFO - Processing image: Pasted image 20240903230309.png at position 9078
2025-07-23 18:52:11,924 - INFO - Found existing file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 18:52:11,925 - INFO - File Pasted image 20240903230309.png already exists, updating...
2025-07-23 18:52:12,487 - INFO - Cleared content from document: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA
2025-07-23 18:52:12,488 - INFO - Using existing Google Doc: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai) (ID: 1dretCgwBpuHnPYP052azgfy1aQJ33z1h-YnTAAQ4gEA)
2025-07-23 18:52:12,488 - INFO - Applying 68 formatting requests...
2025-07-23 18:52:12,493 - INFO - Validated 68/68 requests
2025-07-23 18:52:12,494 - INFO - Validated 68/68 requests
2025-07-23 18:52:13,097 - INFO - Successfully applied formatting to document: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai)
2025-07-23 18:52:13,098 - INFO - Successfully processed Google Doc: Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai)
2025-07-23 18:52:13,098 - INFO - Syncing note: Kho chung IT.md
2025-07-23 18:52:13,532 - INFO - Found existing document: Kho chung IT (ID: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI)
2025-07-23 18:52:13,532 - INFO - Document Kho chung IT already exists, updating content...
2025-07-23 18:52:14,999 - INFO - Cleared content from document: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI
2025-07-23 18:52:15,000 - INFO - Using existing Google Doc: Kho chung IT (ID: 1WnQ7GECBMR999Nb22sXEj3i1Bj4-32Iff3VSWggrUvI)
2025-07-23 18:52:15,000 - INFO - Applying 83 formatting requests...
2025-07-23 18:52:15,009 - INFO - Validated 83/83 requests
2025-07-23 18:52:15,009 - INFO - Validated 83/83 requests
2025-07-23 18:52:16,084 - INFO - Successfully applied formatting to document: Kho chung IT
2025-07-23 18:52:16,085 - INFO - Successfully processed Google Doc: Kho chung IT
2025-07-23 18:52:16,085 - INFO - Syncing note: Target of users in a workspace.md
2025-07-23 18:52:16,919 - INFO - Found existing document: Target of users in a workspace (ID: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM)
2025-07-23 18:52:16,919 - INFO - Document Target of users in a workspace already exists, updating content...
2025-07-23 18:52:18,369 - INFO - Cleared content from document: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM
2025-07-23 18:52:18,370 - INFO - Using existing Google Doc: Target of users in a workspace (ID: 1TJqv1qCERtN8Fnn6Io8A4dzj7ZCaBaUnM0-kMHByTDM)
2025-07-23 18:52:18,370 - INFO - Applying 52 formatting requests...
2025-07-23 18:52:18,374 - INFO - Validated 52/52 requests
2025-07-23 18:52:18,374 - INFO - Validated 52/52 requests
2025-07-23 18:52:18,780 - INFO - Updated existing file: Pasted image 20240903230309.png (ID: 1Uip24fd7KUvsT1phhBPlkOYqUBkaLhB7)
2025-07-23 18:52:19,094 - ERROR - Error inserting image Pasted image 20240903230309.png: <HttpError 429 when requesting https://docs.googleapis.com/v1/documents/1EKocTwdPHN7mTeJLJ2EmCGLTwo59FSLMi_1fcBadEh4:batchUpdate?alt=json returned "Quota exceeded for quota metric 'Quota group for write operations' and limit 'Quota group for write operations per minute per user' of service 'docs.googleapis.com' for consumer 'project_number:561286222532'.". Details: "[{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit_value': '60', 'service': 'docs.googleapis.com', 'consumer': 'projects/561286222532', 'quota_location': 'global', 'quota_unit': '1/min/{project}/{user}', 'quota_limit': 'WriteRequestsPerMinutePerUser', 'quota_metric': 'docs.googleapis.com/write_requests'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quotas/help/request_increase'}]}]">
2025-07-23 18:52:19,095 - ERROR - Failed to insert image: Pasted image 20240903230309.png
2025-07-23 18:52:19,095 - INFO - Processing image: Pasted image 20240903230303.png at position 9056
2025-07-23 18:52:19,121 - INFO - Successfully applied formatting to document: Target of users in a workspace
2025-07-23 18:52:19,122 - INFO - Successfully processed Google Doc: Target of users in a workspace
2025-07-23 18:52:19,122 - INFO - Syncing note: Linter.md
2025-07-23 18:52:19,593 - INFO - Found existing file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 18:52:19,594 - INFO - File Pasted image 20240903230303.png already exists, updating...
2025-07-23 18:52:19,625 - INFO - Found existing document: Linter (ID: 1oaTR8u3SppuLShrHnXiFO46-eJPGVjAs0FGOZ1cm02Q)
2025-07-23 18:52:19,625 - INFO - Document Linter already exists, updating content...
2025-07-23 18:52:20,809 - ERROR - Error clearing document content 1oaTR8u3SppuLShrHnXiFO46-eJPGVjAs0FGOZ1cm02Q: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1oaTR8u3SppuLShrHnXiFO46-eJPGVjAs0FGOZ1cm02Q:batchUpdate?alt=json returned "Invalid requests[0].deleteContentRange: The range should not be empty.". Details: "Invalid requests[0].deleteContentRange: The range should not be empty.">
2025-07-23 18:52:20,809 - WARNING - Failed to update Linter, creating new document...
2025-07-23 18:52:22,340 - INFO - Created new Google Doc: Linter (ID: 1Q-OKXKShtJHU8DDrEiBkqOalYuxRiBL0vs_-I8gAqE4)
2025-07-23 18:52:24,676 - INFO - Updated existing file: Pasted image 20240903230303.png (ID: 1cjjJMo53qYi-6oi7zMWSiLA97e2XzUZe)
2025-07-23 18:52:24,988 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:52:24,988 - INFO - Applying 143 formatting requests...
2025-07-23 18:52:24,991 - INFO - Validated 143/143 requests
2025-07-23 18:52:24,991 - INFO - Validated 143/143 requests
2025-07-23 18:52:25,931 - INFO - Successfully applied formatting to document: Linter
2025-07-23 18:52:25,931 - INFO - Successfully processed Google Doc: Linter
2025-07-23 18:52:25,931 - INFO - Syncing note: Promt.md
2025-07-23 18:52:25,932 - INFO - Found embedded image: Untitled 9.png
2025-07-23 18:52:25,932 - INFO - Found embedded image: Untitled 1 4.png
2025-07-23 18:52:26,399 - INFO - Found existing document: Promt (ID: 1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0)
2025-07-23 18:52:26,400 - INFO - Document Promt already exists, updating content...
2025-07-23 18:52:26,945 - INFO - Inserted image Pasted image 20240903230303.png into document
2025-07-23 18:52:26,945 - INFO - Successfully inserted image: Pasted image 20240903230303.png at index 9056
2025-07-23 18:52:26,945 - INFO - Processing image: bddf3546-c720-4313-9046-36d8c4a97019.png at position 8690
2025-07-23 18:52:27,389 - INFO - Found existing file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 18:52:27,389 - INFO - File bddf3546-c720-4313-9046-36d8c4a97019.png already exists, updating...
2025-07-23 18:52:27,647 - INFO - Cleared content from document: 1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0
2025-07-23 18:52:27,647 - INFO - Using existing Google Doc: Promt (ID: 1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0)
2025-07-23 18:52:27,648 - INFO - Applying 23 formatting requests...
2025-07-23 18:52:27,649 - INFO - Validated 23/23 requests
2025-07-23 18:52:27,649 - INFO - Validated 23/23 requests
2025-07-23 18:52:28,503 - INFO - Successfully applied formatting to document: Promt
2025-07-23 18:52:28,503 - INFO - Successfully processed Google Doc: Promt
2025-07-23 18:52:28,504 - INFO - Recreating document with placeholders removed...
2025-07-23 18:52:29,168 - INFO - Validated 21/21 requests
2025-07-23 18:52:29,476 - ERROR - Error updating document content: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0:batchUpdate?alt=json returned "Invalid requests[9].insertText: Index 399 must be less than the end index of the referenced segment, 356.". Details: "Invalid requests[9].insertText: Index 399 must be less than the end index of the referenced segment, 356.">
2025-07-23 18:52:29,477 - INFO - Processing image: Untitled 1 4.png at position 377
2025-07-23 18:52:29,963 - INFO - Found existing file: Untitled 1 4.png (ID: 1gn-NJh3ZFSRXmNiIaXVxwrFZ5l_GwkWH)
2025-07-23 18:52:29,964 - INFO - File Untitled 1 4.png already exists, updating...
2025-07-23 18:52:31,490 - INFO - Updated existing file: bddf3546-c720-4313-9046-36d8c4a97019.png (ID: 1FHAo_wICm6U86hkecX9oU-PZQSlEF7db)
2025-07-23 18:52:34,492 - INFO - Inserted image bddf3546-c720-4313-9046-36d8c4a97019.png into document
2025-07-23 18:52:34,492 - INFO - Successfully inserted image: bddf3546-c720-4313-9046-36d8c4a97019.png at index 8690
2025-07-23 18:52:34,492 - INFO - Processing image: Pasted image 20240927232457.png at position 8144
2025-07-23 18:52:34,929 - INFO - Found existing file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 18:52:34,929 - INFO - File Pasted image 20240927232457.png already exists, updating...
2025-07-23 18:52:38,726 - INFO - Updated existing file: Pasted image 20240927232457.png (ID: 1-MZwRMJW8bBP5UM_VNREH3H_xzahgsrJ)
2025-07-23 18:52:40,513 - INFO - Inserted image Pasted image 20240927232457.png into document
2025-07-23 18:52:40,514 - INFO - Successfully inserted image: Pasted image 20240927232457.png at index 8144
2025-07-23 18:52:40,514 - INFO - Processing image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg at position 7460
2025-07-23 18:52:41,023 - INFO - Found existing file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 18:52:41,024 - INFO - File telegram-cloud-photo-size-5-6311899726957623527-y.jpg already exists, updating...
2025-07-23 18:52:43,830 - INFO - Updated existing file: Untitled 1 4.png (ID: 1gn-NJh3ZFSRXmNiIaXVxwrFZ5l_GwkWH)
2025-07-23 18:52:45,167 - INFO - Updated existing file: telegram-cloud-photo-size-5-6311899726957623527-y.jpg (ID: 1EKLrjez7nllmKASntu0TCn8btkG8K4Lp)
2025-07-23 18:52:47,265 - ERROR - Error inserting image Untitled 1 4.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 377 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 377 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:52:47,265 - ERROR - Failed to insert image: Untitled 1 4.png
2025-07-23 18:52:47,265 - INFO - Processing image: Untitled 9.png at position 355
2025-07-23 18:52:47,409 - INFO - Inserted image telegram-cloud-photo-size-5-6311899726957623527-y.jpg into document
2025-07-23 18:52:47,410 - INFO - Successfully inserted image: telegram-cloud-photo-size-5-6311899726957623527-y.jpg at index 7460
2025-07-23 18:52:47,410 - INFO - Processing image: Pasted image 20240425163928.png at position 3048
2025-07-23 18:52:47,724 - INFO - Found existing file: Untitled 9.png (ID: 1HuIRWfx8q4xq3xR2_tZV5tSknLUtXaGo)
2025-07-23 18:52:47,724 - INFO - File Untitled 9.png already exists, updating...
2025-07-23 18:52:47,881 - INFO - Found existing file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 18:52:47,881 - INFO - File Pasted image 20240425163928.png already exists, updating...
2025-07-23 18:52:51,750 - INFO - Updated existing file: Pasted image 20240425163928.png (ID: 1pBvvDB15NkxLJ4CGCuYG_CAugNKH-Uu1)
2025-07-23 18:52:52,003 - INFO - Updated existing file: Untitled 9.png (ID: 1HuIRWfx8q4xq3xR2_tZV5tSknLUtXaGo)
2025-07-23 18:52:53,551 - INFO - Inserted image Pasted image 20240425163928.png into document
2025-07-23 18:52:53,551 - INFO - Successfully inserted image: Pasted image 20240425163928.png at index 3048
2025-07-23 18:52:53,551 - INFO - Processing image: Pasted image 20240425163824.png at position 2586
2025-07-23 18:52:53,989 - INFO - Found existing file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 18:52:53,989 - INFO - File Pasted image 20240425163824.png already exists, updating...
2025-07-23 18:52:54,178 - ERROR - Error inserting image Untitled 9.png: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1WcZLpqSJcOAsKHI97OQUQb6vkvyvzLZfUeLmae5Glc0:batchUpdate?alt=json returned "Invalid requests[0].insertInlineImage: Index 355 must be less than the end index of the referenced segment, 2.". Details: "Invalid requests[0].insertInlineImage: Index 355 must be less than the end index of the referenced segment, 2.">
2025-07-23 18:52:54,178 - ERROR - Failed to insert image: Untitled 9.png
2025-07-23 18:52:54,178 - INFO - Syncing note: Rust.md
2025-07-23 18:52:54,626 - INFO - Found existing document: Rust (ID: 1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU)
2025-07-23 18:52:54,626 - INFO - Document Rust already exists, updating content...
2025-07-23 18:52:55,967 - INFO - Cleared content from document: 1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU
2025-07-23 18:52:55,967 - INFO - Using existing Google Doc: Rust (ID: 1Zks-6eybOnNcGm8t5WKqjn0MgGJOygiyrCPEdJuObGU)
2025-07-23 18:52:55,967 - INFO - Applying 76 formatting requests...
2025-07-23 18:52:55,974 - INFO - Validated 76/76 requests
2025-07-23 18:52:55,975 - INFO - Validated 76/76 requests
2025-07-23 18:52:56,771 - INFO - Successfully applied formatting to document: Rust
2025-07-23 18:52:56,771 - INFO - Successfully processed Google Doc: Rust
2025-07-23 18:52:56,771 - INFO - Syncing note: Du lịch.md
2025-07-23 18:52:57,233 - INFO - Found existing document: Du lịch (ID: 1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus)
2025-07-23 18:52:57,233 - INFO - Document Du lịch already exists, updating content...
2025-07-23 18:52:58,355 - INFO - Cleared content from document: 1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus
2025-07-23 18:52:58,356 - INFO - Using existing Google Doc: Du lịch (ID: 1mbjLmI1Lch4eWxH9D4klZfMA6ABmlN6Oom83dfFxmus)
2025-07-23 18:52:58,356 - INFO - Applying 35 formatting requests...
2025-07-23 18:52:58,357 - INFO - Validated 35/35 requests
2025-07-23 18:52:58,357 - INFO - Validated 35/35 requests
2025-07-23 18:52:58,403 - INFO - Updated existing file: Pasted image 20240425163824.png (ID: 1uch3uJOEX8PCyedJdLU1-ZUxTuxY3x92)
2025-07-23 18:52:59,131 - INFO - Successfully applied formatting to document: Du lịch
2025-07-23 18:52:59,131 - INFO - Successfully processed Google Doc: Du lịch
2025-07-23 18:52:59,131 - INFO - Syncing note: VPS - Hosting.md
2025-07-23 18:52:59,600 - INFO - Found existing document: VPS - Hosting (ID: 1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc)
2025-07-23 18:52:59,600 - INFO - Document VPS - Hosting already exists, updating content...
2025-07-23 18:53:00,475 - INFO - Inserted image Pasted image 20240425163824.png into document
2025-07-23 18:53:00,475 - INFO - Successfully inserted image: Pasted image 20240425163824.png at index 2586
2025-07-23 18:53:00,475 - INFO - Processing image: Pasted image 20241012194316.png at position 805
2025-07-23 18:53:00,794 - INFO - Cleared content from document: 1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc
2025-07-23 18:53:00,794 - INFO - Using existing Google Doc: VPS - Hosting (ID: 1b2gyXCHXg-kc7XsiRpP786YV1dhDA55hof9Jtu5jvOc)
2025-07-23 18:53:00,794 - INFO - Applying 79 formatting requests...
2025-07-23 18:53:00,797 - INFO - Validated 79/79 requests
2025-07-23 18:53:00,797 - INFO - Validated 79/79 requests
2025-07-23 18:53:00,933 - INFO - Found existing file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 18:53:00,934 - INFO - File Pasted image 20241012194316.png already exists, updating...
2025-07-23 18:53:01,535 - INFO - Successfully applied formatting to document: VPS - Hosting
2025-07-23 18:53:01,535 - INFO - Successfully processed Google Doc: VPS - Hosting
2025-07-23 18:53:01,535 - INFO - Syncing note: Trải nghiệm.md
2025-07-23 18:53:01,988 - INFO - Found existing document: Trải nghiệm (ID: 1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w)
2025-07-23 18:53:01,988 - INFO - Document Trải nghiệm already exists, updating content...
2025-07-23 18:53:03,376 - INFO - Cleared content from document: 1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w
2025-07-23 18:53:03,376 - INFO - Using existing Google Doc: Trải nghiệm (ID: 1Eo0HZ3a3_tiDLqqO2ojF1El293W23t9juyvZ0h_vQ2w)
2025-07-23 18:53:03,376 - INFO - Applying 8 formatting requests...
2025-07-23 18:53:03,376 - INFO - Validated 8/8 requests
2025-07-23 18:53:03,376 - INFO - Validated 8/8 requests
2025-07-23 18:53:04,404 - INFO - Successfully applied formatting to document: Trải nghiệm
2025-07-23 18:53:04,404 - INFO - Successfully processed Google Doc: Trải nghiệm
2025-07-23 18:53:04,404 - INFO - Syncing note: MySQL.md
2025-07-23 18:53:05,183 - INFO - Found existing document: MySQL (ID: 1Er6C_yV1msWrNCQ3WlL16MDUxsbnDBVZ24DH2-PqdpU)
2025-07-23 18:53:05,184 - INFO - Document MySQL already exists, updating content...
2025-07-23 18:53:05,280 - INFO - Updated existing file: Pasted image 20241012194316.png (ID: 1pKRFOKdZqtxJ-8cP367ZpsL8l6FCZV7r)
2025-07-23 18:53:06,310 - ERROR - Error clearing document content 1Er6C_yV1msWrNCQ3WlL16MDUxsbnDBVZ24DH2-PqdpU: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/1Er6C_yV1msWrNCQ3WlL16MDUxsbnDBVZ24DH2-PqdpU:batchUpdate?alt=json returned "Invalid requests[0].deleteContentRange: The range should not be empty.". Details: "Invalid requests[0].deleteContentRange: The range should not be empty.">
2025-07-23 18:53:06,311 - WARNING - Failed to update MySQL, creating new document...
2025-07-23 18:53:07,820 - INFO - Inserted image Pasted image 20241012194316.png into document
2025-07-23 18:53:07,820 - INFO - Successfully inserted image: Pasted image 20241012194316.png at index 805
2025-07-23 18:53:07,837 - INFO - Syncing note: Domain knowledge.md
2025-07-23 18:53:08,098 - INFO - Created new Google Doc: MySQL (ID: 1TjOKiccsaaUJn8IX4ssPEhv2njFPQgCwj8SLMFMjM70)
2025-07-23 18:53:08,385 - INFO - Found existing document: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 18:53:08,400 - INFO - Document Domain knowledge already exists, updating content...
2025-07-23 18:53:09,869 - INFO - Cleared content from document: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA
2025-07-23 18:53:09,872 - INFO - Using existing Google Doc: Domain knowledge (ID: 1xepvDvs1xsuTuWFXb61bXrMxmFV1miDYe3iZd5XG2CA)
2025-07-23 18:53:09,872 - INFO - Applying 7 formatting requests...
2025-07-23 18:53:09,874 - INFO - Validated 7/7 requests
2025-07-23 18:53:09,874 - INFO - Validated 7/7 requests
2025-07-23 18:53:10,261 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:53:10,266 - INFO - Applying 48 formatting requests...
2025-07-23 18:53:10,285 - INFO - Validated 48/48 requests
2025-07-23 18:53:10,290 - INFO - Validated 48/48 requests
2025-07-23 18:53:10,481 - INFO - Successfully applied formatting to document: Domain knowledge
2025-07-23 18:53:10,481 - INFO - Successfully processed Google Doc: Domain knowledge
2025-07-23 18:53:10,482 - INFO - Syncing note: VPN - Proxy - Firewall.md
2025-07-23 18:53:10,970 - INFO - Found existing document: VPN - Proxy - Firewall (ID: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc)
2025-07-23 18:53:10,971 - INFO - Document VPN - Proxy - Firewall already exists, updating content...
2025-07-23 18:53:11,383 - INFO - Successfully applied formatting to document: MySQL
2025-07-23 18:53:11,383 - INFO - Successfully processed Google Doc: MySQL
2025-07-23 18:53:11,384 - INFO - Syncing note: Java Spring.md
2025-07-23 18:53:11,836 - INFO - Found existing document: Java Spring (ID: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg)
2025-07-23 18:53:11,836 - INFO - Document Java Spring already exists, updating content...
2025-07-23 18:53:12,374 - INFO - Cleared content from document: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc
2025-07-23 18:53:12,375 - INFO - Using existing Google Doc: VPN - Proxy - Firewall (ID: 1YBlKUMo1cm7N6J2Q9Gogu6lHnl5eAFEO-7q_Pur3-uc)
2025-07-23 18:53:12,376 - INFO - Applying 21 formatting requests...
2025-07-23 18:53:12,377 - INFO - Validated 21/21 requests
2025-07-23 18:53:12,377 - INFO - Validated 21/21 requests
2025-07-23 18:53:12,977 - INFO - Successfully applied formatting to document: VPN - Proxy - Firewall
2025-07-23 18:53:12,978 - INFO - Successfully processed Google Doc: VPN - Proxy - Firewall
2025-07-23 18:53:12,979 - INFO - Syncing note: Cách làm sạch và bảo quản boots.md
2025-07-23 18:53:12,980 - INFO - Found embedded image: Untitled 16.png
2025-07-23 18:53:12,981 - INFO - Found embedded image: Untitled 1 9.png
2025-07-23 18:53:12,981 - INFO - Found embedded image: Untitled 2 6.png
2025-07-23 18:53:13,288 - INFO - Cleared content from document: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg
2025-07-23 18:53:13,289 - INFO - Using existing Google Doc: Java Spring (ID: 1wcTjyl-UGQEM2IAp6lHrsVdzLWK328YH4_f6ck1-Ayg)
2025-07-23 18:53:13,289 - INFO - Applying 28 formatting requests...
2025-07-23 18:53:13,289 - INFO - Validated 28/28 requests
2025-07-23 18:53:13,289 - INFO - Validated 28/28 requests
2025-07-23 18:53:13,459 - INFO - Found existing document: Cách làm sạch và bảo quản boots (ID: 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI)
2025-07-23 18:53:13,460 - INFO - Document Cách làm sạch và bảo quản boots already exists, updating content...
2025-07-23 18:53:13,924 - INFO - Successfully applied formatting to document: Java Spring
2025-07-23 18:53:13,924 - INFO - Successfully processed Google Doc: Java Spring
2025-07-23 18:53:13,925 - INFO - Syncing note: Chiến lược backup dữ liệu 3-2-1.md
2025-07-23 18:53:14,455 - INFO - Found existing document: Chiến lược backup dữ liệu 3-2-1 (ID: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ)
2025-07-23 18:53:14,455 - INFO - Document Chiến lược backup dữ liệu 3-2-1 already exists, updating content...
2025-07-23 18:53:14,760 - ERROR - Error clearing document content 11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI: <HttpError 400 when requesting https://docs.googleapis.com/v1/documents/11KpYOmykbk8_N7-CmItG8rd-2E6ZqOyW3o1Qp7GbZMI:batchUpdate?alt=json returned "Invalid requests[0].deleteContentRange: The range should not be empty.". Details: "Invalid requests[0].deleteContentRange: The range should not be empty.">
2025-07-23 18:53:14,761 - WARNING - Failed to update Cách làm sạch và bảo quản boots, creating new document...
2025-07-23 18:53:15,890 - INFO - Cleared content from document: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ
2025-07-23 18:53:15,891 - INFO - Using existing Google Doc: Chiến lược backup dữ liệu 3-2-1 (ID: 1SFpQerAmV_BeZRVe64Jnzs3F2WAkXWAF_qK0rs2WvUQ)
2025-07-23 18:53:15,891 - INFO - Applying 38 formatting requests...
2025-07-23 18:53:15,912 - INFO - Validated 38/38 requests
2025-07-23 18:53:15,912 - INFO - Validated 38/38 requests
2025-07-23 18:53:15,996 - INFO - Created new Google Doc: Cách làm sạch và bảo quản boots (ID: 1e16ZvTbLSiTwit7w0yAyxq9gDVdNOqqkmspsQ7WjT9M)
2025-07-23 18:53:16,549 - INFO - Successfully applied formatting to document: Chiến lược backup dữ liệu 3-2-1
2025-07-23 18:53:16,549 - INFO - Successfully processed Google Doc: Chiến lược backup dữ liệu 3-2-1
2025-07-23 18:53:16,549 - INFO - Syncing note: Những thứ đã học ở Grab tech talk.md
2025-07-23 18:53:17,058 - INFO - Found existing document: Những thứ đã học ở Grab tech talk (ID: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0)
2025-07-23 18:53:17,058 - INFO - Document Những thứ đã học ở Grab tech talk already exists, updating content...
2025-07-23 18:53:18,060 - INFO - Moved document to folder: 1t6AI19Oj4rJhWFGPekxYkyvKzdCzZvze
2025-07-23 18:53:18,060 - INFO - Applying 24 formatting requests...
2025-07-23 18:53:18,089 - INFO - Validated 24/24 requests
2025-07-23 18:53:18,089 - INFO - Validated 24/24 requests
2025-07-23 18:53:18,474 - INFO - Cleared content from document: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0
2025-07-23 18:53:18,475 - INFO - Using existing Google Doc: Những thứ đã học ở Grab tech talk (ID: 1afQagzQSXBGi0rCZBAiMaiksWzVh6qdCRZX-YdRj2G0)
2025-07-23 18:53:18,475 - INFO - Applying 45 formatting requests...
2025-07-23 18:53:18,484 - INFO - Validated 45/45 requests
2025-07-23 18:53:18,484 - INFO - Validated 45/45 requests
2025-07-23 18:53:19,523 - INFO - Successfully applied formatting to document: Cách làm sạch và bảo quản boots
2025-07-23 18:53:19,524 - INFO - Successfully processed Google Doc: Cách làm sạch và bảo quản boots
2025-07-23 18:53:19,532 - INFO - Recreating document with placeholders removed...
2025-07-23 18:53:19,649 - INFO - Successfully applied formatting to document: Những thứ đã học ở Grab tech talk
2025-07-23 18:53:19,657 - INFO - Successfully processed Google Doc: Những thứ đã học ở Grab tech talk
2025-07-23 18:53:19,659 - INFO - Syncing note: English with LLM - Cấu trúc câu phức tạp.md
2025-07-23 18:53:20,150 - INFO - Found existing document: English with LLM - Cấu trúc câu phức tạp (ID: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU)
2025-07-23 18:53:20,150 - INFO - Document English with LLM - Cấu trúc câu phức tạp already exists, updating content...
2025-07-23 18:53:21,172 - INFO - Validated 21/21 requests
2025-07-23 18:53:21,634 - INFO - Updated document content with 21 requests
2025-07-23 18:53:21,636 - INFO - Processing image: Untitled 2 6.png at position 1303
2025-07-23 18:53:21,841 - INFO - Cleared content from document: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU
2025-07-23 18:53:21,842 - INFO - Using existing Google Doc: English with LLM - Cấu trúc câu phức tạp (ID: 1mkvWK-mJuqkyBMYt83qQD-ts2oSTf7JS3Znb4qnpyRU)
2025-07-23 18:53:21,843 - INFO - Applying 44 formatting requests...
2025-07-23 18:53:21,851 - INFO - Validated 44/44 requests
2025-07-23 18:53:21,852 - INFO - Validated 44/44 requests
2025-07-23 18:53:22,136 - INFO - Found existing file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 18:53:22,151 - INFO - File Untitled 2 6.png already exists, updating...
2025-07-23 18:53:22,645 - INFO - Successfully applied formatting to document: English with LLM - Cấu trúc câu phức tạp
2025-07-23 18:53:22,649 - INFO - Successfully processed Google Doc: English with LLM - Cấu trúc câu phức tạp
2025-07-23 18:53:22,654 - INFO - Syncing note: viclass - 752 - Make it easier to create account for user to experience the beta system.md
2025-07-23 18:53:23,444 - INFO - Found existing document: viclass - 752 - Make it easier to create account for user to experience the beta system (ID: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4)
2025-07-23 18:53:23,447 - INFO - Document viclass - 752 - Make it easier to create account for user to experience the beta system already exists, updating content...
2025-07-23 18:53:25,205 - INFO - Cleared content from document: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4
2025-07-23 18:53:25,206 - INFO - Using existing Google Doc: viclass - 752 - Make it easier to create account for user to experience the beta system (ID: 1mu-UGPEJ1JjNpqP8qpiu8dpBc13B7Er4d64YnCL7hT4)
2025-07-23 18:53:25,206 - INFO - Applying 66 formatting requests...
2025-07-23 18:53:25,207 - INFO - Validated 66/66 requests
2025-07-23 18:53:25,207 - INFO - Validated 66/66 requests
2025-07-23 18:53:25,830 - INFO - Successfully applied formatting to document: viclass - 752 - Make it easier to create account for user to experience the beta system
2025-07-23 18:53:25,830 - INFO - Successfully processed Google Doc: viclass - 752 - Make it easier to create account for user to experience the beta system
2025-07-23 18:53:25,831 - INFO - Syncing note: English with LLM - Loại câu.md
2025-07-23 18:53:26,435 - INFO - Found existing document: English with LLM - Loại câu (ID: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q)
2025-07-23 18:53:26,435 - INFO - Document English with LLM - Loại câu already exists, updating content...
2025-07-23 18:53:27,883 - INFO - Cleared content from document: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q
2025-07-23 18:53:27,884 - INFO - Using existing Google Doc: English with LLM - Loại câu (ID: 1_csqteX6VAJEHepV_5dPTAHANrUJJDpJHXi8CUrXh-Q)
2025-07-23 18:53:27,884 - INFO - Applying 137 formatting requests...
2025-07-23 18:53:27,895 - INFO - Validated 137/137 requests
2025-07-23 18:53:27,895 - INFO - Validated 137/137 requests
2025-07-23 18:53:27,919 - INFO - Updated existing file: Untitled 2 6.png (ID: 13FDZA05x6LZFPXoCWmn7s0_MCZ9Dubzj)
2025-07-23 18:53:28,732 - INFO - Successfully applied formatting to document: English with LLM - Loại câu
2025-07-23 18:53:28,732 - INFO - Successfully processed Google Doc: English with LLM - Loại câu
2025-07-23 18:53:28,733 - INFO - Syncing note: Các loại trang phục cho tủ đồ.md
2025-07-23 18:53:29,169 - INFO - Found existing document: Các loại trang phục cho tủ đồ (ID: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c)
2025-07-23 18:53:29,169 - INFO - Document Các loại trang phục cho tủ đồ already exists, updating content...
2025-07-23 18:53:30,756 - INFO - Cleared content from document: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c
2025-07-23 18:53:30,756 - INFO - Using existing Google Doc: Các loại trang phục cho tủ đồ (ID: 17dsgCtST_6g0rIh-YKJUEZOTv8OQnEokLhn5fkNLu7c)
2025-07-23 18:53:30,758 - INFO - Applying 9 formatting requests...
2025-07-23 18:53:30,759 - INFO - Validated 9/9 requests
2025-07-23 18:53:30,759 - INFO - Validated 9/9 requests
