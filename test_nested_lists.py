#!/usr/bin/env python3
"""
Test script to analyze nested list processing issues
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
    import markdown
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_nested_list_html_structure():
    """Test how markdown converts nested lists to HTML"""
    
    print("Testing nested list HTML structure...")
    print("=" * 50)
    
    # Test cases with different nested list structures
    test_cases = [
        {
            'name': 'Simple nested list',
            'markdown': """- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2
  - Nested item 2.1
    - Deep nested 2.1.1
    - Deep nested 2.1.2
  - Nested item 2.2
- Item 3"""
        },
        {
            'name': 'Mixed ordered/unordered',
            'markdown': """1. First item
   - Sub item A
   - Sub item B
2. Second item
   1. Sub item 1
   2. Sub item 2
      - Deep sub A
      - Deep sub B
3. Third item"""
        },
        {
            'name': 'Complex nesting',
            'markdown': """- Level 1 item 1
  - Level 2 item 1.1
    - Level 3 item 1.1.1
    - Level 3 item 1.1.2
  - Level 2 item 1.2
- Level 1 item 2
  - Level 2 item 2.1"""
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        markdown_content = test_case['markdown']
        
        # Convert to HTML
        html = markdown.markdown(markdown_content, extensions=['tables', 'fenced_code'])
        soup = BeautifulSoup(html, 'html.parser')
        
        print("Markdown:")
        for j, line in enumerate(markdown_content.split('\n'), 1):
            print(f"  {j:2d}: {line}")
        
        print(f"\nHTML structure:")
        print(f"  {html}")
        
        print(f"\nParsed structure:")
        for element in soup.find_all(['ul', 'ol']):
            print(f"  {element.name} element:")
            for li in element.find_all('li', recursive=False):  # Only direct children
                print(f"    - li: {repr(li.get_text()[:50])}")
                # Check for nested lists
                nested = li.find_all(['ul', 'ol'])
                if nested:
                    print(f"      Has {len(nested)} nested list(s)")

def test_current_process_list_method():
    """Test the current process_list method with nested lists"""
    
    print(f"\n" + "=" * 50)
    print("Testing current process_list method...")
    print("=" * 50)
    
    # Create a mock sync tool
    class MockSync:
        def process_list(self, element, start_index: int):
            # Copy of current implementation
            requests = []
            total_length = 0

            for li in element.find_all('li'):  # This is the problem!
                text = li.get_text() + '\n'
                text_length = grapheme_len(text)
                requests.append({
                    'insertText': {
                        'location': {'index': start_index + total_length},
                        'text': text
                    }
                })

                # Apply bullet list formatting
                requests.append({
                    'createParagraphBullets': {
                        'range': {
                            'startIndex': start_index + total_length,
                            'endIndex': start_index + total_length + text_length - 1
                        },
                        'bulletPreset': 'BULLET_DISC_CIRCLE_SQUARE'
                    }
                })

                total_length += text_length

            return requests, total_length
    
    sync_tool = MockSync()
    
    # Test with nested list
    test_markdown = """- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2
  - Nested item 2.1"""
    
    html = markdown.markdown(test_markdown, extensions=['tables', 'fenced_code'])
    soup = BeautifulSoup(html, 'html.parser')
    
    print("Test markdown:")
    for i, line in enumerate(test_markdown.split('\n'), 1):
        print(f"  {i}: {line}")
    
    print(f"\nHTML: {html}")
    
    # Find the main list
    main_list = soup.find(['ul', 'ol'])
    if main_list:
        print(f"\nProcessing main {main_list.name} list...")
        requests, total_length = sync_tool.process_list(main_list, 1)
        
        print(f"Generated {len(requests)} requests:")
        insert_requests = [r for r in requests if 'insertText' in r]
        
        for i, req in enumerate(insert_requests):
            text = req['insertText']['text'].strip()
            index = req['insertText']['location']['index']
            print(f"  {i+1}: Index {index} -> '{text}'")
        
        print(f"\nProblem: All nested items are processed as top-level!")
        
        # Show what should happen
        print(f"\nWhat should happen:")
        print(f"  1: Index 1 -> 'Item 1' (level 1)")
        print(f"  2: Index X -> 'Nested item 1.1' (level 2)")
        print(f"  3: Index Y -> 'Nested item 1.2' (level 2)")
        print(f"  4: Index Z -> 'Item 2' (level 1)")
        print(f"  5: Index W -> 'Nested item 2.1' (level 2)")

def analyze_nested_structure():
    """Analyze how we should handle nested structure"""
    
    print(f"\n" + "=" * 50)
    print("Analyzing proper nested list handling...")
    print("=" * 50)
    
    test_markdown = """- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2"""
    
    html = markdown.markdown(test_markdown, extensions=['tables', 'fenced_code'])
    soup = BeautifulSoup(html, 'html.parser')
    
    print("HTML structure analysis:")
    print(html)
    
    def analyze_list_recursive(element, level=0):
        """Recursively analyze list structure"""
        indent = "  " * level
        print(f"{indent}{element.name} (level {level}):")
        
        # Process direct children only
        for child in element.children:
            if hasattr(child, 'name'):
                if child.name == 'li':
                    # Get text content (excluding nested lists)
                    text_parts = []
                    for content in child.children:
                        if hasattr(content, 'name'):
                            if content.name in ['ul', 'ol']:
                                # This is a nested list, handle separately
                                continue
                            else:
                                text_parts.append(content.get_text())
                        else:
                            # Text node
                            text_parts.append(str(content).strip())
                    
                    item_text = ''.join(text_parts).strip()
                    print(f"{indent}  li: '{item_text}'")
                    
                    # Check for nested lists
                    nested_lists = child.find_all(['ul', 'ol'], recursive=False)
                    for nested in nested_lists:
                        analyze_list_recursive(nested, level + 1)
    
    main_list = soup.find(['ul', 'ol'])
    if main_list:
        analyze_list_recursive(main_list)

if __name__ == "__main__":
    print("Testing Nested List Processing")
    print("=" * 60)
    
    test_nested_list_html_structure()
    test_current_process_list_method()
    analyze_nested_structure()
    
    print(f"\n" + "=" * 60)
    print("CONCLUSION:")
    print("The current process_list method has these issues:")
    print("1. Uses find_all('li') which gets ALL li elements, including nested ones")
    print("2. Doesn't handle nesting levels properly")
    print("3. Doesn't separate item text from nested list content")
    print("4. All items get the same bullet formatting")
    print("\nWe need to rewrite process_list to handle nested structure properly!")
