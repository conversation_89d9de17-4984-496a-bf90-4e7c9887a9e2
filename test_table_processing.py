#!/usr/bin/env python3
"""
Test script to verify table processing fix
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
    import markdown
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_table_extraction():
    """Test extracting table data from HTML"""
    
    print("Testing table data extraction...")
    print("=" * 50)
    
    # Create a mock sync tool
    class MockSync:
        def extract_table_data(self, table_element):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.extract_table_data(table_element)
    
    sync_tool = MockSync()
    
    # Test cases with different table structures
    test_cases = [
        {
            'name': 'Simple table',
            'markdown': """| Name | Age | City |
|------|-----|------|
| John | 25  | NYC  |
| Jane | 30  | LA   |"""
        },
        {
            'name': 'Table with Vietnamese text',
            'markdown': """| Tên | Tuổi | Thành phố |
|-----|------|-----------|
| Nguyễn Văn A | 25 | Hà Nội |
| Trần Thị B | 30 | TP.HCM |"""
        },
        {
            'name': 'Table with empty cells',
            'markdown': """| Feature | Status | Notes |
|---------|--------|-------|
| Login | Done | |
| Logout | | In progress |
| Profile | Done | Needs testing |"""
        },
        {
            'name': 'Complex table',
            'markdown': """| ID | Name | Description | Priority | Assignee |
|----|------|-------------|----------|----------|
| 1 | Fix bug | Critical issue with login | High | John |
| 2 | Add feature | New user dashboard | Medium | Jane |
| 3 | Update docs | API documentation | Low | |"""
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        markdown_content = test_case['markdown']
        
        # Convert to HTML
        html = markdown.markdown(markdown_content, extensions=['tables'])
        soup = BeautifulSoup(html, 'html.parser')
        
        print("Markdown:")
        for j, line in enumerate(markdown_content.split('\n'), 1):
            print(f"  {j:2d}: {line}")
        
        print(f"\nHTML: {html}")
        
        # Find table element
        table_element = soup.find('table')
        if table_element:
            table_data = sync_tool.extract_table_data(table_element)
            
            print(f"\nExtracted table data ({len(table_data)} rows):")
            for row_idx, row_data in enumerate(table_data):
                print(f"  Row {row_idx}: {row_data}")
        else:
            print("\nNo table found in HTML")

def test_table_formatting():
    """Test table formatting with fallback method"""
    
    print(f"\n" + "=" * 50)
    print("Testing table formatting...")
    print("=" * 50)
    
    class MockSync:
        def create_simple_table_fallback(self, table_data, start_index):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.create_simple_table_fallback(table_data, start_index)
    
    sync_tool = MockSync()
    
    # Test with different table data
    test_tables = [
        {
            'name': 'Simple 2x3 table',
            'data': [
                ['Name', 'Age', 'City'],
                ['John', '25', 'NYC'],
                ['Jane', '30', 'LA']
            ]
        },
        {
            'name': 'Vietnamese table',
            'data': [
                ['Tên', 'Tuổi', 'Thành phố'],
                ['Nguyễn Văn A', '25', 'Hà Nội'],
                ['Trần Thị B', '30', 'TP.HCM']
            ]
        },
        {
            'name': 'Uneven table',
            'data': [
                ['Feature', 'Status', 'Notes'],
                ['Login', 'Done'],
                ['Logout', '', 'In progress'],
                ['Profile', 'Done', 'Needs testing', 'Extra column']
            ]
        }
    ]
    
    for test_table in test_tables:
        print(f"\n{test_table['name']}:")
        print("-" * 30)
        
        print("Input data:")
        for i, row in enumerate(test_table['data']):
            print(f"  Row {i}: {row}")
        
        try:
            requests, length = sync_tool.create_simple_table_fallback(test_table['data'], 1)
            
            print(f"\nGenerated {len(requests)} requests, total length: {length}")
            
            if requests:
                text_content = requests[0]['insertText']['text']
                print("Formatted table:")
                for i, line in enumerate(text_content.split('\n'), 1):
                    if line.strip():
                        print(f"  {i:2d}: {line}")
            
        except Exception as e:
            print(f"Error formatting table: {e}")
            import traceback
            traceback.print_exc()

def test_complete_table_processing():
    """Test complete table processing workflow"""
    
    print(f"\n" + "=" * 50)
    print("Testing complete table processing...")
    print("=" * 50)
    
    class MockSync:
        def process_table(self, element, start_index):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.process_table(element, start_index)
        
        def extract_table_data(self, table_element):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.extract_table_data(table_element)
        
        def create_simple_table_fallback(self, table_data, start_index):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.create_simple_table_fallback(table_data, start_index)
    
    sync_tool = MockSync()
    
    # Test with a complete markdown document containing tables
    test_content = """# Document with Tables

Here's a simple table:

| Name | Role | Experience |
|------|------|------------|
| John | Developer | 5 years |
| Jane | Designer | 3 years |
| Bob | Manager | 10 years |

And here's another table with Vietnamese content:

| Tên sản phẩm | Giá | Trạng thái |
|--------------|-----|------------|
| Laptop Dell | 15,000,000 VND | Còn hàng |
| iPhone 15 | 25,000,000 VND | Hết hàng |

End of document."""
    
    print("Test content:")
    for i, line in enumerate(test_content.split('\n'), 1):
        print(f"  {i:2d}: {line}")
    
    # Convert to HTML and find tables
    html = markdown.markdown(test_content, extensions=['tables'])
    soup = BeautifulSoup(html, 'html.parser')
    
    tables = soup.find_all('table')
    print(f"\nFound {len(tables)} tables in the document")
    
    for i, table in enumerate(tables):
        print(f"\nProcessing table {i+1}:")
        
        try:
            requests, length = sync_tool.process_table(table, 1)
            
            print(f"Generated {len(requests)} requests, total length: {length}")
            
            if requests and 'insertText' in requests[0]:
                table_text = requests[0]['insertText']['text']
                print("Table output preview:")
                lines = table_text.split('\n')
                for j, line in enumerate(lines[:10], 1):  # Show first 10 lines
                    if line.strip():
                        print(f"  {j:2d}: {line}")
                if len(lines) > 10:
                    print(f"  ... ({len(lines) - 10} more lines)")
            
            return True
            
        except Exception as e:
            print(f"Error processing table {i+1}: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("Testing Table Processing Fix")
    print("=" * 60)
    
    test_table_extraction()
    test_table_formatting()
    success = test_complete_table_processing()
    
    if success:
        print("\n✅ Table processing fix is working!")
        print("Tables should now be properly formatted in Google Docs.")
    else:
        print("\n❌ There are still issues with table processing.")
        print("Check the error messages above for details.")
