#!/usr/bin/env python3
"""
Test script to verify nested list fix
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len
    import markdown
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_nested_list_preprocessing():
    """Test the nested list preprocessing"""
    
    print("Testing nested list preprocessing...")
    print("=" * 50)
    
    # Create a mock sync tool for testing
    class MockSync:
        def preprocess_nested_lists(self, content):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.preprocess_nested_lists(content)
        
        def is_list_item(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.is_list_item(line)
        
        def get_list_indent_level(self, line):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_list_indent_level(line)
        
        def process_list_block(self, lines, start_index):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.process_list_block(lines, start_index)
    
    sync_tool = MockSync()
    
    # Test cases
    test_cases = [
        {
            'name': 'Simple nested list',
            'markdown': """- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2
  - Nested item 2.1"""
        },
        {
            'name': 'Mixed ordered/unordered',
            'markdown': """1. First item
   - Sub item A
   - Sub item B
2. Second item
   1. Sub item 1
   2. Sub item 2"""
        },
        {
            'name': 'Deep nesting',
            'markdown': """- Level 1
  - Level 2
    - Level 3
      - Level 4
    - Level 3 again
  - Level 2 again
- Level 1 again"""
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        original = test_case['markdown']
        processed = sync_tool.preprocess_nested_lists(original)
        
        print("Original:")
        for j, line in enumerate(original.split('\n'), 1):
            indent_level = sync_tool.get_list_indent_level(line) if sync_tool.is_list_item(line) else -1
            is_list = sync_tool.is_list_item(line)
            print(f"  {j:2d}: {line} {'(list, level ' + str(indent_level//2) + ')' if is_list else ''}")
        
        print("Processed:")
        for j, line in enumerate(processed.split('\n'), 1):
            indent_level = sync_tool.get_list_indent_level(line) if sync_tool.is_list_item(line) else -1
            is_list = sync_tool.is_list_item(line)
            print(f"  {j:2d}: {line} {'(list, level ' + str(indent_level//2) + ')' if is_list else ''}")
        
        # Test HTML conversion
        html = markdown.markdown(processed, extensions=['tables', 'fenced_code'])
        print(f"HTML result: {html[:100]}...")

def test_new_process_list_method():
    """Test the new process_list method"""
    
    print(f"\n" + "=" * 50)
    print("Testing new process_list method...")
    print("=" * 50)
    
    # Create a mock sync tool
    class MockSync:
        def process_list(self, element, start_index, level=0):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.process_list(element, start_index, level)
        
        def extract_list_item_text(self, li_element):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.extract_list_item_text(li_element)
        
        def get_bullet_preset_for_level(self, level):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_bullet_preset_for_level(level)
    
    sync_tool = MockSync()
    
    # Test with a simple nested list
    test_markdown = """- Item 1
  - Nested item 1.1
  - Nested item 1.2
- Item 2"""
    
    html = markdown.markdown(test_markdown, extensions=['tables', 'fenced_code'])
    soup = BeautifulSoup(html, 'html.parser')
    
    print("Test markdown:")
    for i, line in enumerate(test_markdown.split('\n'), 1):
        print(f"  {i}: {line}")
    
    print(f"\nHTML: {html}")
    
    # Find the main list
    main_list = soup.find(['ul', 'ol'])
    if main_list:
        print(f"\nProcessing main {main_list.name} list...")
        
        try:
            requests, total_length = sync_tool.process_list(main_list, 1, 0)
            
            print(f"Generated {len(requests)} requests:")
            insert_requests = [r for r in requests if 'insertText' in r]
            bullet_requests = [r for r in requests if 'createParagraphBullets' in r]
            
            print(f"Insert requests: {len(insert_requests)}")
            for i, req in enumerate(insert_requests):
                text = req['insertText']['text'].strip()
                index = req['insertText']['location']['index']
                print(f"  {i+1}: Index {index} -> '{text}'")
            
            print(f"Bullet requests: {len(bullet_requests)}")
            for i, req in enumerate(bullet_requests):
                preset = req['createParagraphBullets']['bulletPreset']
                range_info = req['createParagraphBullets']['range']
                print(f"  {i+1}: {preset} for range {range_info['startIndex']}-{range_info['endIndex']}")
            
            return True
            
        except Exception as e:
            print(f"Error processing list: {e}")
            import traceback
            traceback.print_exc()
            return False
    else:
        print("No list found in HTML")
        return False

def test_bullet_presets():
    """Test bullet preset selection for different levels"""
    
    print(f"\n" + "=" * 50)
    print("Testing bullet presets for different levels...")
    print("=" * 50)
    
    class MockSync:
        def get_bullet_preset_for_level(self, level):
            from obsidian_to_google_sync import ObsidianToGoogleSync
            real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
            return real_sync.get_bullet_preset_for_level(level)
    
    sync_tool = MockSync()
    
    print("Bullet presets by level:")
    for level in range(6):
        preset = sync_tool.get_bullet_preset_for_level(level)
        print(f"  Level {level}: {preset}")

if __name__ == "__main__":
    print("Testing Nested List Fix")
    print("=" * 60)
    
    test_bullet_presets()
    test_nested_list_preprocessing()
    success = test_new_process_list_method()
    
    if success:
        print("\n✅ Nested list fix appears to be working!")
        print("Lists should now be processed with proper nesting levels.")
    else:
        print("\n❌ There are still issues with the nested list fix.")
        print("Check the error messages above for details.")
