#!/usr/bin/env python3
"""
Debug script for integration test failure
"""

import sys
import os
from pathlib import Path
import tempfile

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync, grapheme_len, clean_whitespace_lines
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)

def debug_integration_issue():
    """Debug why integration test is failing"""
    
    test_content = """# Test Document

This paragraph comes before the first image.

![[test_image1.png]]

This text appears between images.

![[test_image2.jpg]]

This is the final paragraph after all images."""
    
    print("Debugging Integration Test Issue")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        vault_path = Path(temp_dir) / "test_vault"
        vault_path.mkdir()
        
        # Create test images
        (vault_path / "test_image1.png").write_bytes(b"fake_png_data")
        (vault_path / "test_image2.jpg").write_bytes(b"fake_jpg_data")
        
        # Create test note
        note_path = vault_path / "test_note.md"
        note_path.write_text(test_content, encoding='utf-8')
        
        try:
            class MockSync:
                def __init__(self):
                    self.obsidian_path = vault_path
                
                def process_images_in_markdown(self, content, note_path):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    real_sync.obsidian_path = vault_path
                    return real_sync.process_images_in_markdown(content, note_path)
                
                def find_media_file(self, base_path, image_name):
                    return base_path / image_name
                
                def convert_markdown_to_docs_requests(self, content, note_path):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.convert_markdown_to_docs_requests(content, note_path)
                
                def calculate_image_positions_in_doc(self, content_requests, image_positions):
                    from obsidian_to_google_sync import ObsidianToGoogleSync
                    real_sync = ObsidianToGoogleSync.__new__(ObsidianToGoogleSync)
                    return real_sync.calculate_image_positions_in_doc(content_requests, image_positions)
            
            sync_tool = MockSync()
            
            print("Step 1: Process images in markdown")
            processed_content, image_positions = sync_tool.process_images_in_markdown(test_content, note_path)
            
            print(f"Original content:")
            for i, line in enumerate(test_content.split('\n'), 1):
                print(f"  {i:2d}: {line}")
            
            print(f"\nProcessed content:")
            for i, line in enumerate(processed_content.split('\n'), 1):
                if 'IMAGE_PLACEHOLDER_' in line:
                    print(f"  {i:2d}: >>> {line} <<<")
                else:
                    print(f"  {i:2d}: {line}")
            
            print(f"\nImage positions: {len(image_positions)}")
            for name, path, placeholder_id in image_positions:
                print(f"  {name} -> {placeholder_id}")
            
            print(f"\nStep 2: Convert to Google Docs requests")
            requests = sync_tool.convert_markdown_to_docs_requests(processed_content, note_path)
            
            print(f"Generated {len(requests)} requests")
            
            # Check for placeholders in requests
            insert_requests = [r for r in requests if 'insertText' in r]
            print(f"Insert text requests: {len(insert_requests)}")
            
            all_text = ""
            for i, req in enumerate(insert_requests):
                text = req['insertText']['text']
                all_text += text
                if 'IMAGE_PLACEHOLDER_' in text:
                    print(f"  Request {i+1} contains placeholder: {repr(text[:100])}...")
            
            has_placeholders = 'IMAGE_PLACEHOLDER_' in all_text
            print(f"\nContains placeholders after conversion: {'YES' if has_placeholders else 'NO'}")
            
            if has_placeholders:
                print("\nStep 3: Test placeholder removal")
                try:
                    image_positions_with_indices, updated_requests = sync_tool.calculate_image_positions_in_doc(
                        requests, image_positions
                    )
                    
                    print(f"Image positions calculated: {len(image_positions_with_indices)}")
                    for name, path, index in image_positions_with_indices:
                        print(f"  {name} at index {index}")
                    
                    # Check updated requests
                    updated_insert_requests = [r for r in updated_requests if 'insertText' in r]
                    updated_all_text = ""
                    for req in updated_insert_requests:
                        updated_all_text += req['insertText']['text']
                    
                    updated_has_placeholders = 'IMAGE_PLACEHOLDER_' in updated_all_text
                    print(f"Contains placeholders after removal: {'YES' if updated_has_placeholders else 'NO'}")
                    
                    if updated_has_placeholders:
                        print("Still has placeholders! Let's see what's left:")
                        for i, req in enumerate(updated_insert_requests):
                            text = req['insertText']['text']
                            if 'IMAGE_PLACEHOLDER_' in text:
                                print(f"  Request {i+1}: {repr(text[:100])}...")
                    
                except Exception as e:
                    print(f"Error in placeholder removal: {e}")
                    import traceback
                    traceback.print_exc()
            
        except Exception as e:
            print(f"Error in debug: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_integration_issue()
