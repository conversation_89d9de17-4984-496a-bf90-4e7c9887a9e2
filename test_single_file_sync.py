#!/usr/bin/env python3
"""
Test script to sync only the problematic Vietnamese file to verify the fix
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import ObsidianToGoogleSync
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_single_file_sync():
    """Test syncing only the problematic file"""
    
    # Check if credentials exist
    credentials_path = "credentials.json"
    if not Path(credentials_path).exists():
        print(f"Credentials file not found: {credentials_path}")
        print("Please ensure Google API credentials are available")
        return False
    
    # Initialize the sync tool
    try:
        sync_tool = ObsidianToGoogleSync("obsidian", credentials_path)
        
        # Authenticate
        if not sync_tool.authenticate():
            print("Authentication failed")
            return False
        
        print("✅ Authentication successful")
        
        # Setup folder structure
        root_folder_id = sync_tool.setup_folder_structure("Obsidian Sync Test")
        if not root_folder_id:
            print("Failed to setup folder structure")
            return False
        
        print("✅ Folder structure setup successful")
        
        # Test with the specific problematic file
        test_file = Path("obsidian/Top 10 câu hỏi phỏng vấn System Design và Microservices.md")
        
        if not test_file.exists():
            print(f"Test file not found: {test_file}")
            return False
        
        print(f"📄 Testing sync of: {test_file.name}")
        
        # Sync the single file
        success = sync_tool.sync_single_note(test_file, root_folder_id)
        
        if success:
            print("✅ File sync successful!")
            print("The grapheme cluster fix appears to be working correctly.")
            return True
        else:
            print("❌ File sync failed")
            print("Check the logs for details")
            return False
            
    except Exception as e:
        print(f"Error during sync test: {e}")
        return False

if __name__ == "__main__":
    print("Testing single file sync with grapheme cluster fix")
    print("=" * 60)
    print()
    
    print("⚠️  This will create a test document in Google Drive")
    print("⚠️  Make sure you want to proceed")
    print()
    
    response = input("Continue? (y/N): ").strip().lower()
    if response != 'y':
        print("Test cancelled")
        sys.exit(0)
    
    print()
    success = test_single_file_sync()
    
    if success:
        print("\n🎉 SUCCESS: The grapheme cluster fix is working!")
        print("Vietnamese text can now be synced to Google Docs without errors.")
    else:
        print("\n💥 FAILED: There may still be issues with the fix.")
        print("Check the error logs for more details.")
