#!/usr/bin/env python3
"""
Test script to verify whitespace cleanup functionality
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

try:
    from obsidian_to_google_sync import clean_whitespace_lines
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the sync directory")
    sys.exit(1)

def test_whitespace_cleanup():
    """Test the whitespace cleanup function"""
    
    print("Testing whitespace cleanup functionality...")
    print("=" * 50)
    
    # Test cases with different types of whitespace
    test_cases = [
        {
            'name': 'Mixed whitespace lines',
            'input': """# Header

This is a normal line.
   
\t\t
    \t  
Another normal line.

\t   \t
Final line.""",
            'description': 'Lines with spaces, tabs, and mixed whitespace'
        },
        {
            'name': 'Vietnamese content with whitespace',
            'input': """# Tiêu đề

Đây là đoạn văn tiếng <PERSON>.
   
\t\t
Đây là đoạn văn kh<PERSON>.
    \t  
Kết thúc.""",
            'description': 'Vietnamese text with whitespace-only lines'
        },
        {
            'name': 'Code blocks with whitespace',
            'input': """```python
def hello():
    print("Hello")
   
\t
```

Normal text here.
    
End.""",
            'description': 'Code blocks with whitespace lines'
        },
        {
            'name': 'Only whitespace lines',
            'input': """   
\t\t
    \t  
\t   \t""",
            'description': 'Content with only whitespace lines'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Description: {test_case['description']}")
        
        input_content = test_case['input']
        cleaned_content = clean_whitespace_lines(input_content)
        
        # Show the differences
        input_lines = input_content.split('\n')
        cleaned_lines = cleaned_content.split('\n')
        
        print(f"   Input lines: {len(input_lines)}")
        print(f"   Cleaned lines: {len(cleaned_lines)}")
        
        # Show line-by-line comparison for first few lines
        print(f"   Line-by-line comparison:")
        for j, (orig, clean) in enumerate(zip(input_lines, cleaned_lines)):
            if orig != clean:
                print(f"     Line {j+1:2d}: '{orig}' -> '{clean}'")
                print(f"              Original repr: {repr(orig)}")
                print(f"              Cleaned repr:  {repr(clean)}")
            elif orig.strip() == '':
                print(f"     Line {j+1:2d}: (empty/whitespace) -> '{clean}'")
        
        # Count whitespace-only lines
        orig_whitespace_lines = sum(1 for line in input_lines if line.strip() == '')
        cleaned_whitespace_lines = sum(1 for line in cleaned_lines if line.strip() == '')
        
        print(f"   Whitespace-only lines: {orig_whitespace_lines} -> {cleaned_whitespace_lines}")
        
        # Verify all whitespace-only lines are now empty
        all_clean = all(line == '' for line in cleaned_lines if line.strip() == '')
        print(f"   All whitespace lines cleaned: {'✅' if all_clean else '❌'}")

def test_with_real_file():
    """Test with a real markdown file"""
    
    print("\n" + "=" * 50)
    print("Testing with real markdown file...")
    print("=" * 50)
    
    # Create a test file with whitespace issues
    test_content = """# Test Document

This is the first paragraph.
   
\t\t
## Section 2

This paragraph has content.
    \t  
Another paragraph here.

\t   \t

### Subsection

Final content.
   """
    
    test_file = Path("test_whitespace.md")
    
    try:
        # Write test file
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"Created test file: {test_file}")
        
        # Read and clean
        with open(test_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        cleaned_content = clean_whitespace_lines(original_content)
        
        # Show statistics
        orig_lines = original_content.split('\n')
        clean_lines = cleaned_content.split('\n')
        
        orig_whitespace = sum(1 for line in orig_lines if line.strip() == '' and line != '')
        clean_whitespace = sum(1 for line in clean_lines if line.strip() == '' and line != '')
        
        print(f"Original content:")
        print(f"  Total lines: {len(orig_lines)}")
        print(f"  Whitespace-only lines: {orig_whitespace}")
        print(f"  Empty lines: {sum(1 for line in orig_lines if line == '')}")
        
        print(f"Cleaned content:")
        print(f"  Total lines: {len(clean_lines)}")
        print(f"  Whitespace-only lines: {clean_whitespace}")
        print(f"  Empty lines: {sum(1 for line in clean_lines if line == '')}")
        
        # Write cleaned version
        cleaned_file = Path("test_whitespace_cleaned.md")
        with open(cleaned_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"Created cleaned file: {cleaned_file}")
        print(f"✅ Whitespace cleanup successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing with real file: {e}")
        return False
    
    finally:
        # Clean up test files
        for file in [test_file, Path("test_whitespace_cleaned.md")]:
            if file.exists():
                file.unlink()

if __name__ == "__main__":
    print("Testing Whitespace Cleanup Functionality")
    print("=" * 60)
    
    test_whitespace_cleanup()
    success = test_with_real_file()
    
    if success:
        print("\n🎉 All tests passed!")
        print("Whitespace-only lines will now be converted to empty lines.")
    else:
        print("\n💥 Some tests failed!")
        print("There may be issues with the whitespace cleanup logic.")
